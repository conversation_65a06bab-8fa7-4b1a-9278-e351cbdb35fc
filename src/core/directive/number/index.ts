import { Directive, DirectiveBinding } from "vue";
export const focus:Directive = {
  mounted(el: HTMLInputElement, binding: DirectiveBinding) {
    el.focus();
  }
};
export const number: Directive = {
  mounted(el: HTMLInputElement, binding: DirectiveBinding) {
    el.addEventListener('input', function(e) {
      // 获取输入值和光标位置
      let value = e.target.value;
      const position = e.target.selectionStart;

      // 保存原始值用于比较
      const originalValue = value;

      // 根据binding.value确定限制类型
      const type = binding.value;

      // 处理不同限制类型
      if (type === 'positiveInt') {
        // 正整数：只允许数字
        value = value.replace(/[^\d]/g, '');
        // 移除前导零（但保留单个零）
        value = value.replace(/^0+(\d)/, '$1');
      }
      else if (type === 'negativeInt') {
        // 负整数：允许数字和开头的负号
        // 只允许开头有一个负号
        value = value.replace(/(?!^)-/g, '');
        value = value.replace(/[^\d-]/g, '');
        value = value.replace(/-{2,}/g, '-');
      }
      else {
        // 带小数的数字：允许数字、负号和小数点
        // 只允许开头有一个负号
        value = value.replace(/(?!^)-/g, '');
        value = value.replace(/-{2,}/g, '-');
        value = value.replace(/[^\d.-]/g, '');

        // 处理多个小数点
        const parts = value.split('.');
        if (parts.length > 2) {
          value = parts[0] + '.' + parts.slice(1).join('');
        }

        // 限制小数位数
        if (value.includes('.')) {
          const [integer, decimal] = value.split('.');
          value = integer + '.' + (decimal || '').slice(0, 2);
        }
      }

      // 关键修复：仅当值实际改变时才更新
      if (value !== originalValue) {
        e.target.value = value;
        // 触发input事件更新Vue模型
        e.target.dispatchEvent(new Event('input'));
      }

      // 恢复光标位置
      e.target.setSelectionRange(position, position);
    });
  },
};