import { defineStore } from "pinia";
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import type { TagView } from "@/core/types/store";
import { useUserStore } from "@/core/store";

export const useTagsViewStore = defineStore("tagsView", () => {
  const visitedViews = ref<TagView[]>([]);
  const cachedViews = ref<string[]>([]);
  // 存储每个系统的tagsview状态
  const systemTagsViewMap = ref<{
    [key: string]: { visited: TagView[]; cached: string[]; currentRoute: object };
  }>({});
  const router = useRouter();
  const route = useRoute();
  const userStore = useUserStore();

  /**
   * 检查标签是否激活
   */
  function isActive(tag: TagView) {
    return tag.path === route.path;
  }

  /**
   * 保存当前系统的tagsview状态
   */
  function saveCurrentSystemState(systemId: string, lastSystemId: string) {
    // const systemId = userStore.systemId;
    if (lastSystemId) {
      console.log("lastSystemId---", lastSystemId, '--currentRoute---', route);
      console.log("systemTagsViewMap.value---", systemTagsViewMap.value);
      systemTagsViewMap.value[lastSystemId] = {
        visited: [...visitedViews.value],
        cached: [...cachedViews.value],
        currentRoute: JSON.parse(JSON.stringify(route)),
      };
    }
  }

  /**
   * 恢复指定系统的tagsview状态
   */
  function restoreSystemState(systemId: string, lastSystemId: string) {
    // 先保存当前系统tagviews状态
    // const currentSystemId = userStore.systemId;
    if (lastSystemId) {
      saveCurrentSystemState(systemId, lastSystemId);
    }

    // 恢复目标系统状态
    const savedState = systemTagsViewMap.value[systemId];
    console.log("savedState---", savedState);
    if (savedState) {
      visitedViews.value = [...savedState.visited];
      cachedViews.value = [...savedState.cached];
      const currentRoute = savedState.currentRoute;
      updateVisitedView({
        name: currentRoute.name as string,
        title: currentRoute.meta.title || "",
        path: currentRoute.path,
        fullPath: currentRoute.fullPath,
        affix: currentRoute.meta?.affix,
        keepAlive: currentRoute.meta?.keepAlive,
        query: currentRoute.query,
      });
      router.push({
        path: currentRoute.path,
        query: currentRoute.query,
      });
    } else {
      // 如果是首次进入该系统，只保留固定标签
      visitedViews.value = visitedViews.value.filter((view) => view.affix);
      cachedViews.value = [];
    }
  }

  function addVisitedView(view: TagView) {
    // 只添加到当前系统的tagsview中
    if (visitedViews.value.some((v) => v.path === view.path)) return;
    if (view.affix) {
      visitedViews.value.unshift(view);
    } else {
      visitedViews.value.push(view);
    }
    // 保存当前系统状态
    saveCurrentSystemState();
  }

  function addCachedView(view: TagView) {
    const viewName = view.name;
    if (cachedViews.value.includes(viewName)) return;
    if (view.keepAlive) {
      cachedViews.value.push(viewName);
      saveCurrentSystemState();
    }
  }

  function delVisitedView(view: TagView) {
    return new Promise((resolve) => {
      for (const [i, v] of visitedViews.value.entries()) {
        if (v.path === view.path) {
          visitedViews.value.splice(i, 1);
          break;
        }
      }
      saveCurrentSystemState();
      resolve([...visitedViews.value]);
    });
  }

  function delViewByPath(path: string) {
    return new Promise((resolve) => {
      // 先找到要删除的视图
      const targetView = visitedViews.value.find(v => v.path === path);
      
      // 删除 visitedViews 中匹配的视图
      for (const [i, v] of visitedViews.value.entries()) {
        if (v.path === path) {
          visitedViews.value.splice(i, 1);
          break;
        }
      }
      
      // 删除 cachedViews 中匹配的视图
      if (targetView) {
        const viewName = targetView.name;
        const index = cachedViews.value.indexOf(viewName);
        index > -1 && cachedViews.value.splice(index, 1);
      }
      
      saveCurrentSystemState();
      resolve({
        visitedViews: [...visitedViews.value],
        cachedViews: [...cachedViews.value],
      });
    });
  }

  function delCachedView(view: TagView) {
    const viewName = view.name;
    return new Promise((resolve) => {
      const index = cachedViews.value.indexOf(viewName);
      index > -1 && cachedViews.value.splice(index, 1);
      saveCurrentSystemState();
      resolve([...cachedViews.value]);
    });
  }

  function delOtherVisitedViews(view: TagView) {
    return new Promise((resolve) => {
      visitedViews.value = visitedViews.value.filter((v) => {
        return v?.affix || v.path === view.path;
      });
      saveCurrentSystemState();
      resolve([...visitedViews.value]);
    });
  }

  function delOtherCachedViews(view: TagView) {
    const viewName = view.name as string;
    return new Promise((resolve) => {
      const index = cachedViews.value.indexOf(viewName);
      if (index > -1) {
        cachedViews.value = cachedViews.value.slice(index, index + 1);
      } else {
        cachedViews.value = [];
      }
      saveCurrentSystemState();
      resolve([...cachedViews.value]);
    });
  }

  function updateVisitedView(view: TagView) {
    for (let v of visitedViews.value) {
      if (v.path === view.path) {
        v = Object.assign(v, view);
        break;
      }
    }
    saveCurrentSystemState();
  }

  function addView(view: TagView) {
    addVisitedView(view);
    addCachedView(view);
  }

  function delView(view: TagView) {
    return new Promise((resolve) => {
      delVisitedView(view);
      delCachedView(view);
      resolve({
        visitedViews: [...visitedViews.value],
        cachedViews: [...cachedViews.value],
      });
    });
  }

  function delOtherViews(view: TagView) {
    return new Promise((resolve) => {
      delOtherVisitedViews(view);
      delOtherCachedViews(view);
      resolve({
        visitedViews: [...visitedViews.value],
        cachedViews: [...cachedViews.value],
      });
    });
  }

  function delLeftViews(view: TagView) {
    return new Promise((resolve) => {
      const currIndex = visitedViews.value.findIndex(
        (v) => v.path === view.path
      );
      if (currIndex === -1) {
        return;
      }
      visitedViews.value = visitedViews.value.filter((item, index) => {
        if (index >= currIndex || item?.affix) {
          return true;
        }

        const cacheIndex = cachedViews.value.indexOf(item.name);
        if (cacheIndex > -1) {
          cachedViews.value.splice(cacheIndex, 1);
        }
        return false;
      });
      resolve({
        visitedViews: [...visitedViews.value],
      });
    });
  }
  function delRightViews(view: TagView) {
    return new Promise((resolve) => {
      const currIndex = visitedViews.value.findIndex(
        (v) => v.path === view.path
      );
      if (currIndex === -1) {
        return;
      }
      visitedViews.value = visitedViews.value.filter((item, index) => {
        if (index <= currIndex || item?.affix) {
          return true;
        }
      });
      resolve({
        visitedViews: [...visitedViews.value],
      });
    });
  }

  function delAllViews() {
    return new Promise((resolve) => {
      const affixTags = visitedViews.value.filter((tag) => tag?.affix);
      visitedViews.value = affixTags;
      cachedViews.value = [];
      resolve({
        visitedViews: [...visitedViews.value],
        cachedViews: [...cachedViews.value],
      });
    });
  }

  function toLastView(visitedViews: TagView[], view?: TagView) {
    const latestView = visitedViews.slice(-1)[0];
    if (latestView && latestView.fullPath) {
      router.push(latestView.fullPath);
    } else {
      // now the default is to redirect to the home page if there is no tags-view,
      // you can adjust it according to your needs.
      if (view?.name === "Dashboard") {
        // to reload home page
        router.replace("/redirect" + view.fullPath);
      } else {
        router.push("/");
      }
    }
  }

  return {
    visitedViews,
    cachedViews,
    systemTagsViewMap,
    isActive,
    addVisitedView,
    addCachedView,
    delVisitedView,
    delCachedView,
    delOtherVisitedViews,
    delOtherCachedViews,
    updateVisitedView,
    addView,
    delView,
    saveCurrentSystemState,
    restoreSystemState,
    toLastView,
    delAllViews,
    delRightViews,
    delLeftViews,
    delOtherViews,
    delViewByPath
  };
});
