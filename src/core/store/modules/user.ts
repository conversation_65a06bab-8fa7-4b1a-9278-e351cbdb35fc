import Auth<PERSON><PERSON>, { LoginData, LoginResult } from "@/core/api/auth";
import User<PERSON><PERSON>, { UserInfo } from "@/core/api/accountManagement";
import { resetRouter } from "@/core/router";
import { store } from "@/core/store";
import { usePermissionStore } from "@/core/store";
import {
  TOKEN_KEY,
  SYSTEM_ID_KEY,
  UPDATEPASSWORD_FLAG_KEY,
} from "@/core/enums/CacheEnum";

export const useUserStore = defineStore("user", () => {
  const permissionStore = usePermissionStore();
  const user = ref<UserInfo>({
    roles: [],
    perms: [],
  });

  const cacheSystemId = localStorage.getItem(SYSTEM_ID_KEY);
  console.log("cacheSystemId---", cacheSystemId);
  // Add systemId to store,use localstorage to cache systemId
  const systemId = ref<string>(cacheSystemId || "");

  const setSystemId = (sid: string) => {
    systemId.value = sid;
  };
  /**
   * 登录
   *
   * @param {LoginData}
   * @returns
   */
  function login(loginData: LoginData) {
    return new Promise<void>((resolve, reject) => {
      AuthAPI.login(loginData)
        .then((data) => {
          const { token_type, access_token, loginResultVO } = data;
          localStorage.setItem(TOKEN_KEY, token_type + " " + access_token);
          //是否强制修改密码 1--是；否则--否
          localStorage.setItem(
            UPDATEPASSWORD_FLAG_KEY,
            loginResultVO.updatePasswordFlag
          );
          localStorage.setItem("userId", loginResultVO.userId);

          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // 获取信息(用户昵称、头像、角色集合、权限集合)
  function getUserInfo() {
    return new Promise<UserInfo>((resolve, reject) => {
      UserAPI.getInfo()
        .then((data: any) => {
          if (!data) {
            reject("Verification failed, please Login again.");
            return;
          }
          /*if (!data.roles || data.roles.length <= 0) {
            reject("getUserInfo: roles must be a non-null array!");
            return;
          }*/
          const perms: any = [];
          if (data && data.authorities && data.authorities.length > 0) {
            data.authorities.forEach((item: any) => {
              perms.push(item.authority);
            });
          }
          data.perms = perms || [];
          Object.assign(user.value, { ...data });
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // user logout
  function logout() {
    return new Promise<void>((resolve, reject) => {
      AuthAPI.logout({ token: localStorage.getItem(TOKEN_KEY).split(" ")[1] })
        .then((res) => {
          localStorage.setItem(TOKEN_KEY, "");
          sessionStorage.removeItem('voucherForm');
          permissionStore.hasRoles = false;
          resetRouter();
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // remove token
  function resetToken() {
    return new Promise<void>((resolve) => {
      localStorage.setItem(TOKEN_KEY, "");
      sessionStorage.removeItem('voucherForm');
      resetRouter();
      resolve();
    });
  }

  return {
    user,
    systemId,
    setSystemId,
    login,
    getUserInfo,
    logout,
    resetToken,
  };
});

export function useUserStoreHook() {
  return useUserStore(store);
}
