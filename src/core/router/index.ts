import type { App } from "vue";
import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";
// 引入子业务系统的静态路由，【动态路由在登录时统一处理】
// 各模块静态路由
import { constantRoutes as or } from "@oms/router/index";
import { constantRoutes as wr } from "@wms/router/index";
import { constantRoutes as pr } from "@pms/router/index";
import { constantRoutes as tr } from "@tms/router/index";
import { constantRoutes as gs } from "@goods/router/index";
// import { constantRoutes as sr } from "@system/router/index"; // 系统设置路由
import { constantRoutes as fr } from "@finance/router/index";

export const Layout = () => import("@/core/layout/index.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/core/views/redirect/index.vue"),
      },
    ],
  },

  {
    path: "/login",
    component: () => import("@/core/views/login/index.vue"),
    meta: { hidden: true },
  },

  {
    path: "/",
    name: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/core/views/dashboard/index.vue"),
        // 用于 keep-alive 功能，需要与 SFC 中自动推导或显式声明的组件名称一致
        // 参考文档: https://cn.vuejs.org/guide/built-ins/keep-alive.html#include-exclude
        name: "Dashboard",
        meta: {
          title: "dashboard",
          icon: "homepage",
          affix: true,
          keepAlive: true,
        },
      },
      {
        path: "401",
        component: () => import("@/core/views/error-page/401.vue"),
        meta: { hidden: true },
      },
      {
        path: "404",
        component: () => import("@/core/views/error-page/404.vue"),
        meta: { hidden: true },
      },
    ],
  },
 /* {
    path: "/application",
    component: Layout,
    children: [
      {
        path: "applicationCenter",
        component: () =>
          import("@/core/views/application/applicationCenter/index.vue"),
        meta: {
          title: "应用中心",
          icon: "homepage",
          keepAlive: true,
        },
      },
    ],
  },*/
  {
    path: "/advertising",
    children: [
      {
        path: "index",
        component: () => import("@/core/views/advertising/index.vue"),
        meta: {
          meta: { hidden: true },
        },
      },
    ],
  },

  {
    path: "/transportationMgr", // 集运广告页
    children: [
      {
        path: "index",
        component: () => import("@/core/views/transportationMgr/index.vue"),
        meta: {
          meta: { hidden: true },
        },
      },
    ],
  },

  {
    path: "/pms",
    component: Layout,
    children: [
      {
        path: "dashboard",
        component: () => import("@pms/views/dashboard/index.vue"),
        meta: {
          title: "工作台",
          icon: "homepage",
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/oms",
    component: Layout,
    children: [
      {
        path: "dashboard",
        component: () => import("@oms/views/dashboard/index.vue"),
        meta: {
          title: "工作台",
          icon: "homepage",
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/tms",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@tms/views/dashboard/index.vue"),
        meta: {
          title: "工作台",
          icon: "homepage",
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/wms",
    component: Layout,
    children: [
      {
        path: "dashboard",
        component: () => import("@wms/views/dashboard/index.vue"),
        meta: {
          title: "工作台",
          icon: "homepage",
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/goods",
    component: Layout,
    children: [
      {
        path: "dashboard",
        component: () => import("@wms/views/dashboard/index.vue"),
        meta: {
          title: "工作台",
          icon: "homepage",
          keepAlive: true,
        },
      },
    ],
  },
  ...pr,
  ...or,
  ...wr,
  ...tr,
  ...gs,
  ...fr,
  // 外部链接
  // {
  //   path: "/external-link",
  //   component: Layout,
  //   children: [ {
  //       component: () => import("@/views/external-link/index.vue"),
  //       path: "https://www.cnblogs.com/haoxianrui/",
  //       meta: { title: "外部链接", icon: "link" },
  //     },
  //   ],
  // },
  // 多级嵌套路由
  /* {
         path: '/nested',
         component: Layout,
         redirect: '/nested/level1/level2',
         name: 'Nested',
         meta: {title: '多级菜单', icon: 'nested'},
         children: [
             {
                 path: 'level1',
                 component: () => import('@/views/nested/level1/index.vue'),
                 name: 'Level1',
                 meta: {title: '菜单一级'},
                 redirect: '/nested/level1/level2',
                 children: [
                     {
                         path: 'level2',
                         component: () => import('@/views/nested/level1/level2/index.vue'),
                         name: 'Level2',
                         meta: {title: '菜单二级'},
                         redirect: '/nested/level1/level2/level3',
                         children: [
                             {
                                 path: 'level3-1',
                                 component: () => import('@/views/nested/level1/level2/level3/index1.vue'),
                                 name: 'Level3-1',
                                 meta: {title: '菜单三级-1'}
                             },
                             {
                                 path: 'level3-2',
                                 component: () => import('@/views/nested/level1/level2/level3/index2.vue'),
                                 name: 'Level3-2',
                                 meta: {title: '菜单三级-2'}
                             }
                         ]
                     }
                 ]
             },
         ]
     }*/
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: [...constantRoutes, ...wr, ...pr, ...tr, ...or, ...gs],
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// 全局注册 router
export function setupRouter(app: App<Element>) {
  app.use(router);
}

/**
 * 重置路由
 */
export function resetRouter() {
  router.replace({ path: "/login" });
}

export default router;
