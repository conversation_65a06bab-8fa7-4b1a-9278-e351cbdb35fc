<template>
  <section class="app-main" :style="{ height: minHeight}">
    <router-view>
      <template #default="{ Component, route }">
        <transition
          enter-active-class="animate__animated animate__fadeIn"
          mode="out-in"
        >
            <!-- <component :is="Component" :key="route.meta?.keepAlive ? route.name : route.path" /> -->
             <!-- route.params.transferOrderCode兼容编辑调拨单缓存 -->
          <keep-alive :include="cachedViews">
              <component 
                :is="Component" 
                :key="route.meta?.keepAlive ? (route.params.transferOrderCode ? `${route.name}-${route.params.transferOrderCode}` : route.name) : route.path" 
              />
          </keep-alive>
        </transition>
      </template>
    </router-view>
  </section>
</template>

<script setup lang="ts">
import { useSettingsStore, useTagsViewStore } from "@/core/store";
import variables from "@/core/styles/variables.module.scss";

const cachedViews = computed(() => useTagsViewStore().cachedViews); // 缓存页面集合
const minHeight = computed(() => {
  if (useSettingsStore().tagsView) {
    return `calc(100vh - ${variables["navbar-height"]} - ${variables["tags-view-height"]} )`;
  } else {
    return `calc(100vh - ${variables["navbar-height"]} )`;
  }
});
</script>

<style lang="scss" scoped>
.app-main {
  position: relative;
  background-color:#F4F6FA;
  box-sizing: border-box;
  padding: 0px 10px 10px 10px;
  overflow-y: auto;
}
</style>
