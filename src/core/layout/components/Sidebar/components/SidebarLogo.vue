<template>
  <div class="logo-container">
    <transition enter-active-class="animate__animated animate__fadeInLeft">
      <router-link v-if="collapse" class="wh-full flex-center" to="/">
        <!-- <img v-if="settingsStore.sidebarLogo" :src="logo" class="logo-image" /> -->
        <img
          v-if="settingsStore.sidebarLogo"
          src="@/core/assets/images/Logo_left.png"
          class="logo-image"
        />
      </router-link>

      <router-link v-else class="wh-full flex-center-start" to="/">
        <!--         <img v-if="settingsStore.sidebarLogo" :src="logo" class="logo-image" />-->
        <img
          v-if="settingsStore.sidebarLogo"
          src="@/core/assets/images/Logo_left.png"
          class="logo-image"
        />
        <div class="logo-title">{{ defaultSettings.title }}</div>
        <div class="logo-right">企业管家</div>
        <img src="@/core/assets/images/erp-intro.png" class="erp-intro">
      </router-link>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import defaultSettings from "@/core/settings";
import { useSettingsStore } from "@/core/store";

const settingsStore = useSettingsStore();

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});

// const logo = ref(new URL(`../../../../assets/logo.png`, import.meta.url).href);
</script>

<style lang="scss" scoped>
.erp-intro{
  margin-left: 10px;
  height: 20px;
  width: 200px;
}
.logo-container {
  width: 100%;
  height: $navbar-height;
  background-color: $sidebar-logo-background;
  padding: 0px 10px;

  .logo-image {
    width: 28px;
    height: 28px;
    //background: #FFFFFF;
    // border-radius: 8px;
  }

  .logo-title {
    flex-shrink: 0; /* 防止容器在空间不足时缩小 */
    font-size: 16px;
    color: #ffffff;
    text-align: left;
    font-style: normal;
    margin-left: 6px;
    margin-right: 6px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    line-height: 20px;
  }
  .logo-right{
    background: #FFFFFF;
    border-radius: 9px;
    font-family: DingTalk, DingTalk;
    font-weight: normal;
    font-size: 8px;
    color: #762ADB;
    line-height: 10px;
    text-align: left;
    font-style: normal;
    padding: 3px 5px;
    flex: none;
  }
}

.layout-top,
.layout-mix {
  .logo-container {
    width: $sidebar-width;
  }

  &.hideSidebar {
    .logo-container {
      width: $sidebar-width-collapsed;
    }
  }
}
</style>
