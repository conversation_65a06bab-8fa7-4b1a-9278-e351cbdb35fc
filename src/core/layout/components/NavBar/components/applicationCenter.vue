<template>
  <div
    class="system-setting"
    @click="handleApplicationCenterMenu('application')"
  >
    {{ $t("applicationCenter.title") }}
  </div>
</template>

<script setup lang="ts">
import {
  useUserStore,
  useTagsViewStore,
  usePermissionStore,
} from "@/core/store";
import { handleFinanceModuleEntry } from "@/core/utils/financeModuleManager";
const router = useRouter();
const userStore = useUserStore();
const permissionStore = usePermissionStore();
const tagsViewStore = useTagsViewStore();
const handleApplicationCenterMenu = async (id: string) => {
  permissionStore.setSystemId(id);
  userStore.setSystemId(id);

  // 检测是否切换到财务系统，如果是则执行账套清空逻辑
  if (id === 'finance') {
    try {
      await handleFinanceModuleEntry('', id);
    } catch (error) {
      console.error("[财务模块] 应用中心切换时账套清空处理失败:", error);
    }
  }

  // 恢复指定系统的tagsview状态
  tagsViewStore.restoreSystemState(id);
  // router.push(`/application/applicationCenter`);
    router.push(`/application/application/applicationCenter`);
};
</script>

<style scoped lang="scss">
.system-setting {
  display: flex;
  align-items: center;
  // padding: 0 10px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  cursor: pointer;
  word-break: keep-all;
}
</style>
