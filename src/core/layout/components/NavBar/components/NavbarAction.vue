<template>
  <div class="flex">
    <template v-if="!isMobile">
      <!-- wms仓库筛选 -->
      <SelWareHouse v-if="warehouseSelectShow" />
      <!--  应用中心   -->
      <ApplicationCenter class="ml-30px" />
      <!--  设置，跳转系统设置菜单   -->
      <SystemSetting
        class="mr-30px ml-30px"
        v-if="permissionStore.isShowSystem"
      />
      <!--全屏 -->
      <!-- <div class="nav-action-item" @click="toggle">
        <svg-icon
          :icon-class="isFullscreen ? 'fullscreen-exit' : 'fullscreen'"
        />
      </div> -->

      <!-- 布局大小 -->
      <!-- <el-tooltip
        class="display-none"
        :content="$t('sizeSelect.tooltip')"
        effect="dark"
        placement="bottom"
      >
        <size-select class="nav-action-item" />
      </el-tooltip> -->

      <!-- 语言选择 -->
      <!-- <lang-select class="nav-action-item display-none" /> -->

      <!-- 消息通知 -->
      <!-- <el-dropdown class="message nav-action-item display-none" trigger="click">
        <el-badge is-dot>
          <div class="flex-center h100% p10px">
            <i-ep-bell />
          </div>
        </el-badge>
        <template #dropdown>
          <div class="px-5 py-2">
            <el-tabs v-model="activeTab">
              <el-tab-pane
                v-for="(label, key) in MessageTypeLabels"
                :label="label"
                :name="key"
                :key="key"
              >
                <div
                  class="w-[380px] py-2"
                  v-for="message in getFilteredMessages(key)"
                  :key="message.id"
                >
                  <el-link type="primary">
                    <el-text class="w-350px" size="default" truncated>
                      {{ message.title }}
                    </el-text>
                  </el-link>
                </div>
              </el-tab-pane>
            </el-tabs>
            <el-divider />
            <div class="flex-x-between">
              <el-link type="primary" :underline="false">
                <span class="text-xs">查看更多</span>
                <el-icon class="text-xs"><ArrowRight /></el-icon>
              </el-link>
              <el-link type="primary" :underline="false">
                <span class="text-xs">全部已读</span>
              </el-link>
            </div>
          </div>
        </template>
      </el-dropdown> -->
    </template>

    <!-- 用户头像 -->
    <el-dropdown class="nav-action-item" trigger="click">
      <div class="flex-center h100% p10px">
        <img
          src="@/core/assets/images/avatar.svg"
          class="rounded-full mr-10px w24px w24px"
        />
        <span class="main-text">{{ userStore.user.nickName }}</span>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
            <el-dropdown-item @click="handlePersonal">
              {{ $t("navbar.personal") }}
            </el-dropdown-item>
          <el-dropdown-item divided @click="updatePassword">
            {{ $t("navbar.updatePassword") }}
          </el-dropdown-item>

          <el-dropdown-item divided @click="logout">
            {{ $t("navbar.logout") }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!--  -->
    <!-- <template v-if="defaultSettings.showSettings">
      <div class="nav-action-item" @click="settingStore.settingsVisible = true">
        <svg-icon icon-class="setting" />
      </div>
    </template> -->

    <!-- 修改密码弹窗 -->
    <!--      <UpdatePassword v-model:dialog-visible="dialogVisible"/>-->
    <UpdatePassword
      :dialog-visible="dialogVisible"
      @on-submit="onUpdatePassword"
    />
  </div>
</template>
<script setup lang="ts">
import {
  useAppStore,
  useTagsViewStore,
  useUserStore,
  useSettingsStore,
  usePermissionStore,
} from "@/core/store";
import defaultSettings from "@/core/settings";
import { DeviceEnum } from "@/core/enums/DeviceEnum";
import {
  MessageTypeEnum,
  MessageTypeLabels,
} from "@/core/enums/MessageTypeEnum";
import ApplicationCenter from "./applicationCenter.vue";
import SystemSetting from "./Setting.vue";
import SelWareHouse from "@/modules/wms/components/SelWareHouse.vue";
const appStore = useAppStore();
const tagsViewStore = useTagsViewStore();
const userStore = useUserStore();
const settingStore = useSettingsStore();
const permissionStore = usePermissionStore();
const route = useRoute();
const router = useRouter();
const warehouseSelectShow = computed(() => userStore.systemId === "wms");
const isMobile = computed(() => appStore.device === DeviceEnum.MOBILE);
import { SYSTEM_ID_KEY, TOKEN_KEY } from "@/core/enums/CacheEnum";
const { isFullscreen, toggle } = useFullscreen();

const activeTab = ref(MessageTypeEnum.MESSAGE);


// 判断是否需要强制更改密码
const updatePasswordFlag = localStorage.getItem("updatePasswordFlag");
const dialogVisible = ref(
  updatePasswordFlag == '1'
);

function onUpdatePassword() {
  if(updatePasswordFlag == '1') return; // 强制修改密码，则禁止关闭遮罩层
  dialogVisible.value = false;
}

function updatePassword() {
  dialogVisible.value = true;
}
function handlePersonal() {
    router.push("/goods/personalSet/personal");
}

/* 注销 */
function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    lockScroll: false,
  }).then(() => {
    userStore
      .logout()
      .then(() => {
        tagsViewStore.delAllViews();
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(SYSTEM_ID_KEY);
        userStore.setSystemId('')
      })
      .then(() => {
        router.push("/login");
      });
  });
}
</script>
<style lang="scss" scoped>
.nav-action-item {
  display: inline-block;
  min-width: 40px;
  height: $navbar-height;
  line-height: $navbar-height;
  // color: var(--el-text-color);
  color: #fff;
  text-align: center;
  cursor: pointer;

  &:hover {
    background: rgb(0 0 0 / 10%);
  }
}

:deep(.message .el-badge__content.is-fixed.is-dot) {
  top: 5px;
  right: 10px;
}

:deep(.el-divider--horizontal) {
  margin: 10px 0;
}

.dark .nav-action-item:hover {
  background: rgb(255 255 255 / 20%);
}

.layout-top .nav-action-item,
.layout-mix .nav-action-item {
  color: #fff;
}
.main-text {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  word-break: keep-all;
}
</style>
