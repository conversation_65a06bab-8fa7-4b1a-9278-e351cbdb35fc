<template>
  <div class="contact-form">
    <div class="form-subtitle">
      {{ getProductTitle() }}
    </div>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit.prevent="handleSubmit"
      label-position="top"
    >
      <el-form-item :label="$t('contactForm.fields.contactName')" prop="contactName">
        <el-input
          v-model="formData.contact"
          :placeholder="$t('contactForm.placeholders.contactName')"
          clearable
          :aria-label="$t('contactForm.fields.contactName')"
          :aria-required="true"
        />
      </el-form-item>

      <el-form-item :label="$t('contactForm.fields.phoneNumber')" prop="phoneNumber">
        <div class="phone-input">
          <el-select
            v-model="formData.contactAreaCode"
            class="country-code"
            :placeholder="区号"
          >
            <el-option label="+86" value="+86" />
            <el-option label="+1" value="+1" />
            <el-option label="+44" value="+44" />
            <el-option label="+81" value="+81" />
            <el-option label="+82" value="+82" />
          </el-select>
          <el-input
            v-model="formData.contactNumber"
            :placeholder="$t('contactForm.placeholders.phoneNumber')"
            clearable
            style="flex: 1"
            :aria-label="$t('contactForm.fields.phoneNumber')"
            :aria-required="true"
          />
        </div>
      </el-form-item>

      <el-form-item :label="$t('contactForm.fields.email')" prop="email">
        <el-input
          v-model="formData.email"
          :placeholder="$t('contactForm.placeholders.email')"
          clearable
          :aria-label="$t('contactForm.fields.email')"
        />
      </el-form-item>

      <el-form-item :label="$t('contactForm.fields.message')" prop="message">
        <el-input
          v-model="formData.intentionDescription"
          type="textarea"
          :placeholder="$t('contactForm.placeholders.message')"
          :rows="4"
          class="message-textarea"
          :aria-label="$t('contactForm.fields.message')"
          :aria-required="true"
        />
      </el-form-item>

      <div class="form-actions">
        <el-button @click="handleCancel">
          {{ $t("contactForm.buttons.cancel") }}
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="props.submitting">
          {{ $t("contactForm.buttons.submit") }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { ContactFormData } from '@/core/api/contact'

defineOptions({
  name: 'ContactForm'
})

// Props
interface Props {
  submitting?: boolean
  productType?: string
}

const props = withDefaults(defineProps<Props>(), {
  submitting: false,
  productType: ''
})

// Emits
const emit = defineEmits<{
  submit: [data: ContactFormData]
  cancel: []
}>()

// Refs
const formRef = ref<FormInstance>()

// Form data
const formData = reactive<ContactFormData>({
  "enterpriseName": "",
  "appCode": "",
  "contact": "",
  "contactNumber": "",
  "intentionDescription": ""
})

// Validation rules
const formRules: FormRules = {
  contact: [
    {
      required: true,
      message: () => useI18n().t('contactForm.validation.contactNameRequired'),
      trigger: 'blur'
    },
    {
      min: 2,
      max: 50,
      message: () => useI18n().t('contactForm.validation.contactNameLength'),
      trigger: 'blur'
    }
  ],
  contactNumber: [
    {
      required: true,
      message: () => useI18n().t('contactForm.validation.phoneNumberRequired'),
      trigger: 'blur'
    },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: () => useI18n().t('contactForm.validation.phoneNumberInvalid'),
      trigger: 'blur'
    }
  ],
}

// Methods
const getProductTitle = () => {
  const { t } = useI18n()
  switch (props.productType) {
    case 'customerSteward':
      return t('applicationCenter.cardTitle.customerStewardTitle')
    case 'wms':
      return t('applicationCenter.cardTitle.WMSTitle')
    case 'jyPlatform':
      return t('applicationCenter.cardTitle.JYTitle')
    default:
      return t('contactForm.subtitle')
  }
}

const handleSubmit = async () => {
  if (!formRef.value || props.submitting) return
  
  try {
    await formRef.value.validate()
    emit('submit', { ...formData })
  } catch (error) {
    console.log('Form validation failed:', error)
  }
}

const handleCancel = () => {
  emit('cancel')
}

// Reset form
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    contact: '',
    contactNumber: '',
    intentionDescription: ''
  })
}

// Expose methods
defineExpose({
  resetForm
})
</script>

<style scoped lang="scss">
.contact-form {
  .form-subtitle {
    color: #762adb;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    text-align: center;
  }

  .el-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .phone-input {
      display: flex;
      gap: 8px;

      .country-code {
        width: 80px;
        flex-shrink: 0;
      }

      // Responsive design for phone input
      @media (max-width: 480px) {
        flex-direction: column;
        gap: 10px;

        .country-code {
          width: 100%;
        }
      }
    }

    .message-textarea {
      .el-textarea__inner {
        min-height: 100px;
        resize: vertical;
      }
    }
  }

  .form-actions {
    text-align: right;
    margin-top: 30px;

    .el-button {
      margin-left: 10px;
    }

    // Responsive design for form actions
    @media (max-width: 480px) {
      text-align: center;
      
      .el-button {
        margin: 5px;
        width: 100px;
      }
    }
  }

  // Responsive design for form labels
  @media (max-width: 768px) {
    .el-form {
      :deep(.el-form-item__label) {
        font-size: 14px;
      }
    }
  }

  @media (max-width: 480px) {
    .form-subtitle {
      font-size: 14px;
      margin-bottom: 15px;
    }

    .el-form {
      :deep(.el-form-item) {
        margin-bottom: 15px;
      }

      :deep(.el-form-item__label) {
        font-size: 13px;
        line-height: 1.4;
      }
    }

    .form-actions {
      margin-top: 20px;
    }
  }
}
</style>