<template>
  <div>
    <svg ref="barcode">a</svg>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import JsBarcode from "jsbarcode";

// 定义 props
const props = defineProps({
  value: {
    type: String,
    required: true,
  },
  options: {
    type: Object,
    default: () => ({
      format: "CODE128", // 默认格式
      width: 2, // 默认宽度
      height: 100, // 默认高度
      displayValue: true, // 显示条形码值
    }),
  },
});

// 获取 SVG 元素的引用
const barcode = ref(null);

// 生成条形码的函数
const generateBarcode = () => {
  try {
    JsBarcode(barcode.value, props.value, props.options);
  } catch (error) {
    console.error("生成条形码失败:", error);
  }
};

// 在组件挂载后生成条形码
onMounted(() => {
  generateBarcode();
});

// 监听 value 和 options 的变化，动态更新条形码
watch(
  () => [props.value, props.options],
  () => {
    generateBarcode();
  }
);
</script>

<style scoped>
svg {
  /*width: 264px;
  height: auto;*/
}
</style>
