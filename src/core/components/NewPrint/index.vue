<template>
  <div class="print-container">
    <div id="printContent">
      <slot></slot>
    </div>
    <el-button
      ref="printButton"
      class="print-button"
      v-print="printObj"
    ></el-button>
  </div>
</template>
<script setup lang="ts">
const emit = defineEmits([
  "previewBeforeOpenCallback",
  "previewOpenCallback",
  "beforeOpenCallback",
  "openCallback",
  "closeCallback",
  "clickMounted",
]);
// 通过ref获取打印按钮的引用
const printButton = ref(null);
// 打印配置
const printObj = ref({
  id: "printContent", // 这里是要打印元素的ID
  popTitle: '',
  zIndex: 20003,
  previewBeforeOpenCallback(value: any) {
    emit("previewBeforeOpenCallback", value);
    console.log("正在加载预览窗口！");
  }, // 预览窗口打开之前的callback
  previewOpenCallback(value: any) {
    emit("previewOpenCallback", value);
    console.log("已经加载完预览窗口，预览打开了！");
  }, // 预览窗口打开时的callback
  beforeOpenCallback(value: any) {
    emit("previewOpenCallback", value);
    console.log("开始打印之前！");
  }, // 开始打印之前的callback
  openCallback(value: any) {
    emit("openCallback", value);
    console.log("监听到了打印窗户弹起了！");
  }, // 调用打印时的callback
  closeCallback(value: any) {
    emit("closeCallback", value);
    console.log("关闭了打印工具！");
  }, // 关闭打印的callback(点击弹窗的取消和打印按钮都会触发)
});

/**打印*/
function onPrint() {
  setTimeout(() => {
    printButton.value?.$el?.click();
  }, 500);
}

defineExpose({
    onPrint,
});
</script>

<style scoped lang="scss">
.print-container {
  display: none;
}

.print-button {
  height: 0;
  padding: 0;
  margin: 0;
  border: none;
  visibility: hidden;
}

@media print {
  @page {
    size: auto;
    /* 打印边距-会影响页眉页脚显示 */
    margin: 10mm 30mm 10mm 30mm;
    @bottom-center {
      content: counter(page) "/" counter(pages);
      font-size: 10px;
    }
  }
  body,
  html {
    height: auto !important;
  }

  .print-container {
    display: block !important;
  }
  /* 调整布局以适应打印 */
  #printContent {
    position: absolute;
    left: 0;
    top: 0;
  }
}
</style>
