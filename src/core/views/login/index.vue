<template>
  <div class="loginPage">
    <div class="login">
      <img src="@/core/assets/images/login-left.png" class="login-left" />
      <div class="login-container">
        <div class="login-bd">
          <div class="login-container__hd">
            <img src="@/core/assets/images/Logo.png" class="login-container__logo" alt="logo" />
            <span class="login-container__title">易通2.0</span>
            <div class="login-container__right">企业管家</div>
          </div>
          <el-tabs class="modify-tabs" v-model="data.loginType" @tabChange="tabChangeAction">
            <el-tab-pane label="密码登录" name="username">
              <Password ref="loginByPassword" />
            </el-tab-pane>
            <el-tab-pane label="短信登录" name="mobile">
              <Password ref="loginByShortMessage"/>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <div style="position: absolute;bottom: 0;text-align: center;width: 100%;z-index:9999;color: #90979f" class="link-div">
      <div style="line-height: 32px;display: flex;align-items: center;justify-content: center;"><a href="https://beian.miit.gov.cn/" target="_blank">浙ICP备2022012241号-6</a><img style="width: 17px;height: 17px;margin:0px 10px 0px 15px;" src="@/core/assets/images/beianImg.png" /><a href="https://beian.mps.gov.cn/#/query/webSearch?code=**************" rel="noreferrer" target="_blank">浙公网安备**************号</a></div>
    </div>

    <Verify @success="onSuccessVerify" ref="verify" :vSpace="16" :mode="verifyOptions.mode"
      :captchaType="verifyOptions.captchaType" :imgSize="verifyOptions.imgSize" />
  </div>
</template>

<script setup lang="ts">
import Password from "./components/Password.vue";
import { LocationQuery, useRoute } from "vue-router";
import type { TabsPaneContext } from "element-plus";
import { clearFinanceAccountClearedFlag } from "@/core/utils/financeModuleManager";
import {
  useUserStore,
} from "@/core/store";
// import UserAPI from "@/core/api/accountManagement"; // 移除，改为在财务模块中使用
const currentRoute = useRoute();
const router = useRouter();
const route = useRoute();
// const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const verify = ref();
const loginByPassword = ref();
const loginByShortMessage = ref();

const data = reactive({
  verifyOptions: {
    captchaType: "blockPuzzle",
    mode: "pop",
    imgSize: { width: "400px", height: "180px" },
  },
  title: import.meta.env.VITE_APP_TITLE,
  loginType: "username", // username 密码登录 mobile 验证码
  redirect: undefined,
});

let { verifyOptions,detailRef,detailDialog } = toRefs(data);
provide("showVerify", showVerify);
provide("loginSuccess", loginSuccess);

watch(
  currentRoute,
  (newRoute) => {
    data.redirect = undefined;
    // data.redirect = newRoute.query && newRoute.query.redirect;
    if (newRoute.query && newRoute.query.redirect) {
      let [redirectPath, redirectQuery] = newRoute.query.redirect.split("?");
      console.log(redirectPath, redirectQuery);
      data.redirect = {
        redirectPath,
        redirectQuery: redirectQuery ? urlParamToJson(redirectQuery) : {},
      };
      console.log("data.redirect", data.redirect);
    }
  },
  { immediate: true }
);

function urlParamToJson(url) {
  if (!url) {
    return {};
  }

  let json = {};
  url
    .substring(url.indexOf("?") + 1)
    .trim()
    .split("&")
    .forEach((item) => (json[item.split("=")[0]] = item.split("=")[1]));

  return json;
}

function showVerify() {
  verify.value.show();
}

function onSuccessVerify(params) {
  if (data.loginType === "username") {
    loginByPassword.value.onSuccessVerify({
      ...params,
      ...data.verifyOptions,
      loginType: "username",
    });
  } else if (data.loginType === "mobile") {
    loginByShortMessage.value.onSuccessVerify({
      ...params,
      ...data.verifyOptions,
      loginType: "mobile",
    });
  }
}

function tabChangeAction(tab: TabsPaneContext, event: Event) {
  loginByPassword.value.resetData(tab);
  loginByShortMessage.value.resetData(tab);
}

import { SYSTEM_ID_KEY } from "@/core/enums/CacheEnum";
function loginSuccess() {
  // 清除财务账套清空标记，确保新的登录会话可以重新执行
  clearFinanceAccountClearedFlag();
  userStore.setSystemId('');
  localStorage.removeItem(SYSTEM_ID_KEY);
  const { path, queryParams } = parseRedirect();
  router.push({ path: path, query: queryParams });
  // 移除财务模块账套清空调用，改为在首次进入财务模块时调用
  // UserAPI.refreshLoginExt().then((data) => {})
}
function parseRedirect(): {
  path: string;
  queryParams: Record<string, string>;
} {
  const query: LocationQuery = route.query;
  const redirect = (query.redirect as string) ?? "/";

  const url = new URL(redirect, window.location.origin);
  const path = url.pathname;
  const queryParams: Record<string, string> = {};

  url.searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  return { path, queryParams };
}
</script>
<style></style>
<style lang="scss" scoped>
@use "./login.scss";

/* reset element-ui css */
:deep(.login-container) {
  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__item {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #90979f;
    padding-bottom: 12px;
    height: auto;
    line-height: 22px;

    &.is-active {
      color: var(--color-primary);
    }

    &:focus-visible {
      box-shadow: none;
    }
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }

  .el-tabs__active-bar {
    padding: 0 15px;
    background: none;

    &::after {
      content: " ";
      display: block;
      width: 100%;
      height: 3px;
      border-radius: 5px;
      background: var(--el-color-primary);
    }
  }
}
</style>
