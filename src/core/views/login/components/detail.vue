<template>
  <el-dialog
    v-model="isVisible"
    :title="detailData.contentName"
    :close-on-click-modal="false"
    width="800px"
    class="dialog-wrapper"
    @close="closeDialog"
    header-class="dialog-header"
  >
    <div class="dialog-content">
      <div v-html="detailData.contentDesc"></div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">{{ $t("common.close") }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import UserAPI from "@/core/api/accountManagement";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});

const state = reactive({
  loading: false,
  queryParams: "",
  detailData: "",
}) as any;

const { loading, queryParams, detailData } = toRefs(state);

const emit = defineEmits(["update:dialogVisible", "onSubmit"]);

const isVisible = computed({
  get: () => {
    return props.dialogVisible;
  },
  set: (val: any) => {
    closeDialog();
    reset();
  },
});

/**
 * 关闭
 */

function closeDialog() {
  emit("update:dialogVisible", false);
}

/**
 * 重置
 */
function reset() {
  queryParams.value = "";
  detailData.value = "";
}

/**
 * 查询
 * @param data
 */
function handleQuery(data: any) {
  queryParams.value = {
    ...data,
  };
    getDetail();
}

/**
 * 查询详情
 */
function getDetail() {
  loading.value = true;
  let params = {
    ...queryParams.value,
  };
  UserAPI.queryProvisionInfo(params.contentCode)
    .then((res: any) => {
      detailData.value = res || "";
    })
    .catch((err: any) => {
      detailData.value = "";
      console.warn(err);
    })
    .finally(() => {
      loading.value = false;
    });
}

defineExpose({
  handleQuery,
});
</script>

<style lang="scss" scoped>
.dialog-content {
  max-height: 600px;
  overflow: auto;
    padding: 20px;
    :deep(table){
        margin: 0 auto;
    }
}
.dialog-footer{
    width: 100%;
    display: block;
    text-align: center;
}
</style>
<style lang="scss">
.dialog-wrapper {
  .dialog-header {
    border-bottom: 1px solid #e5e7f3;
    margin-bottom: 20px;
  }
}
</style>
