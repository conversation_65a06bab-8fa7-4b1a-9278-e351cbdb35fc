import request from "@/core/utils/request";

const PURCHASE_BASE_URL = "/supply-oms/receivableAccount";

export default class FinanceAPI {
  /** 账单汇总 */
  static getBigCustomerBillSummary(data: any) {
    return request({
      url:   PURCHASE_BASE_URL + `/pageList`,
      method: "post",
      data: data,
    });
  }

  /** 列表账单汇总 */
  static getFinanceSummary(data: any) {
    return request({
      url:   PURCHASE_BASE_URL + `/total`,
      method: "post",
      data: data,
    });
  }

  /** 收款记录汇总 */
  static getCollectionRecord(data: any) {
    return request({
      url:   '/supply-oms/receivableAccountPaymentRecord/pageList',
      method: "post",
      data: data,
    });
  }

  /** 收款记录汇总合计 */
  static getFCollectionRecordSummary(data: any) {
    return request({
      url:  '/supply-oms/receivableAccountPaymentRecord/total',
      method: "post",
      data: data,
    });
  }

  /** 收款订单 */
  static getOrderRecord(data: any) {
    return request({
      url:   '/supply-oms/receivableAccountOrderDetail/pageList',
      method: "post",
      data: data,
    });
  }
  /** 收款订单合计 */
  static getOrderSummary(data: any) {
    return request({
      url:  '/supply-oms/receivableAccountOrderDetail/total',
      method: "post",
      data: data,
    });
  }
  /** 支付方式查询 */
  static getCollectionMethods() {
    return request({
      url:  '/supply-biz-common/paymentMethods/queryList',
      method: "get",
      params: {
        enableStatus: 1,
      },
    });
  }
    /** 新增收款记录 */
  static addCollectionRecord(data: any) {
    return request({
      url:  '/supply-oms/receivableAccountPaymentRecord/add',
      method: "post",
      data: data,
    });
  }

  /** 账单详情 */
  static getCollectionDetail(id: string | number) {
    return request({
      url:  `/supply-oms/receivableAccount/detail/${id}`,
      method: "get",
    });
  }

}
/**
 * 分页查询参数
 */
interface PageQuery {
  page: number;
  limit: number;
}

/** 应收帐单分页查询参数 */
export interface FinancePageQuery extends PageQuery {
  /*客户属性 1-大客户 2-散客*/
  attributesType: number;
  /*客户名称*/
  customerName? : string;
  /*客户类型*/
  customerTypeCode? : string;
  /*销售人员*/
  salesName? : string;
  /*是否正序*/
  sortType?: boolean;
  /*排序字段*/
  sortColumn?: string;
}

export interface CollectionRecordQuery extends PageQuery {
  dateRange?: array<string>;
  /* 收款开始时间*/
  startTime?: string;
  /*收款结束时间 */
  endTime?: string;
  /*收款人*/
  createUserName?: string;
  /*是否正序*/
  sortType?: boolean;
  /*排序字段*/
  sortColumn?: string;
  /*应收帐单汇总表id*/
  receivableAccountId?: string;
}

export interface FinancePageVO {
  /*id*/
  id: number;
  /*客户属性 1-大客户 2-散客*/
  attributesType: number;
  /*属性类型为是大客户 该值为客户编码 如果是散客 则为销售员id*/
  businessCode: string;
  /*客户名称*/
  customerName: string;
  /*客户类型编码*/
  customerTypeCode: string;
  /*客户类型名称*/
  customerTypeName: string;
  /*销售人员*/
  salesName: string;
  /*应收金额*/
  receivableAmount: number;
  /*实收金额*/
  actualReceivedAmount: number;
  /*剩余应收金额*/
  remainingReceivableAmount: number;
  /*售后金额*/
  returnAmount: number;
  /*币种编码*/
  currencyCode: string;
}

/*收款记录*/
export interface CollectionRecordVO {
  /*收款时间*/
  collectionTime?: string;
  /*收款金额*/
  collectionAmount?: number;
  /*收款凭证*/
  collectionVoucher?: [];
  /*备注*/
  remark?: string;
  /*收款人*/
  createUserName?: string;
  /*收款类型id*/
  collectionTypeId?: string;
  /*收款类型名称*/
  collectionTypeName?: string;
}

/** 应收帐单合计 */
export interface FinanceSummary {
  /*应收金额*/
  receivableAmount?: number;
  /*实收金额*/
  actualReceivedAmount?: number;
  /*剩余应收金额*/
  remainingReceivableAmount?: number;
}

export interface OrderSummary {
  /*订单金额*/
  orderAmount?: number;
  /*售后金额*/
  returnAmount?: number;
  /*应收金额*/
  receivableAmount?: number;
}

export interface CollectionRecord {
  /*id*/
  id: number;
  /*客户属性 1-大客户 2-散客*/
  attributesType: number;
  /*属性类型为是大客户 该值为客户编码 如果是散客 则为销售员id*/
  businessCode: string;
  /*客户名称*/
  customerName: string;
  /*客户类型编码*/
  customerTypeCode: string;
  /*客户类型名称*/
  customerTypeName: string;
  /*销售人员*/
  salesName: string;
}

export interface orderRecordQuery extends PageQuery {
  dateRange?: array<string>;
  /* 下单时间开始*/
  orderCreateTimeStar?: string;
  /*下单时间结束 */
  orderCreateTimeEnd?: string;
  /*订单编号*/
  orderCode?: string;
  /*销售人员*/
  salesName?: string;
  /*销售主题*/
  orderTheme?:string;
  /*是否正序*/
  sortType?: boolean;
  /*排序字段*/
  sortColumn?: string;
}

/*收款记录*/
export interface OrderRecordVO {
  /*订单号*/
  orderCode?: string;
  /*销售主题*/
  orderTheme?: string;
  /*下单时间*/
  submitterTime?: string;
  /*销售人员*/
  salesName?: string;
  /*订单金额*/
  orderAmount?: number;
  /*售后金额*/
  returnAmount?: number;
  /*应收金额*/
  receivableAmount?: number;
}

/*排序*/
export interface SortTableData {
  /*排序的字段*/
  prop: string;
  /*升序还是降序 asc desc*/
  order: string;
  [key: string]: any;
}
/*排序Info*/
export interface SortInfo {
  /*排序字段*/
  sortColumn: string,
  /*升序降序*/
  sortType: boolean,
}

/*收款方式*/
export interface PaymentMethod {
  /*id*/
  id: number;
  /*收款方名称*/
  methodName: string;
  /*是否启用*/
  enableStatus: string | number;
  [key: string]: any;
}

/*预览私密图片*/
export interface PreviewPrivateImage {
  /*oss桶的名称*/
  bucket: string;
  /*文件名*/
  fileName: string;
  /*文件原始名称*/
  originalFileName: string;
}
