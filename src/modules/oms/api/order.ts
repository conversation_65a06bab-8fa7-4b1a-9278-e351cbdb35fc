import request from "@/core/utils/request";
import {convertToTimestamp} from "@/core/utils";
import {File} from "buffer";

const PURCHASE_BASE_URL = "/supply-oms/order";

class OrderAPI {
    /** 仓库下拉框 */
    static getStorehouseSelect() {
        return request({
            url: `/supply-oms/warehouse/warehouseSelect`,
            method: "get",
        });
    }

  /** 获取订单分页数据 */
  static getOrderPage(queryParams?: OrderPageQuery) {
    return request<any, PageResult<OrderPageVO[]>>({
      url: `${PURCHASE_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /** 订单数量分类查询*/
  static getOrderCountOfType() {
    return request<any,OrderCountVO>({
      url: `${PURCHASE_BASE_URL}/getOrderCountOfType`,
      method: "get",
    });
  }

  /** 订单导入模板下载*/
  static getImportTemplateUrl() {
    return request({
      url: `${PURCHASE_BASE_URL}/getImportTemplateUrl`,
      method: "get",
    });
  }

  /** 订单导入*/
  static importOrder(data?:{file:File}) {
    return request({
      url: `${PURCHASE_BASE_URL}/importOrder`,
      method: "post",
      data: data,
      headers: {
          "Content-Type": "multipart/form-data",
      },
    });
  }

    /** 订单解密*/
    static querySrcInfo(data: any) {
        return request<any, PageResult<OrderPageVO[]>>({
            url: `${PURCHASE_BASE_URL}/querySrcInfo`,
            method: "post",
            data: data,
        });
    }

    /** 删除订单 */
  static deleteOrder(data: { id?:string ,orderCode?:string}) {
    return request({
      url: `${PURCHASE_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  /** 撤回订单 */
  static recallOrder(data: { id?:string}) {
    return request({
      url: `${PURCHASE_BASE_URL}/recall`,
      method: "post",
      data: data,
    });
  }

  /** 查询所有可选的商品列表(分页展示采购单选择的商品)*/
  static queryProductAll(data?: ProductAllPageQuery) {
    return request<any, PageResult<ProductAllPageVO>>({
      url: `${PURCHASE_BASE_URL}/queryInquiryPurchaseChooseProductListPage`,
      method: "post",
      data: data,
    });
  }

  /** 根据商品id查询订单编辑详情 */
  static queryDetailForEdit(data: { id?: string,orderCode?:string }) {
    return request<any, OrderFrom>({
      url: `${PURCHASE_BASE_URL}/queryDetailForEdit`,
      method: "post",
      data: data,
    });
  }

  /** 查询订单详情 */
  static queryOrderDetail(data: { id?: string,orderCode?:string }) {
    return request<any, OrderFrom>({
      url: `${PURCHASE_BASE_URL}/queryDetail`,
      method: "post",
      data: data,
    });
  }

  static getDeliveryWarehouseList() {
    return request({
      url: `${PURCHASE_BASE_URL}/deliveryWarehouseList`,
      method: "get",
    });
  }

  /** 添加订单 */
  static addOrder(data: OrderFrom) {
    return request({
      url: `${PURCHASE_BASE_URL}/save`,
      method: "post",
      data: data,
    });
  }

  /** 编辑订单 */
  static editOrder(data: OrderFrom) {
    return request({
      url: `${PURCHASE_BASE_URL}/edit`,
      method: "post",
      data: data,
    });
  }

  /** 取消订单 */
  static concelOrder(data: CancelOrderFrom) {
      return request({
          url: `${PURCHASE_BASE_URL}/cancel`,
          method: "post",
          data: data,
      });
  }

  /** 审核完成 */
  static auditCompleted(data: AuditCompletedFrom) {
      return request({
          url: `${PURCHASE_BASE_URL}/auditCompleted`,
          method: "post",
          data: data,
      });
  }

  /** 修改状态 */
  static updateStatus(data: UpdateStatus) {
      return request({
          url: `${PURCHASE_BASE_URL}/updateStatus`,
          method: "post",
          data: data,
      });
  }

  /** 生成交货计划*/
  static createDeliverySchedule(data: CreateDeliverySchedule) {
      return request({
          url: `/supply-oms/orderBatch/createDeliverySchedule`,
          method: "post",
          data: data,
      });
  }

  /** 调整交货计划详情*/
  static getDeliveryScheduleInfo(params = {orderId: Number}) {
    return request({
      url: `/supply-oms/orderBatch/adjustDeliveryScheduleInfo`,
      method: "get",
      params
    });
  }


  /** 调整交货计划*/
  static adjustDeliverySchedule(data: CreateDeliverySchedule) {
    return request({
      url: `/supply-oms/orderBatch/adjustDeliverySchedule`,
      method: "post",
      data: data,
    });
  }

  /*手动同步状态*/
  static syncHandeStatus(data: number[]) {
    return request({
      url: `/supply-oms/order/syncCreateWarehouseOutboundNotice`,
      method: "post",
      data: data,
    });
  }

  /*查询客户绑定的未执行和执行中的合同列表*/
  static queryCustomerContractList(data: any) {
    return request({
      url: `/supply-oms/contract/queryCustomerContractList`,
      method: "post",
      data: data,
    });
  }

  //配送方式表
  static queryDeliveryMethodList(data?: any){
    return request({
      url: `/supply-biz-common/deliveryMethods/queryPageList`,
      method: "post",
      data: data
    })
  }
  /*配送方式分类*/
  static queryDeliveryMethodsCategoryList(data?: any){
    return request({
      url: `/supply-biz-common/deliveryMethodsCategory/queryPageList`,
      method: "post",
      data: data
    })
  }

  /*异常取消*/
  static cancelOrderApi(data?: any){
    return request({
      url: `${PURCHASE_BASE_URL}/cancel`,
      method: "post",
      data: data
    })
  }

  /*驳回*/
  static rejectOrderApi(data?: any){
    return request({
      url: `${PURCHASE_BASE_URL}/auditReject`,
      method: "post",
      data: data
    })
  }

  /*审核完成*/
  static orderApprovePassApi(data?: any){
    return request({
      url: `${PURCHASE_BASE_URL}/auditCompleted`,
      method: "post",
      data: data
    })
  }

  /*查询原订单号*/
  static queryOldOrderCodeApi(queryCode?: any){
    return request({
      url: `${PURCHASE_BASE_URL}/orderCodeSelect?orderCode=${queryCode}`,
      method: "get",
    })
  }

  /*查询审核人列表*/
  static queryApproveUserApi(){
    return request({
      url: `/supply-oms/userAuditRlt/getUserListByRangeCode?rangeCode=201`,
      method: "get",
    })
  }

}

export default OrderAPI;


export interface CreateDeliverySchedule{
    /** 订单id */
    orderId?: string;
    /** 订单编码 */
    orderCode?: string;
    createDeliveryScheduleDateDTOS?: CreateDeliveryScheduleDateDTOS[];
}
export interface CreateDeliveryScheduleDateDTOS{
    /**  计划交货日期 */
    planDate?: string;
    /** 计划开始时间 */
    planDateStart?: string;
    /** 计划结束时间 */
    planDateEnd?: string;
    createDeliveryScheduleDateDetailDTOS?: [];
}
export interface UpdateStatus{
    /**  订单供货计划id */
    id?: string;
    /** 订单供货计划当前状态 */
    batchStatus?: number;
    /** 订单id */
    orderId?: string;
    /** 订单编码 */
    orderCode?: string;
}
export interface CancelOrderFrom{
    /** 订单id */
    orderId?: string;
    /** 备注 */
    remark?: string;
}

export interface AuditCompletedFrom{
    /** 订单id */
    orderId?: string;
    /**  仓库id */
    warehouseId?: string;
    /**  仓库编码 */
    warehouseCode?: string;
    /**  仓库名称 */
    warehouseName?: string;
}

/** 采购单分页查询参数 */
export interface OrderPageQuery extends PageQuery {
  /** 时间类型 */
  dateType?: number;
  /** 时间范围 */
  dateRange?: string[];
  orderCreateTimeStar?: string;
  orderCreateTimeEnd?: string;
  expectedReceivedTimeStar?: string;
  expectedReceivedTimeEnd?: string;
  /** 订购单号 */
  orderCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 客户名称 */
  customerName?: string;
  /** 来源 */
  // orderSource?: number;
  /*销售类型*/
  orderType: number;
  /*同步状态*/
  syncStatus?: number;
  /**/
  /** 订单状态 */
  status?: number;
  /*交付批次*/
  orderBatchNo?: string;
  /*制单人*/
  submitterUser?: string;
  /*只看我的*/
  isMine?: boolean;
}

/** 订单分页对象 */
export interface OrderPageVO {
  /** ID */
  id?: string;
  /** 订购单号 */
  orderCode?: string;
  /** 客户编码 */
  customerCode?: string;
  /** 客户信息 */
  customerName?: string;
  /** 商品 */
  allProductName?: string;
  /** 商品个数 */
  totalCount?: number;
  /** 币种编码 */
  currencyCode?: string;
  /** 币种名称 */
  currencyName?: string;
  /** 下单金额 */
  totalAmount?: string;
  /** 收货人 */
  contactPerson?: string;
  /** 联系电话区域代码*/
  contactAreaCode?: string;
  /** 联系方式*/
  contactMobile?: number;
  /** 联系地址*/
  countryId?: string;
  countryName?: string;
  provinceId?: string;
  provinceName?: string;
  cityId?: string;
  cityName?: string;
  districtId?: string;
  districtName?: string;
  address?: string;
  /** 期望收货日期00：00：00*/
  expectedReceivedTimeBase?: string;
  /** 期望收货开始时间*/
  expectedReceivedTimeStar?: string;
  /** 期望收货结束时间*/
  expectedReceivedTimeEnd?: string;
  /** 下单时间*/
  orderCreateTime?: string;
  /** 来源(1-手动新增 2-导入) */
  orderSource?: number;
  /**订单状态 （0-草稿 1-待审核 2-备货中 3-配送中 4-已完成 5-已关闭 ）*/
  orderStatus?: number;
  /** 备注 */
  remark?: string;
}

/** 列表汇总返回对象  */
export interface OrderCountVO {
    /** 全部 */
    allCount?: number;
    /** 草稿 */
    draftCount?: number;
    /** 待审核 */
    approveCount?: number;
    /** 备货中 */
    stockUpCount?: number;
    /** 配送中 */
    deliveryCount?: number;
    /** 已完成 */
    completeCount?: number;
    /** 已关闭 */
    cancelCount?: number;
}

/** 订单对象 */
export interface OrderFrom {
    /** ID */
    id?: string;
    /** 订购单号 */
    orderCode?: string;

    /** 销售主题 */
    orderTheme?: string;
    /** 销售类型 */
    orderType?: string | number;
    /*兑换卡号*/
    exchangeCode?: number;
    /*原订单号*/
    originalOrderCode?: number | string;
    /*客户属性*/
    customerAttributes?: number;
    /*合同id*/
    contractId: number;
    /*合同名称*/
    contractName: string;
    /*合同类型*/
    contractType?:string
    /*合同编号*/
    contractCode?: string;
    /*是否预售*/
    isPresale?: number;
    /*销售人员id*/
    salesId?: number;
    /*销售人员名称*/
    salesName?: string;
    /*审核人员id*/
    approveUser?: number;
    /*审核人员名称*/
    approveUserName?: string;
    /*经办人id*/
    handlerUserId?: number;
    /*经办人*/
    handler?: string;
    /*结算方式*/
    paymentType?: number;
    /*配送方式*/
    deliveryType?: number;
    /*商品总金额*/
    totalAmountCopy: number;
    /*优惠方式*/
    discountType?: number;
    /*优惠金额*/
    discountAmount?: number;
    /*优惠折扣*/
    discountValue?: number;
    /*优惠后金额*/
    totalDiscountedAmount?: number;
    /*客户id*/
    customerId: string;

    /** 客户编码 */
    customerCode?: string;
    /** 客户信息 */
    customerName?: string;
    /** 商品 */
    allProductName?: string;
    /** 商品个数 */
    totalCount?: number;
    /** 币种编码 */
    currencyCode?: string;
    /** 币种名称 */
    currencyName?: string;
    /** 商品合计金额(未手动修改与优惠) */
    originalTotalAmount?: number;
    /** 下单手动输入金额 */
    totalAmount?: number;
    /** 收货人 */
    contactPerson?: string;
    /** 联系电话区域代码*/
    contactAreaCode?: string;
    /** 联系方式*/
    contactMobile?: number;
    /** 联系地址*/
    countryId?: string;
    countryName?: string;
    provinceId?: string;
    provinceName?: string;
    cityId?: string;
    cityName?: string;
    districtId?: string;
    districtName?: string;
    address?: string;
    deliveryAddress?: string;
    /** 期望收货日期00：00：00*/
    expectedReceivedTimeBase?: string;
    /** 期望收货开始时间*/
    expectedReceivedTimeStar?: string;
    /** 期望收货结束时间*/
    expectedReceivedTimeEnd?: string;
    /** 下单时间*/
    orderCreateTime?: string;
    /*新的 下单时间字段*/
    submitterTime?: string;
    /** 来源(1-手动新增 2-导入) */
    orderSource?: number;
    /**订单状态 （0-草稿 1-待审核 2-备货中 3-配送中 4-已完成 5-已关闭 ）*/
    orderStatus?: number;
    /** 备注 */
    remark?: string;
    /** 订单附件信息 */
    orderAttachmentFiles?: string;
    /** 配货仓库编码 */
    warehouseId?: string;
    /** 配货仓库名称 */
    warehouseName?: string;
    /**是否提交审核 （false-保存草稿 true-提交 ）*/
    approveFlag?: boolean;
    /**是否为编辑订单 （false-调整 true-编辑 ）*/
    updateFlag?: boolean;
    /** 货品总金额*/
    totalAmount?: string;
    /** 仓库id*/
    warehouseId?: string;
    /** 仓库编码*/
    warehouseCode?: string;
    /** 仓库名称*/
    warehouseName?: string;
    /** 取消原因*/
    shutdownReason?: string;
    /**商品明细*/
    orderDetailDTOList?:  ProductAllPageVO[];
    /**交货计划*/
    deliveryScheduleVOList?:  DeliveryScheduleVOList[];
    /**操作日志*/
    logList?:  LogList[];
}

/** 可选商品列表弹窗分页查询参数 */
export interface ProductAllPageQuery extends PageQuery {
  chooseType?: number;
  /** 采购类型*/
  orderType?: number;
  /** 仓库*/
  warehouseId?: string;
  /** 计划交货日期*/
  startExpectedDeliveryDate?: string;
  /** 供应商*/
  supplierId?: string;
  keywords?: string;
  productName?: string;
  /** 商品分类 */
  productCategory?: string[];
  firstCategoryId?: string;
  secondCategoryId?: string;
  thirdCategoryId?: string;
  productCode?: string;
}

/** 可选商品列表弹窗分页对象 */
export interface ProductAllPageVO {
  /** ID */
  id?: string;
  /** 商品图片 */
  productImg?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 规格名称 */
  productSpecName?: string;
  /** 商品分类 */
  productCategoryFullName?: string;
  productCategory?: string;
  firstCategoryId?: string;
  secondCategoryId?: string;
  thirdCategoryId?: string;
  /** 币种Id */
  currencyCode?: string;
/*
  /!** 现有库存 *!/
  onHandStock?: string;
  /!** 计划采购量 *!/
  planPurchaseCount?: number;
  /!** 计划采购价 *!/
  purchasePrice?: string;
  planPurchasePrice?: string;
  /!** 计划采购金额 *!/
  planPurchaseAmount?: string;
  /!** 已收货量 *!/
  receivedCount?: number;
  /!** 已收货金额 *!/
  receivedAmount?: string;
  imagesUrls?: string;*/
}

/** 交货计划返回对象  */
export interface DeliveryScheduleVOList {
    /** 订单批次主键id */
    orderBatchId?: string;
    /** 订单id */
    orderId?: string;
    /** 订单编码 */
    orderCode?: string;
    /** 计划交货日期 */
    planDate?: string;
    /** 交付批次 */
    batchCode?: string;
    /** 状态（0-草稿 1-待审核 2-备货中 3-配送中 4-已完成 5-已关闭 ）*/
    batchStatus?: number;
    batchStatusStr?: string;
    /** 计划交货日期 */
    planDate?: string;
    /** 计划交货日期开始时间 */
    planDateStart?: string;
    /** 计划交货日期结束时间 */
    planDateEnd?: string;
    /** 生成时间 */
    createTime?: string;
    /** 配送时间 */
    deliveryTime?: string;
    /** 完成时间 */
    completionTime?: string;
    /** 交货计划 */
    deliveryScheduleDetailVOS?: DeliveryScheduleDetailVOS[];
}

/** 操作日志返回对象 */
export interface LogList {
  /** ID */
  id?: string;
  /** 操作时间 */
  createTime?: string;
  /** 操作类型 */
  operationName?: string;
  /** 操作人 */
  createUserName?: string;
}
/** 交货计划VOS */
export interface DeliveryScheduleDetailVOS {
  /** ID */
  id?: string;
  /** 商品名称 */
  productName?: string;
  /** 规格 */
  productSpecs?: string;
  /** 到货数量 */
  productQty?: string;
  /** 销售价 */
  saleAmount?: string;
  /** 合计 */
  totalSaleAmount?: string;
}

export interface ErrorOrderFrom{
  /** 订单id */
  orderId?: string;
  /** 备注 */
  remark?: string;
}

