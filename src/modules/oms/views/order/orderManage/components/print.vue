<template>
  <NewPrint ref="printRef">
    <div class="box-title">
      {{ printData.title }}
    </div>
    <div>
      <div class="grad-row-print">
        <!-- 客户 -->
        <div class="el-form-item--div">
          <span class="el-form-item__label">{{$t('omsOrder.label.customerNameCopy')}}：</span>
          <span class="el-form-item__content">{{printData.customerName}}</span>
        </div>
        <!-- 销售人员 -->
        <div class="el-form-item--div">
          <span class="el-form-item__label">{{$t('omsOrder.label.salesName')}}：</span>
          <span class="el-form-item__content"> {{printData.salesName}}</span>
        </div>
        <!-- 销售类型 -->
        <div class="el-form-item--div">
          <span class="el-form-item__label">{{$t('omsOrder.label.orderType')}}：</span>
          <span class="el-form-item__content"> {{printData.orderTypeName}}</span>
        </div>
        <!-- 销售单号 -->
        <div class="el-form-item--div">
          <span class="el-form-item__label">{{$t('omsOrder.label.orderTypeCode')}}：</span>
          <span class="el-form-item__content">{{printData.orderCode}}</span>
        </div>
        <!-- 结算方式 -->
        <div class="el-form-item--div">
          <span class="el-form-item__label">{{$t('omsOrder.label.paymentType')}}：</span>
          <span class="el-form-item__content"> {{printData.paymentType}}</span>
        </div>
      </div>
    </div>
    <table class="print-table">
      <thead>
      <tr>
        <th style="width: 180px">{{$t('omsOrder.label.productName')}}</th>
        <th style="width: 80px">{{$t('omsOrder.label.unit')}}</th>
        <th style="width: 90px">{{$t('omsOrder.label.count')}}</th>
        <th>{{$t('omsOrder.label.price')}}</th>
        <th>{{$t('omsOrder.label.totalAmount')}}</th>
        <th style="width: 180px">{{$t('omsOrder.label.remark')}}</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(item, index) in printData.orderDetailList" :key="index">
        <td style="width: 180px;text-align: center">{{ item.productName }}</td>
        <td style="width: 80px;text-align: center">{{ item.productUnitName }}</td>
        <td style="width: 90px;text-align: center">{{ item.productQty }}</td>
        <td style="text-align: center">{{ item.salePrice }}</td>
        <td style="text-align: center">{{ item.saleAmount }}</td>
        <td style="width: 180px">{{ item.remark }}</td>
      </tr>
      <tr>
        <td colspan="2" style="text-align: left !important;">{{$t('omsOrder.label.totalAmounts')}}：{{printData.totalSaleAmount}}</td>
        <td colspan="3" style="text-align: left !important;">{{$t('omsOrder.label.totalCount')}}：{{printData.totalQty}}</td>
        <td style="text-align: left !important;">{{$t('omsOrder.label.discount')}}：{{printData.totalDiscountAmount}}</td>
      </tr>
      <tr>
        <td colspan="1" style="text-align: left !important;">{{$t('omsOrder.label.receiveName')}}：{{printData.contactPerson}}</td>
        <td colspan="2" style="text-align: left !important;word-break: break-all">{{$t('omsOrder.label.phone')}}：{{printData.customerMobile}}</td>
        <td colspan="3" style="text-align: left !important;">{{$t('omsOrder.label.deliveryAddressCopy')}}：{{printData.addressFormat}}</td>
      </tr>
      </tbody>
    </table>
    <div class="print-info">
      <div class="info-row">
        <div class="info-item-2">
            <span class="info-label" style="margin-left: 15px">{{$t('omsOrder.label.createOrderName')}}：</span>
          <span class="info-value">{{ printData.submitter}}</span>
        </div>
        <div class="info-item-2">
            <span class="info-label">{{$t('omsOrder.label.approveUser')}}：</span>
          <span class="info-value">{{ printData.approveUserName}}</span>
        </div>
      </div>
    </div>
  </NewPrint>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PrintTemplate from "@/core/components/Print/PrintTemplate.vue";
import NewPrint from "@/core/components/NewPrint/index.vue";
const { t } = useI18n();

const printData = ref<any>({});
const printRef = ref<InstanceType<typeof PrintTemplate>>();
// 暴露打印方法给父组件
const handlePrint = (data: any) => {
  printData.value = data;
  nextTick(() => {
    printRef.value?.onPrint();
  });
};

defineExpose({
  handlePrint,
});
</script>

<style scoped lang="scss">

  .box-title {
    color: #000000;
    text-align: center !important;
    margin-top: 24px;
    margin-bottom: 14px;
    font-weight: 600 !important;
    font-size: 18px !important;
  }
  .barcode {
    text-align: center;
    margin-bottom: 16px;
  }

  .grad-row-print {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    align-items: flex-start;

    .el-form-item--div {
      flex-basis: calc(33.33% - 10px);
      display: flex;
      align-items: flex-start;
    }

    .el-form-item__label {
      color: #000000;
      font-weight: 500;
      font-size: 12px;
      font-style: normal;
      width: 80px;
      line-height: 16px;
      padding-top: 0;
    }

    .el-form-item__content {
      color: #000000;
      font-weight: 500;
      font-size: 12px;
      word-break: break-all;
      line-height: 16px;
      padding-right: 8px;
    }
  }
  .print-info {
    margin-top: 10px;

    .info-row {
      display: flex;
      margin-bottom: 8px;
      flex-wrap: wrap;
      page-break-inside: avoid;

      .info-item-2 {
        width: 50% !important;
        display: flex;
        margin-bottom: 5px;

        .info-label {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: #000000 !important;
          line-height: 26px;
          text-align: left;
          font-style: normal;
        }

        .info-value {
          flex: 1;
          padding-right: 8px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: #000000 !important;
          line-height: 26px;
          text-align: left;
          font-style: normal;
          word-break: break-word;
        }
      }
    }
  }
  .print-table {
    margin-top: 10px;
    border: 1px solid #000000 !important;
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed;
    -webkit-print-color-adjust: exact;
  }


  table,
  th,
  td {
    color: #000000;
    border: 1px solid #000000;
  }

  th,
  td {
    padding: 14px 12px;
    word-break: break-word;
    font-size: 12px;
    font-weight: 500;
  }



</style>
