<template>
  <div class="search-container">
    <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
      <el-form-item :label="t(`omsFinance.label.collectionTime`)" prop="dateRange">
        <el-date-picker
          :editable="false"
          class="!w-[360px]"
          v-model="queryParams.dateRange"
          type="datetimerange"
          :range-separator="$t('omsFinance.label.dateTimeRangeTo')"
          :start-placeholder="$t('omsFinance.label.startDate')"
          :end-placeholder="$t('omsFinance.label.endDate')"
          value-format="YYYY-MM-DD HH:mm:ss"
          :placeholder="$t('common.placeholder.selectTips')"
          :prefix-icon="Calendar"
        />
      </el-form-item>
      <el-form-item :label="t(`omsFinance.label.collectionUser`)" prop="createUserName">
        <el-input
          v-model="queryParams.createUserName"
          :placeholder="$t('common.placeholder.inputTips')"
          clearable
          maxlength="30"
          class="!w-[256px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery(true)">{{$t('common.search')}}</el-button>
        <el-button  @click="handleResetQuery">{{$t('common.reset')}}</el-button>
      </el-form-item>
    </el-form>
  </div>
    <el-table ref="tableRef" v-loading="loading" :data="collectionRecordList" show-summary :summary-method="getSummaries"  @sort-change="handleSortChange" max-height="700" highlight-current-row stripe>
      <template #empty>
        <Empty/>
      </template>
      <!--收款时间-->
      <el-table-column :label="$t('omsFinance.label.collectionTime')" prop="collectionTime" show-overflow-tooltip min-width="120">
        <template #default="scope">
          <span v-if="scope.row.collectionTime">{{ parseDateTime(scope.row.collectionTime, "dateTime") }}</span>
        </template>
      </el-table-column>
      <!-- 收款金额-->
      <el-table-column :label="$t('omsFinance.label.receivableAmountLabel') + currencyCodeLabel" prop="collectionAmount" align="right" :sortable="'custom'" show-overflow-tooltip min-width="140">
        <template #default="scope">
          <span style="color: var(--el-color-danger)">{{ formatPrice(scope.row.collectionAmount) }}</span>
        </template>
      </el-table-column>
      <!-- 收款方式-->
      <el-table-column :label="$t('omsFinance.label.collectionMethod')" prop="collectionTypeName" show-overflow-tooltip min-width="170"></el-table-column>
      <!--收款凭证-->
      <el-table-column :label="$t('omsFinance.label.collectionVoucher')" prop="collectionVoucher" show-overflow-tooltip min-width="170">
        <template #default="scope">
          <span style="color: var(--el-color-primary); cursor: pointer" @click="previewHandler(scope.row.collectionVoucher)">{{ $t('omsFinance.button.look') }}</span>
        </template>
      </el-table-column>
      <!-- 收款人-->
      <el-table-column :label="$t('omsFinance.label.collectionUser')" prop="createUserName" show-overflow-tooltip min-width="150"></el-table-column>
      <!--  备注-->
      <el-table-column :label="$t('omsFinance.label.collectionRemark')" prop="remark" show-overflow-tooltip min-width="220"></el-table-column>
    </el-table>
    <pagination
      v-if="total > 0"
      v-model:total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.limit"
      @pagination="handleQuery"
    />
    <!--预览-->
    <ImageViewer v-model:visible="previewVisible" :imagesList="previewImgData" />
</template>

<script setup lang="ts">
import { convertToTimestamp, formatPrice, parseDateTime } from "@/core/utils";
import FinanceAPI, {
  CollectionRecordQuery,
  CollectionRecordVO,
  FinanceSummary, PreviewPrivateImage,
  SortInfo,
  SortTableData,
} from "@/modules/oms/api/finance";
import type { FormInstance, TableColumnCtx, TableInstance } from "element-plus";
import { Calendar } from '@element-plus/icons-vue';
import ImageViewer from "@/core/components/ImageViewer/index.vue";

const props = defineProps({
  receivableAccountId: {
    type: String,
    default: ''
  },
})

const loading = ref(false);
const queryFormRef = ref<FormInstance>();
const tableRef = ref<TableInstance>();
const { t } = useI18n();

const queryParams = reactive<CollectionRecordQuery>({
  page: 1,
  limit: 20,
  dateRange: [],
  receivableAccountId: props?.receivableAccountId
});
const total = ref(0);


const collectionRecordList = ref<CollectionRecordVO[]>([]);
const collectionListData = reactive<FinanceSummary>({
  receivableAmount: 0,
})

/*获取金额单位*/
const currencyCodeLabel = computed(() => {
  let defaultCurrencyCode = '(￥)';
  if(collectionRecordList.value?.length){
    defaultCurrencyCode = collectionRecordList.value?.[0]?.currencyCode === 'CNY' ? '(￥)' : '($)'
  }
  return defaultCurrencyCode
})

const sortInfo = reactive<SortInfo>({
  sortColumn: '',
  sortType: '',
});

/*获取收款记录 fetch*/
const queryListData = async () => {
  let params = {
    ...queryParams,
    ...sortInfo,
  }
  if(params?.dateRange?.length){
    params.startTime = convertToTimestamp(params?.dateRange[0]);
    params.endTime = convertToTimestamp(params?.dateRange[1]);
  }
  return await FinanceAPI.getCollectionRecord(params)
};

/*获取汇总 fetch*/
const querySummary = async () => {
  let params = {
    ...queryParams,
    ...sortInfo,
  }
  if(params?.dateRange?.length){
    params.startTime = convertToTimestamp(params?.dateRange[0]);
    params.endTime = convertToTimestamp(params?.dateRange[1]);
  }
  return await FinanceAPI.getFCollectionRecordSummary(params)
};

/*插叙列表数据*/
const handleQuery = async (clearSortFlag = false) => {
  loading.value = true;
  if (clearSortFlag && tableRef.value) {
    tableRef.value.clearSort();
    clearSortConfig();
  }
  try {
    const [listData,summaryData] = await Promise.all([queryListData(),querySummary()])
    collectionRecordList.value = listData?.records?.map(item => {
      return {
        ...item,
        collectionVoucher: JSON.parse(item?.collectionVoucher)
      }
    }) || [];
    total.value = parseInt(listData?.total);
    collectionListData.receivableAmount = summaryData?.totalCollectionAmount;
  } catch (error){
    ElMessage.error(t("omsFinance.message.loadFail"));
  }finally {
    loading.value = false;
  }
};

/*重置*/
const handleResetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery(true);
}

/*清除排序*/
const clearSortConfig = () => {
  sortInfo.sortColumn = '';
  sortInfo.sortType = '';
}

interface SummaryMethodProps<T = CollectionRecordVO> {
  columns: TableColumnCtx<T>[]
  data: T[]
}

/*汇总处理*/
const getSummaries = (param: SummaryMethodProps) => {
  const { columns } = param
  const sums: string[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h('span', {
        innerHTML: `<span style="font-weight: bold">${t('omsFinance.label.totalSum')}:</span>`
      })
      return
    }else if(column.property === 'collectionAmount'){
      sums[index] = h('span', {
        innerHTML: `<span style="color: var(--el-color-danger);font-weight: bold">${formatPrice(collectionListData?.receivableAmount)}</span>`
      })
      return
    }else {
      sums[index] = h('span', { innerHTML: '' })
      return
    }
  })
  return sums
}

/*排序查询*/
const handleSortChange = (sortData: SortTableData) => {
  if(sortData.prop && sortData.order){
    sortInfo.sortColumn = sortData.prop;
    sortInfo.sortType = sortData.order === 'ascending';
    handleQuery()
  }
}

const previewVisible = ref(false)
const previewImgData = ref<PreviewPrivateImage[]>([]);
/*凭证预览*/
const previewHandler = async(fileData: PreviewPrivateImage[]) => {
  previewImgData.value = fileData || [];
  previewVisible.value = true;
}

onMounted(() => {
  handleQuery();
})

defineExpose({
  reload: handleQuery
})
</script>

<style scoped lang="scss">
.search-container {
  padding-left: 0;
}
</style>
