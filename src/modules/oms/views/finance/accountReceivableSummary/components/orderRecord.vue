<template>
  <div class="search-container">
    <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
      <el-form-item :label="t(`omsFinance.label.orderCreateTime`)" prop="dateRange">
        <el-date-picker
          :editable="false"
          class="!w-[360px]"
          v-model="queryParams.dateRange"
          type="datetimerange"
          :range-separator="$t('omsFinance.label.dateTimeRangeTo')"
          :start-placeholder="$t('omsFinance.label.startDate')"
          :end-placeholder="$t('omsFinance.label.endDate')"
          value-format="YYYY-MM-DD HH:mm:ss"
          :placeholder="$t('common.placeholder.selectTips')"
          :prefix-icon="Calendar"
        />
      </el-form-item>
      <el-form-item :label="t(`omsFinance.label.orderCode`)" prop="orderCode">
        <el-input
          v-model="queryParams.orderCode"
          :placeholder="$t('common.placeholder.inputTips')"
          clearable
          maxlength="30"
          class="!w-[256px]"
        />
      </el-form-item>
      <el-form-item v-if="isBigCustomer" :label="t(`omsFinance.label.salesName`)" prop="salesName">
        <el-input
          v-model="queryParams.salesName"
          :placeholder="$t('common.placeholder.inputTips')"
          clearable
          maxlength="30"
          class="!w-[256px]"
        />
      </el-form-item>
      <el-form-item :label="t(`omsFinance.label.orderTheme`)" prop="orderTheme">
        <el-input
          v-model="queryParams.orderTheme"
          :placeholder="$t('common.placeholder.inputTips')"
          clearable
          maxlength="50"
          class="!w-[256px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery(true)">{{$t('common.search')}}</el-button>
        <el-button @click="handleResetQuery">{{$t('common.reset')}}</el-button>
      </el-form-item>
    </el-form>
  </div>
  <el-table
    ref="tableRef"
    v-loading="loading"
    :data="orderRecordList"
    show-summary
    :summary-method="getSummaries"
    @sort-change="handleSortChange"
    max-height="700"
    highlight-current-row stripe>
    <template #empty>
      <Empty/>
    </template>
    <!--订单号-->
    <el-table-column :label="$t('omsFinance.label.orderCode')" prop="orderCode" show-overflow-tooltip min-width="170"></el-table-column>
    <!--销售主题-->
    <el-table-column :label="$t('omsFinance.label.orderTheme')" prop="orderTheme" show-overflow-tooltip min-width="170"></el-table-column>
    <!--下单时间-->
    <el-table-column :label="$t('omsFinance.label.orderCreateTime')" prop="submitterTime" show-overflow-tooltip min-width="170">
      <template #default="scope">
        <span v-if="scope.row.submitterTime">{{ parseDateTime(scope.row.submitterTime, "dateTime") }}</span>
      </template>
    </el-table-column>
   <!--销售人员-->
    <el-table-column v-if="isBigCustomer" :label="$t('omsFinance.label.salesName')" prop="salesName" show-overflow-tooltip min-width="170"></el-table-column>
   <!--订单金额-->
    <el-table-column :label="$t('omsFinance.label.orderAmountTitle') + currencyCodeLabel" prop="orderAmount" align="right" :sortable="'custom'" show-overflow-tooltip min-width="170">
      <template #default="scope">
        {{ scope.row.orderAmount }}
      </template>
    </el-table-column>
    <!--售后金额-->
    <el-table-column :label="$t('omsFinance.label.returnAmountTitle') + currencyCodeLabel" prop="returnAmount" align="right" :sortable="'custom'" show-overflow-tooltip min-width="170">
      <template #default="scope">
        {{ scope.row.returnAmount }}
      </template>
    </el-table-column>
   <!--应收金额-->
    <el-table-column :label="$t('omsFinance.label.receivableAmountTitle') + currencyCodeLabel" prop="receivableAmount" align="right" :sortable="'custom'" show-overflow-tooltip min-width="170">
      <template #default="scope">
        <span style="color: var(--el-color-danger)"> {{ formatPrice(scope.row.receivableAmount) }}</span>
      </template>
    </el-table-column>
    <el-table-column fixed="right" :label="$t('common.handle')" width="170">
      <template #default="scope">
        <el-button type="primary" link @click="detailHandler(scope.row.orderId,scope.row)">{{ $t('omsFinance.button.look') }}</el-button>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-if="total > 0"
    v-model:total="total"
    v-model:page="queryParams.page"
    v-model:limit="queryParams.limit"
    @pagination="handleQuery"
  />
</template>

<script setup lang="ts">
import { convertToTimestamp, formatPrice, parseDateTime } from "@/core/utils";
import {useRoute, useRouter} from "vue-router";
import type { FormInstance, TableColumnCtx, TableInstance } from "element-plus";
import FinanceAPI, {
  orderRecordQuery, OrderRecordVO, OrderSummary, SortInfo, SortTableData,
} from "@/modules/oms/api/finance";
import { Calendar } from "@element-plus/icons-vue";

const props = defineProps({
  receivableAccountId: {
    type: String,
    default: ''
  },
  attributesType: {
    type: String,
    default: '1'
  }
})

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const loading = ref(false);
const queryFormRef = ref<FormInstance>();
const tableRef = ref<TableInstance>();

const isBigCustomer = computed(() => props?.attributesType == '1')

const queryParams = reactive<orderRecordQuery>({
  page: 1,
  limit: 20,
  dateRange: [],
  receivableAccountId: props?.receivableAccountId
});
const total = ref(0);
const orderRecordList = ref<OrderRecordVO[]>([]);
const orderListData = reactive<OrderSummary>({
  receivableAmount: 0,
  orderAmount: 0,
  returnAmount: 0,
})

/*获取金额单位*/
const currencyCodeLabel = computed(() => {
  let defaultCurrencyCode = '(￥)';
  if(orderRecordList.value?.length){
    defaultCurrencyCode = orderRecordList.value?.[0]?.currencyCode === 'CNY' ? '(￥)' : '($)'
  }
  return defaultCurrencyCode
})

const sortInfo = reactive<SortInfo>({
  sortColumn: '',
  sortType: '',
});

/*获取订单列表*/
const queryListData = async () => {
  let params = {
    ...queryParams,
    ...sortInfo,
  }
  if(params?.dateRange.length > 0){
    params.orderCreateTimeStar = convertToTimestamp(params?.dateRange[0]);
    params.orderCreateTimeEnd = convertToTimestamp(params?.dateRange[1]);
  }
  return await FinanceAPI.getOrderRecord(params)
};

/*获取汇总fetch*/
const querySummary = async () => {
  let params = {
    ...queryParams,
    ...sortInfo,
  }
  if(params?.dateRange.length > 0){
    params.orderCreateTimeStar = convertToTimestamp(params?.dateRange[0]);
    params.orderCreateTimeEnd = convertToTimestamp(params?.dateRange[1]);
  }
  return await FinanceAPI.getOrderSummary(params)
};

/*列表数据查询*/
const handleQuery = async (clearSortFlag = false) => {
  loading.value = true;
  if (clearSortFlag && tableRef.value) {
    tableRef.value.clearSort();
    clearSortConfig()
  }
  try {
    const [listData,summaryData] =await Promise.all([queryListData(),querySummary()])
    orderRecordList.value = listData?.records || [];
    total.value = parseInt(listData?.total);
    orderListData.receivableAmount = summaryData?.receivableAmount;
    orderListData.orderAmount = summaryData?.orderAmount;
    orderListData.returnAmount = summaryData?.returnAmount;
  } catch (error){
    ElMessage.error(t("omsFinance.message.loadFail"));
  }finally {
    loading.value = false;
  }
};

/*重置*/
const handleResetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery(true);
}

interface SummaryMethodProps<T = OrderRecordVO> {
  columns: TableColumnCtx<T>[]
  data: T[]
}

/*汇总处理*/
const getSummaries = (param: SummaryMethodProps) => {
  const { columns } = param
  const sums: string[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h('span', {
        innerHTML: `<span style="font-weight: bold">${t('omsFinance.label.totalSum')}:</span>`
      })
      return
    }else if(column.property === 'orderAmount'){
      sums[index] = h('span', {
        innerHTML: `<span style="font-weight: bold">${formatPrice(orderListData?.orderAmount)}</span>`
      })
      return
    }else if(column.property === 'returnAmount'){
      sums[index] = h('span', {
        innerHTML: `<span style="font-weight: bold">${formatPrice(orderListData?.returnAmount)}</span>`
      })
      return
    }else if(column.property === 'receivableAmount'){
      sums[index] = h('span', {
        innerHTML: `<span style="color: var(--el-color-danger);font-weight: bold">${formatPrice(orderListData?.receivableAmount)}</span>`
      })
      return
    }else {
      sums[index] = h('span', { innerHTML: '' })
      return
    }
  })
  return sums
}

/*排序查询*/
const handleSortChange = (sortData: SortTableData) => {
  if(sortData.prop && sortData.order){
    sortInfo.sortColumn = sortData.prop;
    sortInfo.sortType = sortData.order === 'ascending';
    handleQuery()
  }
}

/*重置*/
const clearSortConfig = () => {
  sortInfo.sortColumn = '';
  sortInfo.sortType = '';
}

/*跳转订单详情*/
const detailHandler = (id: number, rowData) => {
  router.push({
    path: "/oms/order/orderDetail",
    query: {
      id: id,
      orderCode: rowData?.orderCode,
      type: 'detail',
      title: t('omsFinance.label.orderDetail'),
    }
  });
}

onMounted(() => {
  handleQuery();
})
</script>
<style scoped lang="scss">
.search-container {
  padding-left: 0;
}
</style>

