<template>
  <div class="finance-container">
    <div>
      <el-card shadow="never" class="table-container">
        <el-tabs v-model="activeName" class="finance-tabs" @tab-click="handleTabClick">
          <el-tab-pane :label=" t(`omsFinance.label.bigCustomer`)" name="bigCustomer"></el-tab-pane>
          <el-tab-pane :label="t(`omsFinance.label.smallCustomer`)" name="smallCustomer"></el-tab-pane>
        </el-tabs>
        <div class="search-container">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
          <el-form-item v-if="isBigCustomer" :label="t(`omsFinance.label.customerName`)" prop="customerName">
            <el-input
              v-model="queryParams.customerName"
              :placeholder="$t('common.placeholder.inputTips')"
              clearable
              maxlength="30"
              class="!w-[256px]"
            />
          </el-form-item>
          <el-form-item v-if="isBigCustomer" :label="t(`omsFinance.label.customerType`)" prop="customerTypeCode">
            <el-select
              v-model="queryParams.customerTypeCode"
              :placeholder="$t('common.placeholder.selectTips')"
              clearable
              class="!w-[256px]"
            >
              <el-option v-for="item in customerTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="!isBigCustomer" :label="t(`omsFinance.label.salesName`)" prop="salesName">
            <el-input
              v-model="queryParams.salesName"
              :placeholder="$t('common.placeholder.inputTips')"
              clearable
              maxlength="20"
              class="!w-[256px]"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" v-hasPerm="['oms:finance:accountReceivableSummary:search']" @click="handleQuery(true)">{{$t('common.search')}}</el-button>
            <el-button v-hasPerm="['oms:finance:accountReceivableSummary:reset']" @click="handleResetQuery">{{$t('common.reset')}}</el-button>
          </el-form-item>
        </el-form>
        </div>
        <el-table ref="tableRef" v-loading="loading" max-height="calc(100vh - 325px)" :data="financeDataList" show-summary :summary-method="getSummaries" @sort-change="handleSortChange" highlight-current-row stripe>
          <template #empty><Empty/></template>
          <!--客户名称-->
          <el-table-column v-if="isBigCustomer" fixed="left" :label="$t('omsFinance.label.customerName')" prop="customerName" show-overflow-tooltip min-width="170"></el-table-column>
          <!--客户编码-->
          <el-table-column v-if="isBigCustomer" :label="$t('omsFinance.label.customerCode')" prop="businessCode" show-overflow-tooltip min-width="170"></el-table-column>
          <!--客户类型-->
          <el-table-column v-if="isBigCustomer" :label="$t('omsFinance.label.customerType')" prop="customerTypeName" show-overflow-tooltip min-width="170"></el-table-column>
          <!--销售人员-->
          <el-table-column v-if="!isBigCustomer" :label="$t('omsFinance.label.salesName')" prop="salesName" show-overflow-tooltip min-width="170"></el-table-column>
          <!--应收金额-->
          <el-table-column :label="$t('omsFinance.label.receivableAmount') + currencyCodeLabel" prop="receivableAmount" show-overflow-tooltip :sortable="'custom'" min-width="170">
            <template #default="scope">
              <span style="color: var(--el-color-danger)">{{ formatPrice(scope.row.receivableAmount) }}</span>
            </template>
          </el-table-column>
          <!--已收金额  -->
          <el-table-column :label="$t('omsFinance.label.actualReceivedAmount') + currencyCodeLabel" prop="actualReceivedAmount" show-overflow-tooltip :sortable="'custom'" min-width="170">
            <template #default="scope">
              {{ formatPrice(scope.row.actualReceivedAmount) }}
            </template>
          </el-table-column>
          <!--剩余金额-->
          <el-table-column :label="$t('omsFinance.label.remainingReceivableAmount') + currencyCodeLabel" prop="remainingReceivableAmount" show-overflow-tooltip :sortable="'custom'" min-width="170">
            <template #default="scope">
              {{ formatPrice(scope.row.remainingReceivableAmount) }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="$t('common.handle')" width="150">
            <template #default="scope">
              <el-button v-hasPerm="['oms:finance:accountReceivableSummary:detail']" type="primary" link @click="detailHandler(scope.row.id,scope.row)">
                {{$t('omsFinance.button.look')}}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handleQuery"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TabsPaneContext, TableColumnCtx, TableInstance, FormInstance } from 'element-plus';
import FinanceAPI, {
  FinancePageVO,
  FinancePageQuery,
  FinanceSummary,
  SortTableData, SortInfo,
} from "@/modules/oms/api/finance";
import { formatPrice } from "@/core/utils";
import {useRouter} from "vue-router";

defineOptions({
  name: "AccountReceivableSummary",
  inheritAttrs: false,
})
const router = useRouter();
const { t } = useI18n();
const activeName = ref<string>('bigCustomer')
const customerTypeList = ref([
  {
    label: t('omsFinance.label.enterpriseCustomer'),
    value: 1
  },
  {
    label: t('omsFinance.label.governmentCustomer'),
    value: 2
  },
  {
    label: t('omsFinance.label.individualCustomer'),
    value: 3
  },
])
const total = ref(0);
const tableRef = ref<TableInstance>();
const loading = ref(false);
const financeDataList = ref<FinancePageVO[]>([]);
const summaryListData = reactive<FinanceSummary>({
  receivableAmount: 0,
  actualReceivedAmount: 0,
  remainingReceivableAmount: 0,
})
const queryFormRef = ref<FormInstance>();
const sortInfo = reactive<SortInfo>({
  sortColumn: '',
  sortType: '',
});

const queryParams = reactive<FinancePageQuery>({
  attributesType: 1,
  page: 1,
  limit: 20,
});

const isBigCustomer = computed(() => {
  return activeName.value === 'bigCustomer'
})

/*获取金额单位*/
const currencyCodeLabel = computed(() => {
  let defaultCurrencyCode = '(￥)';
  if(financeDataList.value?.length){
    defaultCurrencyCode = financeDataList.value?.[0]?.currencyCode === 'CNY' ? '(￥)' : '($)'
  }
  return defaultCurrencyCode
})

/*tab切换*/
const handleTabClick = () => {
  queryParams.attributesType = activeName.value === 'bigCustomer' ? 2 : 1;
  handleQuery()
}

interface SummaryMethodProps<T = FinancePageVO> {
  columns: TableColumnCtx<T>[]
  data: T[]
}

/*汇总处理*/
const getSummaries = (param: SummaryMethodProps) => {
  const { columns } = param
  const sums: string[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h('span', {
        innerHTML: `<span style="font-weight: bold">${t('omsFinance.label.totalSum')}:</span>`
      })
      return
    }else if(column.property === 'receivableAmount'){
      sums[index] = h('span', {
        innerHTML: `<span style="color: var(--el-color-danger);font-weight: bold">${formatPrice(summaryListData?.receivableAmount)}</span>`
      })
      return
    }else if(column.property === 'actualReceivedAmount'){
      sums[index] = h('span', {
        innerHTML: `<span style="font-weight: bold">${formatPrice(summaryListData?.actualReceivedAmount)}</span>`
      })
      return
    } else if(column.property === 'remainingReceivableAmount'){
      sums[index] = h('span', {
        innerHTML: `<span style="font-weight: bold">${formatPrice(summaryListData?.remainingReceivableAmount)}</span>`
      })
      return
    }else {
      sums[index] = h('span', { innerHTML: '' })
      return
    }
  })
  return sums
}

/*排序查询*/
const handleSortChange = (sortData: SortTableData) => {
  if(sortData.prop && sortData.order){
    sortInfo.sortColumn = sortData.prop;
    sortInfo.sortType = sortData.order === 'ascending';
    handleQuery()
  }
}

/*fetch 列表*/
const queryListData = async () => {
  let params = {
    ...queryParams,
    ...sortInfo,
  }
  return await FinanceAPI.getBigCustomerBillSummary(params)
};

/*fetch 汇总*/
const querySummary = async () => {
  let params = {
    ...queryParams,
    ...sortInfo,
  }
  return await FinanceAPI.getFinanceSummary(params)
};

/*查询列表*/
const handleQuery = async (clearSortFlag = false) => {
  loading.value = true;
  if (clearSortFlag && tableRef.value) {
    tableRef.value.clearSort();
    clearSortConfig()
  }
  try {
    const [listData,summaryData] = await Promise.all([queryListData(),querySummary()])
    financeDataList.value = listData?.records || [];
    total.value = parseInt(listData?.total);
    summaryListData.receivableAmount = summaryData?.receivableAmount;
    summaryListData.actualReceivedAmount = summaryData?.actualReceivedAmount;
    summaryListData.remainingReceivableAmount = summaryData?.remainingReceivableAmount;
  } catch (error){
    ElMessage.error(t("omsFinance.message.loadFail"));
  }finally {
    loading.value = false;
  }
};

/*重置*/
const handleResetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery(true);
}

/*清除排序字段*/
const clearSortConfig = () => {
  sortInfo.sortColumn = '';
  sortInfo.sortType = '';
}

/*详情*/
const detailHandler = (id: number, rowData: any) => {
  router.push({
    path: "/oms/finance/accountReceivableSummaryDetail",
    query: {
      id: id,
      attributesType: isBigCustomer.value ? 1 : 2,
      name: isBigCustomer.value ? rowData.customerName : rowData.salesName,
    }
  });
}
onActivated(() => {
  handleQuery();
})
</script>

<style lang="scss" scoped>
:deep(.el-tabs__item) {
  width: 120px;
}
.search-container {
  padding-left: 0;
}
</style>
