<template>
  <div class="app-container">
    <div class="search-container">
      <el-form
        ref="queryFormRef"
        :model="queryParams"
        :inline="true"
        label-width="84px"
      >
        <!-- 客户名称 -->
        <el-form-item
          prop="customerName"
          :label="$t('omsCustomer.label.customerName')"
        >
          <el-input
            v-model="queryParams.customerName"
            :placeholder="$t('omsCustomer.placeholder.customerNameTips')"
            clearable
            maxlength="50"
            class="!w-[256px]"
          />
        </el-form-item>
        <!-- 客户类型 -->
        <el-form-item
          :label="$t('omsCustomer.label.customerType')"
          prop="customerTypeCode"
        >
          <el-select
            v-model="queryParams.customerTypeCode"
            clearable
            :placeholder="$t('common.placeholder.selectTips')"
            class="!w-[256px]"
          >
            <el-option
              v-for="item in customerTypeList"
              :key="item.statusId"
              :label="item.statusName"
              :value="item.statusId"
            />
          </el-select>
        </el-form-item>
        <!-- 手机号 -->
        <el-form-item
          prop="contactMobile"
          :label="$t('omsCustomer.label.phone')"
        >
          <el-input
            v-model="queryParams.contactMobile"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            class="!w-[256px]"
          />
        </el-form-item>
        <!-- 状态 -->
        <el-form-item
          :label="$t('omsCustomer.label.status')"
          prop="enableStatus"
        >
          <el-select
            v-model="queryParams.enableStatus"
            clearable
            :placeholder="$t('common.placeholder.selectTips')"
            class="!w-[256px]"
          >
            <el-option
              v-for="item in statusList"
              :key="item.statusId"
              :label="item.statusName"
              :value="item.statusId"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleQuery"
            v-hasPerm="['oms:customer:search']"
          >
            {{ $t("common.search") }}
          </el-button>
          <el-button
            @click="handleResetQuery"
            v-hasPerm="['oms:customer:reset']"
          >
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          type="primary"
          @click="AddCustomer(undefined, 'add')"
          v-hasPerm="['oms:customer:add']"
        >
          {{ $t("omsCustomer.button.addCustomer") }}
        </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="tableData"
        highlight-current-row
        stripe
      >
        <template #empty>
          <Empty />
        </template>
        <!-- 客户编码 -->
        <el-table-column
          :label="$t('omsCustomer.label.customerCode')"
          prop="customerCode"
        />
        <!-- 客户名称 -->
        <el-table-column
          :label="$t('omsCustomer.label.customerName')"
          prop="customerName"
        />
        <!-- 客户类型 -->
        <el-table-column
          prop="customerTypeCode"
          :label="$t('omsCustomer.label.customerType')"
        >
          <!-- 客户类型编码：1、企业客户 2、政府客户 3、个体客户 -->
          <template #default="scope">
            <span v-if="scope.row.customerTypeCode == 1">
              {{ $t("omsCustomer.customerTypeList.enterpriseCustomers") }}
            </span>
            <span v-else-if="scope.row.customerTypeCode == 2">
              {{ $t("omsCustomer.customerTypeList.governmentClients") }}
            </span>
            <span v-else-if="scope.row.customerTypeCode == 3">
              {{ $t("omsCustomer.customerTypeList.individualCustomers") }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <!-- 国家地区 -->
        <el-table-column :label="$t('omsCustomer.label.countryAndArea')">
          <template #default="scope">
            {{ scope.row.receiverCountryName }}
            {{ scope.row.receiverProvinceName }}
            {{ scope.row.receiverCityName }}
            {{ scope.row.receiverDistrictName }}
          </template>
        </el-table-column>
        <!-- 联系人 -->
        <el-table-column
          :label="$t('omsCustomer.label.contactsPerson')"
          min-width="70"
        >
          <template #default="scope">
            <div class="encryptBox flex_style">
              <div>
                <div>{{ scope.row.contact }}</div>
                <div>
                  {{ scope.row.contactCountryAreaCode }}
                  {{ scope.row.contactMobile }}
                </div>
              </div>
              <div>
                <el-icon
                  v-if="scope.row.contactMobile || scope.row.contact"
                  @click="
                    scope.row.mobilePhoneShow
                      ? getRealPhone(scope.row.id, scope.$index)
                      : ''
                  "
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['oms:customer:eye']"
                >
                  <component :is="scope.row.mobilePhoneShow ? 'View' : ''" />
                </el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- 状态 -->
        <el-table-column :label="$t('omsCustomer.label.status')" align="center" width="120">
          <template #default="scope">
            <el-switch
              :active-text="$t('common.statusEmun.enable')"
              :inactive-text="$t('common.statusEmun.disable')"
              inline-prompt
              style="
                --el-switch-on-color: #762adb;
                --el-switch-off-color: #cccfd5;
              "
              v-model="scope.row.enableStatus"
              :active-value="1"
              :inactive-value="0"
              @change="handleUpdateStatus(scope.row)"
              v-hasPerm="['oms:customer:updateStatus']"
            />
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('common.handle')" width="300">
          <template #default="scope">
            <el-button
              type="primary"
              link
              v-hasPerm="['oms:customer:details']"
              @click="customerDetail(scope.row.id)"
            >
              {{ $t("common.detailBtn") }}
            </el-button>
            <el-button
              type="primary"
              link
              v-hasPerm="['oms:customer:updateContacts']"
              @click="updateContacts(scope.row)"
            >
              {{ $t("omsCustomer.button.updateContacts") }}
            </el-button>
            <el-button
              type="primary"
              link
              v-hasPerm="['oms:customer:edit']"
              v-if="scope.row.enableStatus == 0"
              @click="AddCustomer(scope.row.id, 'edit')"
            >
              {{ $t("common.edit") }}
            </el-button>

            <el-button
              type="primary"
              link
              v-if="ifShowShare(scope.row)"
              @click="handleShare(scope.row)"
            >
              {{ $t("omsCustomer.button.share") }}
            </el-button>
            <el-button
              type="danger"
              link
              v-hasPerm="['oms:customer:delete']"
              v-if="scope.row.enableStatus == 0"
              @click="handleDelete(scope.row)"
            >
              {{ $t("common.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>
    <!-- 更新联系人弹框 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      width="500px"
      @close="handleCloseDialog"
    >
      <el-form
        ref="roleFormRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        label-position="top"
      >
        <el-form-item
          :label="$t('omsCustomer.label.contactsPerson')"
          prop="contact"
        >
          <el-input
            v-model="formData.contact"
            clearable
            :placeholder="$t('common.placeholder.inputTips')"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item
          :label="$t('omsCustomer.label.mobileNumber')"
          prop="contactCountryAreaCode"
          class="custom-select"
        >
          <el-row style="width: 100%">
            <el-col :span="6">
              <!-- 手机号码 -->
              <el-form-item
                label-width="0"
                prop="contactCountryAreaCode"
                class="custom-select"
              >
                <el-select
                  v-model="formData.contactCountryAreaCode"
                  :placeholder="$t('omsCustomer.label.areaCode')"
                  clearable
                >
                  <el-option
                    v-for="(item, index) in areaList"
                    :key="index"
                    :label="item.internationalCode"
                    :value="item.internationalCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="18">
              <el-form-item prop="contactMobile" label-width="0">
                <el-input
                  v-model="formData.contactMobile"
                  :placeholder="$t('common.placeholder.inputTips')"
                  clearable
                  maxlength="30"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDialog">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="formLoading"
          >
            {{ $t("common.confirm") }}
          </el-button>
        </div>
      </template>
    </el-drawer>
    <ShareList
      ref="shareListRef"
      v-model:dialog-visible="shareDialog.visible"
      :title="shareDialog.title"
      @onSubmit="handleQuery"
    />
  </div>
</template>

<script setup lang="ts">
import ShareList from "./components/shareList.vue";

defineOptions({
  name: "CustomerManagement",
  inheritAttrs: false,
});

import CustomerApi, {
  CustomerListQueryParams,
} from "@/modules/oms/api/customer";
import { useNavigation } from "@/core/composables";
import { useUserStore } from "@/core/store";
import { hasAuth } from "@/core/plugins/permission";

const { refreshAndNavigate } = useNavigation();

const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();

const queryFormRef = ref(ElForm);
const loading = ref(false);
const formLoading = ref(false);
const total = ref(0);
const tableData = ref([]);
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
const shareDialog = reactive({
  title: "",
  visible: false,
});
const shareListRef = ref(null);
const roleFormRef = ref(ElForm);
// 角色表单
const formData = reactive({
  id: "",
  contact: "",
  contactCountryAreaCode: "",
  contactMobile: "",
});
const areaList = ref([]);
const rules = reactive({
  contact: [
    {
      required: true,
      message: t("omsCustomer.rules.contactTip"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("omsCustomer.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  contactCountryAreaCode: [
    {
      required: true,
      message: t("omsCustomer.rules.countryAreaCodeTip"),
      trigger: "change",
    },
  ],
  contactMobile: [
    {
      required: true,
      message: t("omsCustomer.rules.phoneTip"),
      trigger: "blur",
    },
  ],
});

const ifShowShare = (row) => {
  return row?.isShare == 1 && (userStore?.user?.userType == 'scm_main' || hasAuth(['oms:customer:share']))
}

const queryParams = reactive<CustomerListQueryParams>({
  page: 1,
  limit: 20,
});

const customerTypeList = ref([
  {
    statusId: 1,
    statusName: t("omsCustomer.customerTypeList.enterpriseCustomers"),
  },
  {
    statusId: 2,
    statusName: t("omsCustomer.customerTypeList.governmentClients"),
  },
  {
    statusId: 3,
    statusName: t("omsCustomer.customerTypeList.individualCustomers"),
  },
]);

const statusList = ref([
  {
    statusId: 1,
    statusName: t("common.statusEmun.enable"),
  },
  {
    statusId: 0,
    statusName: t("common.statusEmun.disable"),
  },
]);

/** 查询 */
function handleQuery() {
  loading.value = true;
  CustomerApi.getCustomerList(queryParams)
    .then((data: any) => {
      tableData.value = data.records;
      tableData.value = data.records.map((item: any, index: any) => {
        item.mobilePhoneShow = true;
        return { ...item };
      });
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

function getRealPhone(id: any, index: any) {
  CustomerApi.queryDetail({ id: id })
    .then((data: any) => {
      tableData.value[index].contactMobile = data.contactMobile;
      tableData.value[index].contact = data.contact;
      tableData.value[index].mobilePhoneShow = false;
    })
    .finally(() => {});
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery();
}

/** 新增编辑 */
function AddCustomer(id?: string, type?: string) {
  // let isEdit: any = "true";
  // if (status && status == 1 && type == "edit") {
  //   isEdit = "false";
  // } else {
  //   isEdit = "true";
  // }
  let title;
  if (type == "edit") {
    title = t("omsCustomer.title.editCustomer");
  } else {
    title = t("omsCustomer.title.addCustomer");
  }
  refreshAndNavigate({
    path: "/oms/omsCustomer/addCustomer",
    query: { title: title, id: id, type: type },
  })
}

function customerDetail(id: any) {
  router.push({
    path: "/oms/omsCustomer/customerDetail",
    query: { id: id },
  });
}

// 获取区号
function getAreaList() {
  CustomerApi.getAllCountry()
    .then((data: any) => {
      areaList.value = data;
    })
    .finally(() => {});
}

async function updateContacts(row: any) {
  await getDetail(row.id);
  dialog.visible = true;
  dialog.title = t("omsCustomer.button.updateContacts");
  getAreaList();
}

function getDetail(id: any) {
  CustomerApi.queryDetail({ id: id })
    .then((res) => {
      Object.assign(formData, res);
    })
    .finally();
}

/** 提交角色表单 */
function handleSubmit() {
  roleFormRef.value.validate((valid: any) => {
    if (valid) {
      formLoading.value = true;
      CustomerApi.updateContact(formData)
        .then(() => {
          ElMessage.success(t("omsCustomer.message.updateSuccess"));
          handleCloseDialog();
          handleResetQuery();
        })
        .finally(() => (formLoading.value = false));
    }
  });
}

/** 关闭角色弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  roleFormRef.value.resetFields();
  roleFormRef.value.clearValidate();
  formData.id = "";
  formData.contact = "";
  formData.contactCountryAreaCode = "";
  formData.contactMobile = "";
}

/** 禁用启用 */
function handleUpdateStatus(row: any) {
  let flag = row.enableStatus;
  row.enableStatus = row.enableStatus === 0 ? 1 : 0; //保持switch点击前的状态
  let params = {
    id: row.id,
    enableStatus: flag,
  };
  // 禁用
  if (row.enableStatus == 1) {
    ElMessageBox.confirm(
      `<span style="word-break: break-all;"> ${t("omsCustomer.message.updateStatusTips", { name: row.customerName })}</span>`,
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        dangerouslyUseHTMLString: true, // 允许使用 HTML
      }
    )
      .then(() => {
        CustomerApi.updateStatus(params)
          .then((res: any) => {
            ElMessage({
              type: "success",
              message: t("omsCustomer.message.disabledSucess"),
            });
            handleQuery();
          })
          .catch(() => {
            handleQuery();
          });
      })
      .catch(() => {
        handleQuery();
        return false;
      });
  } else {
    // 启用
    CustomerApi.updateStatus(params).then((res) => {
      handleQuery();
    });
  }
}

/** 删除角色 */
function handleDelete(row: any) {
  // if (row.enableStatus == 1) {
  //   return ElMessage.error(t("omsCustomer.message.deleteNotTips"));
  // }
  ElMessageBox.confirm(
    `<span style="word-break: break-all;"> ${t("omsCustomer.message.deleteTips")}${row.customerName}？</span>`,
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
      dangerouslyUseHTMLString: true, // 允许使用 HTML
    }
  ).then(
    () => {
      loading.value = true;
      let params = {
          id: row.id,
      };
      CustomerApi.delete(params)
        .then(() => {
          ElMessage.success(t("omsCustomer.message.deleteSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("omsCustomer.message.deleteCancel"));
    }
  );
}

/**
 * 共享
 */
function handleShare(row: any) {
  shareDialog.title = t("omsCustomer.title.selectSharer");
  shareDialog.visible = true;
  let params = {
    customerId: row.id,
     tenantId:row.tenantId,
  };
  shareListRef.value.handleQuery(params);
}

onActivated(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.encryptBox {
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  vertical-align: text-top;
}

.flex_style {
  display: flex;
  align-items: center;
}

.el-message-box__message {
  p {
    word-break: break-all !important;
  }
}
</style>
