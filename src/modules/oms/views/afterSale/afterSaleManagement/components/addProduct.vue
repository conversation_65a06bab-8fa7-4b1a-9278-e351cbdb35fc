<template>
  <div class="add-product-div">
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" size="850px" @close="close" class="add-product">
        <div>
            <el-form ref="productFromRef" :model="productFrom" :inline="true">
                        <el-form-item prop="keywords" :label="$t('afterSaleManagement.label.productName')">
                            <el-input
                                    v-model="productFrom.productName"
                                    :placeholder="$t('afterSaleManagement.placeholder.productName')"
                                    clearable
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="queryProductAll">
                                {{$t('common.search')}}
                            </el-button>
                            <el-button  @click="reset">
                                {{$t('common.reset')}}
                            </el-button>
                        </el-form-item>
            </el-form>
            <el-table
                    v-loading="loading"
                    :data="productTable"
                    highlight-current-row
                    stripe
                    ref="productTableRef"
                    @selection-change="handleSelectionSupplierChange"
            >
                <el-table-column type="selection" width="60" align="center"/>
<!--                <el-table-column :label="$t('afterSaleManagement.label.productName')" min-width="150" show-overflow-tooltip>-->
<!--                    <template #default="scope">-->
<!--                        <div class="product-div">-->
<!--                            <div class="picture">-->
<!--                                <img :src="scope.row.productImg" alt="">-->
<!--                            </div>-->
<!--                            <div class="product">-->
<!--                                <div class="product-code">-->
<!--                                    <span class="product-key">{{$t('purchaseOrder.label.productCode')}}：</span>-->
<!--                                    <span class="product-value">{{scope.row.productCode}}</span>-->
<!--                                </div>-->
<!--                                <div class="product-name">{{scope.row.productName}}</div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </template>-->
<!--                </el-table-column>-->
                <el-table-column :label="$t('afterSaleManagement.label.productCode')" prop="productCode" >
                </el-table-column>
                <el-table-column :label="$t('afterSaleManagement.label.productInformation')" prop="productName" >
                </el-table-column>
                <el-table-column :label="$t('afterSaleManagement.label.productSpec')" prop="productSpecs" show-overflow-tooltip/>
                <el-table-column :label="$t('afterSaleManagement.label.quantityOrdered')" prop="productQty" show-overflow-tooltip/>
            </el-table>
            <div class="select-num-panel">{{ $t("purchaseOrder.message.selectNumTips")}}<span class="select-num">{{multipleSelectionSupplier.length}}</span></div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" :loading="submitLoading" @click="submitForm" :disabled="multipleSelectionSupplier.length==0">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
    import PurchaseOrderAPI, { ProductAllPageQuery,ProductAllPageVO} from "@/modules/pms/api/purchaseOrder";
    import type { CascaderProps } from 'element-plus';
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();

    const  ids = ref([])
    const  submitLoading= ref(false)
    const loading = ref(false);
    const productFromRef = ref();
    const productTableRef = ref();
    const multipleSelectionSupplier = ref([]);
    const productTable = ref<ProductAllPageVO[]>()
    const oldProductTable = ref<ProductAllPageVO[]>()
    let productFrom = reactive<ProductAllPageQuery>({

    });

    function close() {
        emit("update:visible", false);
        productFrom.productName = '';
        productTableRef.value.clearSelection()
    }

    function handleSelectionSupplierChange(val) {
        multipleSelectionSupplier.value = val;
    }

    function submitForm() {
        submitLoading.value = true;
        const  collection = multipleSelectionSupplier.value
        console.log("collection===",collection)
        close();
        submitLoading.value = false;
        emit("onSubmit",collection);
    }

    function queryProductAll(){
        loading.value = true;
        submitLoading.value=true;
        if(productFrom.productName != ''){
            productTable.value = oldProductTable.value.filter(function (table) {
                return table.productName.indexOf(productFrom.productName) !== -1
            })
        }else {
            productTable.value = oldProductTable.value;
        }
        console.log("productTable.value ===",productTable.value)
        loading.value = false;
        submitLoading.value=false;
    }
    function reset() {
        productFrom.productName = '';
        productTable.value = oldProductTable.value;
        productTableRef.value.clearSelection()
    }

    function setFormData(data) {
        productTable.value = data.productAllList;
        oldProductTable.value = data.productAllList;
        productFrom.productName = '';
    }

    defineExpose({
        setFormData,
    });
</script>

<style scoped lang="scss">
    .add-product{
        .supplier-div{
            width: calc(100% - 170px);
        }
        .product-div{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .picture{
                margin-right: 16px;
                img{
                    width: 80px;
                    height: 80px;
                }
            }
            .product{
                font-family: PingFangSC, PingFang SC;
                font-style: normal;
                .product-code{
                    font-weight: 400;
                    font-size: 14px;
                    color: #90979E;
                }
                .product-name{
                    font-weight: 500;
                    font-size: 14px;
                    color: #52585F;
                }
            }
        }
        .select-num-panel{
            margin-top: 16px;
            .select-num{
                margin-left: 8px;
                font-size: 18px;
                color:var(--el-color-primary)
            }
        }


    }
</style>
<style lang="scss">
  .add-product-div{
    .el-drawer__body{
      overflow: hidden;
      .el-table{
        height: calc(100% - 80px);
        overflow: auto;
      }
    }
    .el-drawer__body>*{
      height: 100%;
    }
  }
</style>
