<template>
  <div class="app-container">
    <div class="contractReview-container">
        <el-card class="mb-12px search-card">
          <div class="search-form">
            <el-form :model="queryParams" :inline="true" label-width="84px">
              <el-form-item :label="t('omsContract.label.contractName')">
                <el-input
                  v-model="queryParams.contractName"
                  :placeholder="t('omsContract.label.contractName')"
                  maxlength="50"
                  clearable
                  class="!w-[256px]"
                />
              </el-form-item>
              <el-form-item :label="t('omsContract.label.contractPartner')">
                <el-input
                  v-model="queryParams.contractPartner"
                  :placeholder="t('omsContract.label.contractPartner')"
                  maxlength="50"
                  clearable
                  class="!w-[256px]"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleSearch"
                  v-hasPerm="['oms:contractReview:page']"
                >
                  {{ t("omsContract.button.search") }}
                </el-button>
                <el-button
                  @click="handleReset"
                  v-hasPerm="['oms:contractReview:reset']"
                >
                  {{ t("omsContract.button.reset") }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
        <el-card class="content-card">
          <div class="panelContent">
            <div class="panelItem" @click="changePanel(item)" :class="{ active: item.active }" v-for="(item, index) in tabs" :key="item.key">
              {{ item.value }}
            </div>
          </div>
          <el-table
            v-loading="loading"
            :data="contractReviewList"
            highlight-current-row
            stripe
          >
            <template #empty>
              <Empty/>
            </template>
            <el-table-column :label="$t('omsContract.label.contractName')" prop="contractName" show-overflow-tooltip></el-table-column>
            <el-table-column :label="$t('omsContract.label.contractPartner')" prop="contractPartner" show-overflow-tooltip></el-table-column>
            <el-table-column :label="$t('omsContract.label.contractCode')" prop="contractCode" show-overflow-tooltip></el-table-column>
            <el-table-column :label="$t('omsContract.label.signType')" prop="signType" show-overflow-tooltip>
              <template #default="{ row }">
                {{ getContractTypeLabel(row.signType) }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('omsContract.label.createTime')" prop="createTime" show-overflow-tooltip min-width="180">
              <template #default="scope">
                <span v-if="scope.row.createTime">{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('omsContract.label.auditStatus')" min-width="150" prop="approveStatus" show-overflow-tooltip>
              <template #default="scope">
                <div class="purchase">
                  <span class="purchase-status purchase-status-color1" v-if="scope.row.approveStatus==1">{{$t('omsContract.label.pendingReview')}}</span>
                  <span class="purchase-status purchase-status-color3" v-else-if="scope.row.approveStatus==2">{{$t('omsContract.label.agreed')}}</span>
                  <span class="purchase-status purchase-status-color5" v-else-if="scope.row.approveStatus==3">{{$t('omsContract.label.rejected')}}</span>
                  <span v-else>-</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('omsContract.label.auditTime')" prop="approveTime" show-overflow-tooltip min-width="180">
              <template #default="scope">
                <span v-if="scope.row.approveTime">{{ parseDateTime(scope.row.approveTime, "dateTime") }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('omsContract.label.auditPerson')" prop="approveUserName" show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right" :label="$t('common.handle')" width="100">
              <template #default="scope">
<!--                v-hasPerm="['oms:contractReview:audit']"-->
                <el-button
                  v-if="scope.row.approveFlag==1"
                  type="primary"
                  link
                  @click="auditHandel(scope.row)"
                >
                  {{$t('omsContract.button.audit')}}
                </el-button>
                <el-button
                  v-hasPerm="['oms:contractReview:detail']"
                  type="primary"
                  v-if="scope.row.approveStatus!=1 || (scope.row.approveStatus == 1 && scope.row.approveFlag==0)"
                  link
                  @click="viewDetail(scope.row)"
                >
                  {{$t('omsContract.button.view')}}
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container">
            <pagination
              v-if="total > 0"
              v-model:total="total"
              v-model:page="queryParams.page"
              v-model:limit="queryParams.limit"
              @pagination="handlePageChange"
            />
          </div>
        </el-card>

    </div>
  </div>
</template>
<script setup lang="ts">
    import {useRouter} from "vue-router";
    import {ContractReviewQueryParams,ContractReviewResp,getContractList} from "@/modules/oms/api/contract";
    import { parseDateTime} from "@/core/utils/index.js";
    import {computed} from "vue";
    defineOptions({
        name: "ContractReview",
        inheritAttrs: false,
    });
    const router = useRouter();
    const { t } = useI18n();
    const queryParams = reactive<ContractReviewQueryParams>({
        approveStatus:'',
        page: 1,
        limit: 20,
    });
    const contractReviewList = ref<ContractReviewResp[]>();
    const loading = ref(false);
    const total = ref(0);
    const tabs = ref([
        {
            key: '',
            value: t('omsContract.label.all'),
            active: true
        },
        {
            key: 1,
            value: t('omsContract.label.pendingReview'),
            active: false
        },
        {
          key: 2,
          value: t('omsContract.label.agreed'),
          active: false
        },
        {
            key: 3,
            value: t('omsContract.label.rejected'),
            active: false
        },
    ]);
    // 合同签订类型选项
    const contractTypeOptions = [
      { label: t("omsContract.label.typeSign"), value: 0 },
      { label: t("omsContract.label.typeRenew"), value: 1 },
    ] as const;
    // 获取签订类型文本
    const getContractTypeLabel = computed(() => (type: number) => {
      return (
        contractTypeOptions.find((option) => option.value === type)?.label ?? ""
      );
    });
    const changePanel = (data) => {
        tabs.value.map(item => item.active = false);
        data.active =true;
        queryParams.approveStatus = data.key;
        queryParams.page = 1;
        queryParams.limit = 20;
        fetchContracts();
    }
    const fetchContracts = async () => {
      loading.value = true;
      try {
        const params = {
          ...queryParams,
        };
        const data = await getContractList(params);
        contractReviewList.value = data.records;
        total.value = parseInt(data.total);
      } catch (error) {
        console.error("获取合同审核列表失败:", error);
        ElMessage.error("获取合同审核列表失败");
      } finally {
        loading.value = false;
      }
    };
    const handlePageChange = () => {
      fetchContracts();
    };

    //查询列表
    function handleSearch() {
      queryParams.page = 1;
      fetchContracts()
    }
    //重置
    function handleReset() {
        queryParams.contractName = ''
        queryParams.contractPartner = ''
        queryParams.page = 1;
        queryParams.limit = 20;
        fetchContracts()
    }

    function auditHandel(row) {
      router.push({
        path: "/oms/omsContract/contractAudit",
        query: {
          contractId:row.contractId
        },
      });
    }
    function viewDetail(row){
      router.push({
        path: "/oms/omsContract/reviewDetail",
        query: {
          contractId:row.contractId
        },
      });
    }
    onActivated(() => {
      fetchContracts();
    });
</script>
<style lang="scss" scoped>
  :deep(.el-button--primary.el-button--default.is-link) {
    color: #762adb;
  }
  :deep(.el-button--danger.el-button--default.is-link) {
    color: #c00c1d;
  }
  .contractReview-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .panelContent {
      display: flex;
      border-bottom: 1px solid #F2F3F4;
      width: 100%;
      margin-bottom: 16px;
      .panelItem {
        font-size: 14px;
        color: #151719;
        padding: 10px 39px;
        cursor: pointer;
        &.active {
          color: var(--el-color-primary);
          border-bottom: 2px solid var(--el-color-primary);
        }
      }
    }

    .search-card {
      flex-shrink: 0;
    }

    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }

  :deep(.el-form-item--default) {
    // 表单项样式设置
    margin-bottom: 0;
  }
</style>

