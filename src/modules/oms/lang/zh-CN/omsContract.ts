/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-21 09:45:35
 * @LastEditors: ch<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-03-21 17:57:46
 * @FilePath: \supply-manager-web\src\modules\oms\lang\zh-CN\omsContract.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default {
  omsContract: {
    label: {
      // 基本信息
      contractName: "合同名称",
      contractCode: "合同编码",
      contractPartner: "签订对方",
      contractOwner: "签订我方",
      contractAmount: "合同金额(元)",
      contractDeposit: "保证金(元)",
      signDate: "合同签订日期",
      dateRange: "合同期限",
      contractNote: "合同说明",
      contractStatus: "合同状态",
      contractType: "合同类型",
      signType: "签订类型",
      contractPeriod: "合同期限",
      createTime: "录入时间",
      auditStatus:"审核状态",
      auditTime:'审核时间',
      auditPerson:'审核人',
      auditRemark: '审核意见',
      attachment: "合同附件",
      uploadAttachment: "上传合同附件",
      basicInfo: "基础信息",
      attachmentInfo: "附件信息",
      salesPerson:'业务员',
      enableProductSwitch:'商品清单',
      open:'开启',
      close:'关闭',
      productDetail:'商品明细',
      sort: "序号",
      productName:'商品名称',
      productUnit:'单位',
      price:'单价',
      product:'商品',
      productCategory:'商品分类',
      productCode:'商品编码',
      audit:'审核',

      // 日期相关
      startDate: "开始日期",
      endDate: "结束日期",
      rangeSeparator: "至",

      // 状态和类型选项
      statusUnworking: "未生效",
      statusExecuting: "执行中",
      statusExpired: "已到期",
      statusCancelled: "已作废",
      typeSign: "签订",
      typeRenew: "续签",
      typePurchase: "采购合同",

      //审核状态
      all:'全部',
      pendingReview:'待审核',
      agreed:'已同意',
      rejected:'已驳回',
    },
    placeholder: {
      productName:'请输入商品名称',
      productCode:'请输入商品编码',
      processRemark:'请输入审核意见，驳回必填',
    },
    button: {
      search: "搜索",
      reset: "重置",
      add: "新增合同",
      edit: "编辑",
      delete: "删除",
      void: "作废",
      save: "保存",
      cancel: "取消",
      close: "关闭",
      upload: "上传附件",
      operation: "操作",
      addGoods: "添加商品",
      audit:'审核',
      view:'查看',
      reject:'驳回',
      agree:'同意',
    },
    title: {
      manage: "合同管理",
      add: "新增合同",
      edit: "编辑合同",
      detail: "合同详情",
      addProduct:'选择商品',
    },
    message: {
      createSuccess: "合同创建成功",
      updateSuccess: "合同更新成功",
      deleteSuccess: "删除成功",
      voidSuccess: "作废成功",
      deleteConfirm: "确认删除{name}合同吗，删除后不可恢复？",
      voidConfirm: "确认要作废{name}合同吗？作废终止后不可恢复",
      tip: "提示",
      uploadTip: "只能上传 jpg/jpeg/png/pdf/rar/zip 文件，且不超过20M",
      fetchFailed: "获取合同列表失败",
      deleteFailed: "删除合同失败",
      voidFailed: "作废合同失败",
      supplierBindWarning: "该合同已绑定供应商，暂不支持编辑和删除操作",
      attachmentParseFailed: "合同附件数据解析失败",
      attachmentError: "合同附件数据错误",
      previewFailed: "预览文件失败",
      selectNumTips:'已选择',
      processRemark:'请输入审核意见',
      approveSuccess:'审核成功',
    },
    rules: {
      contractNameRequired: "请输入合同名称",
      contractNamePattern: "支持中文、英文、数字，限50字以内",
      contractCodeRequired: "请输入合同编码",
      contractCodePattern: "支持英文、数字、特殊字符，限50字以内",
      contractPartnerRequired: "请输入签订对方",
      contractPartnerPattern: "支持中文、英文、数字，限100字以内",
      contractOwnerRequired: "请输入签订我方",
      contractOwnerPattern: "支持中文、英文、数字，限100字以内",
      contractAmountRequired: "请输入合同金额",
      contractDepositRequired: "请输入保障金",
      contractAmountPattern:
        "请输入大于0的数字，整数部分最多16位，小数部分最多2位",
      amountGreaterThanZero: "金额必须大于0",
      integerMaxLength: "整数部分不能超过16位",
      decimalMaxLength: "小数部分不能超过2位",
      contractNoteRequired: "请输入合同说明",
      contractNotePattern: "支持中文、英文、数字、特殊字符，限200字以内",
      startDateRequired: "请选择合同开始日期",
      endDateRequired: "请选择合同结束日期",
      attachmentRequired: "请上传合同附件",
      signTypeRequired: "请选择签订类型",
      signDateRequired: "请选择签订日期",
      dateRangeRequired: "请选择合同期限",
      productList: "请添加商品明细",
      enableProductSwitch: "请选择商品清单",
      price:'请输入单价',
      priceFormat:'单价支持大于0的数字，支持小数点后2位',
      approveUser:'请选择审核人',
    },
  },
};
