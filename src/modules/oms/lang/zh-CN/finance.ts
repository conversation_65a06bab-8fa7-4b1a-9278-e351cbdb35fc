export default {
  omsFinance: {
    label: {
      bigCustomer: "大客户",
      smallCustomer: "散客",

      governmentCustomer: "政府客户",
      enterpriseCustomer: "企业客户",
      individualCustomer:"个体客户",

      customerName: "客户名称",
      customerType: "客户类型",
      salesName: "销售人员",

      customerCode: "客户编码",
      receivableAmount: "应收金额",
      actualReceivedAmount: "实收金额",
      remainingReceivableAmount: "剩余应收金额",
      detail: "详情",
      collection:"收款",
      collectionRecord: "收款记录",
      order: "订单",

      collectionTime: "收款时间",
      collectionMethod: "收款方式",
      collectionVoucher: "收款凭证",
      collectionUser: "收款人",
      collectionRemark: "备注",

      startTime: "开始时间",
      endTime: "结束时间",
      startDate: "开始日期",
      endDate: "结束日期",
      refresh: "刷新",

      orderTheme: "销售主题",
      orderCode: "订单号",
      orderCreateTime: "下单时间",

      receivableAmountTitle: "应收金额",
      returnAmountTitle: "售后金额",
      orderAmountTitle: "订单金额",
      receivableAmountLabel: "收款金额",
      remark: "备注",
      dateTimeRangeTo: "至",
      totalSum: "合计",
      orderDetail: "订单详情",

    },
    placeholder: {
      select: "请选择",
      inputTip: "请输入",
      collectionTime: "请选择时间",
    },


    button: {
     search: "搜索",
      reset: "重置",
      look: "查看",
    },
    title: {

    },
    message: {
      loadFail: "加载失败",
    },
    rules: {
      collectionTypeId: "请选择收款方式",
      collectionAmount: "请输入收款金额",
      collectionTime: "请选择收款时间",

      collectionAmountFormat: "收款金额需大于0，支持小数点前9位后2位的数字",

      collectionVoucher: "请上传收款凭证",
      dateRange: "请选择收款时间",
    },
    confirm: {

    },
  },
};
