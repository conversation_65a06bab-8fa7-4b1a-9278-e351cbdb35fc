export default {
  omsOrder: {
    label: {
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      dateType: "时间选择",
      orderCode: "订购单号",
      customerName: "客户名称",
      customer: "客户信息",
      productName: "商品名称",
      product: "商品",
      orderSource: "来源",
      productCount: "商品个数",
      orderMoney: "下单金额",
      receivingInfo: "收货信息",
      planDeliveryDate: "期望出库时间",
      orderDate: "下单时间",
      orderStatus: "订单状态",
      remark: "备注",
      remarkCopy: "留言备注",
      receiveName: "收货人",
      receiveMobile: "联系方式",
      deliveryAddress: "联系地址",
      downLoadOrder: "下载订购单导入模板.xlsx",
      basicInformation: "基本信息",
      deliveryInformation: "收货信息",
      customerNameCopy: "客户",
      planDeliveryDateCopy: "期望出库时间",
      receiveNameCopy: "收货人姓名",
      receiveMobileCopy: "手机号码",
      deliveryAddressCopy: "收货地址",
      address: "详细地址",
      productListInformation: "订购商品",
      orderAttachmentFiles: "附件",
      productCategory: "商品分类",
      productCode: "商品编码",
      productSpecName: "规格",
      saleCount: "数量",
      saleCountCopy: "到货数量",
      salePrice: "销售价",
      saleMoney: "总价",
      total: '合计',
      totalMoney: "货品合计金额",
      remarkInformation: "备注信息",
      orderAttachmentFilesInformation: "附件信息",
      customerInformation: "客户信息",
      orderInformation: "订单信息",
      deliveryWarehouseInformation: "配货仓库",
      deliveryPlanInformation: "交货计划",
      operationLogInformation: "操作日志",
      warehouseName: "仓库",
      floatRange: "浮动区间",
      externalOrderCode: "外部订单号",
      deliverDate: "配送时间",
      finashDate: "完成时间",
      closeDate: "关闭时间",
      operationTime: "操作时间",
      operationType: "操作类型",
      operationUserName: "操作人",
      deliveryBatch: "交付批次",
      estimatedDeliveryTime: "预计送达时间",
      generationTime: "生成时间",
      arrivalQuantity: "到货数量",
      concleReason: "确认取消当前采购单？",
      shutdownReason: "取消原因",
      deliveryPlan: "交货计划",
      orderCreateTime: "下单时间",
      orderType: "销售类型",
      orderTypeCode: "销售单号",
      syncStatus: "同步状态",
      handleSync: "手动同步",
      orderTheme: "销售主题",
      customerAttributes: "客户属性",
      presale: "预售",
      salesName: "销售人员",
      approveUserName: "审核人员",
      paymentType: "结算方式",
      bigCustomer: "大客户",
      smallCustomer: "散客",
      otherCustomer: "其他",
      // contract: "合同",
      originalOrderCode: "原订单号",
      exchangeCode: "兑换卡号",
      operatorUser: "经办人",
      warehouseDelivery: "仓库配送",
      warehouse: "仓库",
      deliveryMethod: "配送方式",

      productTotalAmount: "商品总金额",
      discountType: "优惠方式",
      discountValue: "优惠金额",
      totalDiscountedAmount: "优惠后金额",
      contract: '客户合同',
      approveIng: "审核中",
      approveUnpass: "已驳回",
      approvePass: "审核通过",
      approveUnpassReason: "驳回原因",
      unit: "单位",
      count: "数量",
      price: "单价",
      totalAmount: "金额",
      createOrderName: "制单人",
      approveUser: "审批人",
      phone: "电话",
      totalAmounts: "合计金额",
      totalCount: "总数量",
      discount: "优惠",
      saleOutBounceOrder: '销售出库单',
      outboundNoticeCode: '出库单号',
      outboundNoticeStatus: "状态",
      orderReturn: '售后情况',
      orderReturnCode: '售后单号',
      orderReturnTime: '售后申请时间',
      orderReturnApproveTime: '售后审核时间',
      returnType: '售后类型',
      returnAmount: "售后金额",
      afterSaleEntryOrder: '售后入库单',
      receiptNoticeCode: '入库单通知单',
      submitterUser: "制单人",
      isMine: "只看我的",
    },
    placeholder: {
      concleReason: "请输入取消原因，最多输入100字",
      // keywords: "请输入商品名称/编码",
      productCode: "请输入商品编码",
      productName: "请输入商品名称",
      productCategory: "请输入关键词搜索分类",
      contractName: "请选择合同",
      originalOrderCode: "请选择原订单号",
      exchangeCode: "请输入兑换卡号",
      customerName: '请选择客户',
      contract: "请选择客户合同",
      deliveryMethod: "请选择配送方式",
      errorCloseMsg: "请输入关闭原因，最多输入100字",
      rejectMsg: "请输入驳回原因，最多输入100字",

    },
    dateTypeList: {
      orderDate: "下单时间",
      planDeliveryDate: "期望出库时间",
    },
    orderSourceList: {
      imports: "导入",
      manuallyAdd: "手动新增",
    },
    deliveryPlanList: {
      uniformDelivery: "按期望时间统一送达",
      batchesdelivery: "货品分批次送达",
    },
    orderStatusList: {
      all: "全部",
      draft: "草稿",
      check: "待审核",
      ready: "备货中",
      send: "配送中",
      finash: "已完成",
      close: "已关闭",
    },
    outboundNoticeStatusList:{
      1: '初始',
      2: '拣货中',
      3: '完成',
      4: '取消',
      5: '已排班',
      6: '部分出库',
    },
    receiptNoticeStatusList: {
      0: '草稿',
      1: "初始",
      2: "完结",
      3: '取消',
      4: '入库中'
    },
    returnTypeList: {
      1: '退货',
      2: '退货退款',
      3: '退款'
    },
    orderTypeList: {
      1: '普通销售',
      2: '提货卡兑换',
      3: '业务招待',
      4: '补发',
      5: '地头售卖',
      9: '参展',
    },
    syncStatusList: {
      1: '成功',
      2: '失败'
    },
    presaleList: {
      1: '是',
      0: '否'
    },
    paymentTypeList: {
      1: '现结',
      2: '挂账',
      3: '免结算'
    },
    discountTypeList: {
      1: '无',
      2: "金额",
      3: "折扣"
    },
    shelfLifeUnitList: {
      day: "天",
      month: "月",
      year: "年",
    },
    button: {
      importOrderInformation: "导入订单信息",
      addOrder: "新建订单",
      editOrder: "编辑订单",
      adjustOrder: "调整订单",
      submitOrder: "提交订单",
      saveDraft: "保存草稿",
      saveCheck: "保存",
      check: "审核",
      orderDetail: "订单详情",
      addProduct: "添加商品",
      deliveryPlan: "交货计划",
      adjustDeliveryPlan: "调整交货计划",
      concleOrder: "取消订单",
      checkFinash: "审核完成",
      copy: "复制",
      updateStatus: "更改状态",
      confirmDeliveryPlan: '确认交货计划',
      afterSale: "售后",
      errorClose: "异常关闭",
      reject: "驳回",
      reissue: "补发",
      approvePass: "审核完成",
      approveStatus: "审核状态",
      print: "打印",
      recall: "撤回",

    },
    title: {
      draftDdelete: "草稿删除",
      addProduct: "添加商品",
      concleOrder: "取消订单",
      checkFinash: "审批完成",
      updateStatus: "更改状态",
      deliveryPlan: "交货计划",

      reissueOrder: "补发订单",
      errorClose: "确定关闭当前订购单？",
      rejectTitle: "确定驳回当前订购单？",
      printTitle: "销售单"
    },
    message: {
      adjustOrderTips: "调整订单后，原有交货计划将被删除，需重新创建交货计划，是否确定调整订单?",
      adjustOrderConcel: "已取消调整订单！",
      deleteTips: "确定删除当前订购单，删除后不可恢复?",
      deleteConcel: "已取消删除！",
      deleteSucess: "删除成功",
      orderCodeTips: "订购单号最多支持输入50位",
      customerNameTips: "客户名称最多支持输入50位",
      productNameTips: "商品名称最多支持输入50位",
      addOrEditOrderTips: '订购商品不能为空！',
      saveDraftOrderSucess: "订购单草稿保存成功！",
      submitOrderSucess: "订购单下单成功！",
      saveCheckSucess: "保存成功！",
      selectNumTips: "已选",
      copySuccess: "复制成功",
      copyFail: "复制失败",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      salePriceTips: "不可超过上下的浮动区间百分比",
      closeOrderSucess: "取消成功",
      checkFinashTips1: "订单[",
      checkFinashTips2: "], 客户期望出库时间[",
      checkFinashTips3: "],确认按此安排?",
      checkFinashSucess: "审批成功",
      checkFinashConcel: "已取消审批！",
      updateStatusTips: "更改批次状态为：",
      updateStatusTips1: "配送中",
      updateStatusTips2: "已完成",
      updateStatusSucess: "更改状态成功",
      updateStatusConcel: "已取消更改状态！",
      createDeliveryScheduleSucess: "生成交货计划成功",
      importOrderSucess: "导入成功",
      devilerySuccess: "交货计划创建成功",
      devileryUpdateSuccess: "交货计划调整成功",
      devileryFail: "交货计划创建失败",
      deliveryCanceled: "已取消交货计划",
      devileryEmpty: "交货计划不能为空",
      loadFail: "加载详情失败",
      uploadTips: "请上传文件",

      discountAmountTips: "优惠金额不能大于商品总金额",
      maxSyncCountTips: "最大勾选条数不能超过20条，请重新选择",
      syncStatusSuccess: "同步成功",
      syncStatusTitle: "状态同步失败",
      errorCloseTitle: "确定关闭当前订购单?",
      errorCloseOrderSucess: "订单已关闭，请及时通知仓库",
      rejectOrderSucess: "驳回成功",
      recallOrderTitle: "取消订单",
      recallOrderMsg: "确定撤回当前订购单？",
      recallOrderSuccess: "撤回成功",
      recallOrderFail: "撤回失败",
    },
    rules: {
      warehouseId: "请先选择配货仓库",
      orderTheme: "请输入销售主题",
      orderType: "请选择销售类型",
      exchangeCode: "请输入兑换卡号",
      exchangeCodeFormat: "可输入英文和数字",
      originalOrderCode: "请选择原订单号",
      originalOrderCodeFormat: "可输入英文和数字",
      customerAttributes: "请选择客户属性",
      isPresale: "请选择是否预售",
      approveUser: "请选择审核人员",
      salesId: "请选择销售人员",
      paymentType: "请选择结算方式",
      contract: "请选择合同",
      warehouseName: "请选择仓库",
      deliveryMethod: "请选择配送方式",

      totalAmountCopy: "请输入商品总金额",
      totalAmountCopyFormat: "金额支持小数点前9位后2位的数字",
      discountValue: "请输入折扣",
      discountAmount: "请输入金额",
      discountAmountFormat: "金额支持小数点前9位后2位的数字",
      totalDiscountedAmount: "请输入优惠后金额",
      totalDiscountedAmountFormat: "金额支持小数点前9位后2位的数字",

      customerCode: "请选择客户",
      planDeliveryDate: "请选择日期",
      planDeliveryTime: "请选择时间",
      contactPerson: "请输入收货人姓名",
      contactPersonFormat: "收货人姓名支持中文和英文",
      contactMobile: "请输入手机号码",
      contactMobileFormat: "手机号码支持数字",
      countryTip: "请选择国家",
      areaTip: "请选择收货地址",
      addressTip: "请输入详细地址",
      saleCount: "请输入数量",
      salePrice: "请输入销售价",
      saleCountFomart: "数量支持大于0的数字，支持小数点前11位，小数点后2位",
      salePriceFomart: "销售价支持小数点前7位后4位的数字",
      concleReason: "请输入取消原因",
      deliveryPlan: "请选择交货计划",
      errorReason: "请输入关闭原因，最多输入100字",
      rejectReason: "请输入驳回原因，最多输入100字"
    },
    confirm: {
      deliveryPlanSubmit: "确认按照当前交货计划？",
      cancelText: "取消",
      confirmText: "确认",
      title: "交货计划",

    },
  },
};
