<template>
  <div class="dashboard-container">
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true">
        <el-form-item prop="brandName" :label="$t('product.label.brandName')">
          <el-input
            v-model="searchForm.brandName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            @keyup.enter="onSearchHandler"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="onSearchHandler"
            v-hasPerm="['product:brand:search']"
          >
            {{ $t("common.search") }}
          </el-button>
          <el-button
            @click="onResetHandler"
            v-hasPerm="['product:brand:reset']"
          >
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          type="primary"
          @click="
            onAddHandler();
            dialogMode = 'add';
          "
          v-hasPerm="['product:brand:add']"
        >
          {{ $t("product.button.addBrand") }}
        </el-button>
      </template>
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="tableData"
        highlight-current-row
        stripe
      >
        <template #empty>
          <Empty />
        </template>
        <el-table-column
          :label="$t('product.label.brandName')"
          prop="brandName"
          show-overflow-tooltip
        />
        <el-table-column :label="$t('product.label.sort')" prop="sort" />
        <el-table-column
          :label="$t('product.label.updateTime')"
          prop="updateTime"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ parseDateTime(scope.row.updateTime, "dateTime") }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.handle')" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="
                onEditHandler(scope.row);
                dialogMode = 'edit';
              "
              v-hasPerm="['product:category:edit']"
            >
              {{ $t("common.edit") }}
            </el-button>
            <el-button
              type="danger"
              link
              @click="
                onDeleteHandler(
                  $t('product.message.deleteBrandTip') +
                    '  ' +
                    scope.row.brandName +
                    '?',
                  'id',
                  scope.row.id
                )
              "
              v-hasPerm="['product:brand:delete']"
            >
              {{ $t("common.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="paginationInfo.pageNo"
        v-model:limit="paginationInfo.pageSize"
        @pagination="onPaginationChangeHandler"
      />
    </el-card>
    <el-drawer
      v-model="showDialog"
      :title="
        dialogMode === 'add'
          ? $t('product.label.addBrand')
          : $t('product.label.editBrand')
      "
      :close-on-click-modal="false"
      @close="onCloseHandler"
    >
      <el-form
        :model="contentForm"
        :rules="contentFormRules"
        ref="contentFormRef"
        label-position="top"
      >
        <el-form-item :label="$t('product.label.brandName')" prop="brandName">
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.brandName"
            :maxlength="20"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('product.label.showSort')" prop="sort">
          <el-input-number
            v-model="contentForm.sort"
            :placeholder="$t('common.placeholder.inputTips')"
            controls-position="right"
            :max="1000"
            :min="0"
            :step="1"
            style="width: 150px"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseHandler()">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button
            type="primary"
            :loading="dialogLoading"
            @click="onSaveHandler"
          >
            {{ $t("common.confirm") }}
          </el-button>
        </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import formMixin from "@/modules/goods/mixins/form";
import type { FormRules } from "element-plus";
import tableMixin from "@/modules/goods/mixins/table";
import productBrandAPI, {
  productBrandForm,
  productBrandInfo,
} from "@/modules/goods/api/productBrand";
defineOptions({
  name: "GoodsBrand",
  inheritAttrs: false,
});
const { proxy } = getCurrentInstance();
import { parseDateTime } from "@/core/utils/index.js";
const { t } = useI18n();

const dialogMode = ref<string>("add");
const searchForm = reactive({
  brandName: "",
});
const contentForm = reactive<productBrandForm>({
  id: "",
  brandName: "",
});
const contentFormRules = reactive<FormRules>({
  brandName: [
    {
      required: true,
      message: proxy.$t("product.rules.brandNameRule"),
      trigger: "blur",
    },
  ],
  sort: [
    {
      required: true,
      message: proxy.$t("product.rules.brandSortRule"),
      trigger: "blur",
    },
    {
      pattern: /^(0|\+?[1-9][0-9]*)$/,
      message: t("unitManagement.rules.sortFomart"),
      trigger: ["blur", "change"],
    },
  ],
});
function saveCallbackHandler() {
  onSearchHandler();
}
const {
  showDialog,
  dialogLoading,
  formType,
  contentFormRef,
  onAddHandler,
  onEditHandler,
  onSaveHandler,
  onCloseHandler,
} = formMixin({
  contentForm,
  idName: "id",
  formAddApi: productBrandAPI.saveBrand,
  formEditApi: productBrandAPI.updateBrand,
  saveCallback: saveCallbackHandler,
});
const {
  loading,
  tableData,
  total,
  paginationInfo,
  headFormRef,
  router,
  path,
  onSearchHandler,
  onResetHandler,
  onPaginationChangeHandler,
  onDeleteHandler,
  onStatusChangeHandler,
} = tableMixin({
  searchForm,
  tableGetApi: productBrandAPI.getPageList,
  tableDeleteApi: productBrandAPI.deleteBrand,
});

onMounted(() => {
  onSearchHandler();
});
</script>

<style lang="scss" scoped></style>
