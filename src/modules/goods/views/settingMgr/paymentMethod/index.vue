<template>
  <div class="app-container">
    <div class="paymentMethod">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="80px">
            <el-row>
              <el-form-item :label="$t('paymentMethod.label.paymentMethod')" prop="methodName">
                <el-input v-model="queryParams.methodName" :placeholder="$t('common.placeholder.inputTips')" clearable style="width: 240px" @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleQuery" v-hasPerm="['goods:payment_method:search']">
                  {{ $t("common.search") }}
                </el-button>
                <el-button @click="handleResetQuery" v-hasPerm="['goods:payment_method:reset']">
                  {{ $t("common.reset") }}
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
        </div>
      </el-card>
      <!-- 数据表格 -->
      <el-card class="content-card">
        <div class="action-bar">
          <el-button type="danger" :disabled="multipleSelection.length === 0" @click="handleBatchDisable" v-hasPerm="['goods:payment_method:disable']">{{ $t("paymentMethod.button.disable") }}</el-button>
          <el-button type="success" :disabled="multipleSelection.length === 0" @click="handleBatchEnable" v-hasPerm="['goods:payment_method:enable']">{{ $t("paymentMethod.button.enable") }}</el-button>
          <el-button type="primary" @click="handleAdd" v-hasPerm="['goods:payment_method:add']">{{ $t("paymentMethod.button.add") }}</el-button>
        </div>
        <el-table ref="dataTableRef" v-loading="loading" :data="paymentMethodList" stripe highlight-current-row @selection-change="handleSelectionChange">
          <template #empty>
            <Empty />
          </template>
          <el-table-column type="selection" width="55" align="center" fixed="left" />
          <el-table-column :label="$t('paymentMethod.label.paymentMethod')" prop="methodName" show-overflow-tooltip/>
          <el-table-column :label="$t('paymentMethod.label.status')" align="center" width="200">
            <template #default="scope">
              <el-switch v-model="scope.row.enableStatus" :active-value="1" :inactive-value="0"
                         :active-text="$t('paymentMethod.status.enabled')" :inactive-text="$t('paymentMethod.status.disabled')"
                         inline-prompt style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                         @change="handleStatusChange(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('paymentMethod.label.updateTime')" prop="updateTime" align="center" show-overflow-tooltip width="180">
            <template #default="scope">
              <span>{{ scope.row.updateTime ? formatDateTime(scope.row.updateTime) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('common.handle')" fixed="right" align="center" width="200">
            <template #default="scope">
              <el-button v-if="!scope.row.enableStatus" type="primary" link @click="handleEdit(scope.row)" v-hasPerm="['goods:payment_method:edit']">{{ $t("paymentMethod.button.edit") }}</el-button>
              <el-button v-if="!scope.row.enableStatus" type="danger" link @click="handleDelete(scope.row)" v-hasPerm="['goods:payment_method:delete']">{{ $t("paymentMethod.button.delete") }}</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.page" v-model:limit="queryParams.limit" :page-sizes="[20, 50, 100, 200]" @pagination="handleQuery" />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-drawer v-model="dialogVisible" :title="dialogTitle" direction="rtl" size="500px" :close-on-click-modal="false" @close="handleClose">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" label-position="top">
        <el-form-item :label="$t('paymentMethod.label.paymentMethod')" prop="methodName">
          <el-input v-model="formData.methodName" maxlength="30" show-word-limit :placeholder="$t('common.placeholder.inputTips')" clearable />
        </el-form-item>
        <el-form-item :label="$t('paymentMethod.label.status')" prop="enableStatus">
          <el-radio-group v-model="formData.enableStatus">
            <el-radio :value="1">{{ $t('paymentMethod.status.enabled') }}</el-radio>
            <el-radio :value="0">{{ $t('paymentMethod.status.disabled') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="drawer-footer">
          <el-button @click="handleClose">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button type="primary" :loading="formLoading" @click="handleSubmit">
            {{ $t("paymentMethod.button.save") }}
          </el-button>
        </div>
      </template>
    </el-drawer>

  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "PaymentMethod",
    inheritAttrs: false,
  });
  import PaymentMethodAPI,{PaymentMethodPageQuery,PaymentMethodResp,PaymentMethodForm,EnableStatus} from "@/modules/goods/api/paymentMethod"
  import type { FormInstance, TableInstance } from "element-plus";
  import { parseDateTime } from "@/core/utils";

  const { t } = useI18n();
  const queryFormRef = ref<FormInstance>();
  const dataTableRef = ref<TableInstance>();
  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const formLoading = ref(false);
  const total = ref(0);
  const dialogVisible = ref(false);
  const isEdit = ref(false);

  // 查询参数
  const queryParams = reactive<PaymentMethodPageQuery>({
    page: 1,
    limit: 20,
  });

  // 支付方式列表
  const paymentMethodList = ref<PaymentMethodResp[]>([]);

  // 多选数据
  const multipleSelection = ref<PaymentMethodResp[]>([]);

  // 表单数据
  const formData = reactive<PaymentMethodForm>({
    enableStatus:1,
  });

  // 弹窗标题
  const dialogTitle = computed(() => {
    return isEdit.value ? t('paymentMethod.button.edit') : t('paymentMethod.button.add');
  });

  // 表单验证规则
  const formRules = {
    methodName: [
      { required: true, message: t('paymentMethod.rules.methodName'), trigger: 'change' }
    ],
    enableStatus: [
      { required: true, message: t('paymentMethod.rules.enableStatus'), trigger: 'change' }
    ]
  };

  /** 查询支付方式列表 */
  function handleQuery() {
    loading.value = true;
    PaymentMethodAPI.getPageList(queryParams).then((data) => {
      paymentMethodList.value = data.records;
      total.value = parseInt(data.total);
    }).catch(() => {

    }).finally(() => {
      loading.value = false;
    });
  }

  /** 重置查询 */
  function handleResetQuery() {
    queryFormRef.value?.resetFields();
    queryParams.page = 1;
    queryParams.limit = 20;
    handleQuery();
  }

  /** 处理多选变化 */
  function handleSelectionChange(selection: PaymentMethodResp[]) {
    multipleSelection.value = selection;
  }

  /** 处理状态变更 */
  function handleStatusChange(row: PaymentMethodResp) {
    if (row.id) {
      if (row.enableStatus === EnableStatus.ENABLED) {
        PaymentMethodAPI.enableBatch([row.id!]).then(() => {
          ElMessage.success(t("paymentMethod.message.enableSuccess"));
          handleQuery();
        }).catch(() => {
          // 失败时恢复状态
          row.enableStatus = EnableStatus.DISABLED;
        });
      }
      else if (row.enableStatus === EnableStatus.DISABLED) {
        ElMessageBox.confirm(
          t("paymentMethod.message.disableBatchConfirm"),
          t("common.tipTitle"),
          {
            confirmButtonText: t("common.confirm"),
            cancelButtonText: t("common.cancel"),
            type: "warning",
          }
        ).then(() => {
          PaymentMethodAPI.disableBatch([row.id!]).then(() => {
            ElMessage.success(t("paymentMethod.message.disableSuccess"));
            handleQuery();
          }).catch(() => {
            // 失败时恢复状态
            row.enableStatus = EnableStatus.ENABLED;
          });
        }).catch(() => {
          // 用户取消时恢复状态
          row.enableStatus = EnableStatus.ENABLED;
        });
      }
    }
  }

  /** 新增 */
  function handleAdd() {
    isEdit.value = false;
    resetForm()
    dialogVisible.value = true;
  }

  /** 编辑 */
  function handleEdit(row: PaymentMethodResp) {
    isEdit.value = true;
    formData.id = row.id;
    formData.methodName = row.methodName;
    formData.enableStatus = row.enableStatus;
    dialogVisible.value = true;
  }

  /** 删除 */
  function handleDelete(row: PaymentMethodResp) {
    ElMessageBox.confirm(
      `<span style="word-break: break-all;">${t("paymentMethod.message.deleteConfirm1")}<span style="color: #f56c6c"> ${row.methodName}</span>${t("paymentMethod.message.deleteConfirm2")}</span>`,
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
        dangerouslyUseHTMLString: true, // 允许使用 HTML
      }
    ).then(() => {
      PaymentMethodAPI.deleteConfirm(row.id!).then(()=>{
        ElMessage.success(t("paymentMethod.message.deleteSuccess"));
        handleQuery();
      }).catch(() => {
      });
    });
  }

  /** 批量启用 */
  function handleBatchEnable() {
    if (multipleSelection.value.length === 0) {
      ElMessage.warning(t("paymentMethod.message.selectTip"));
      return;
    }
    const ids = multipleSelection.value.map(item => item.id!);
    PaymentMethodAPI.enableBatch(ids).then(() => {
      ElMessage.success(t("paymentMethod.message.enableSuccess"));
      handleQuery();
    }).catch(() => {
    });
  }

  /** 批量禁用 */
  function handleBatchDisable() {
    if (multipleSelection.value.length === 0) {
      ElMessage.warning(t("paymentMethod.message.selectTip"));
      return;
    }
    ElMessageBox.confirm(
      t("paymentMethod.message.disableBatchConfirm"),
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
      }
    ).then(() => {
      const ids = multipleSelection.value.map(item => item.id!);
      PaymentMethodAPI.disableBatch(ids).then(() => {
        ElMessage.success(t("productConfig.message.disableSuccess"));
        handleQuery();
      }).catch(() => {
      });
    });
  }

  /** 提交表单 */
  function handleSubmit() {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        formLoading.value = true;
        const apiCall = isEdit.value ?
          PaymentMethodAPI.editSave(formData) :
          PaymentMethodAPI.addSave(formData);

        apiCall.then(() => {
          ElMessage.success(isEdit.value ? t("paymentMethod.message.editSuccess") : t("paymentMethod.message.addSuccess"));
          dialogVisible.value = false;
          handleQuery();
        }).catch(() => {
        }).finally(() => {
          formLoading.value = false;
        });
      }
    });
  }

  /** 关闭弹窗 */
  function handleClose() {
    dialogVisible.value = false;
    resetForm();
  }

  /** 重置表单 */
  function resetForm() {
    formData.id = undefined;
    formData.methodName = '';
    formData.enableStatus = 1;
    formRef.value?.clearValidate();
  }

  /** 格式化时间 */
  function formatDateTime(time: string) {
    return parseDateTime(time, "dateTime");
  }

  // 页面加载时查询数据
  onMounted(() => {
    handleQuery();
  });
</script>

<style scoped lang="scss">
  .paymentMethod {
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }

    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .action-bar {
        margin-bottom: 12px;
        flex-shrink: 0;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
    .el-message-box__message {
      p {
        word-break: break-all !important;
      }
    }
  }
  :deep(.multipleSelect .el-select__selected-item) {
    color: #151719 !important;
  }
  // 开关组件样式调整
  :deep(.el-switch__label) {
    font-size: 12px;
  }

  :deep(.el-switch__label.is-active) {
    color: #13ce66;
  }

  :deep(.el-switch__label:not(.is-active)) {
    color: #ff4949;
  }
</style>
