<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="80px">
        <el-row>
          <el-form-item :label="$t('warehouseAreaType.label.typeName')" prop="typeName">
            <el-input v-model="queryParams.typeName"
              :placeholder="$t('warehouseAreaType.placeholder.typeNamePlaceholder')" clearable style="width: 240px"
              @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" v-hasPerm="['wms:area:type:search']">
              {{ $t("warehouseAreaType.button.search") }}
            </el-button>
            <el-button @click="handleResetQuery" v-hasPerm="['wms:area:type:search']">
              {{ $t("warehouseAreaType.button.reset") }}
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="table-header">
          <!-- <span>{{ $t('warehouseAreaType.title.list') }}</span> -->
          <div class="header-buttons">
            <el-button type="danger" :disabled="multipleSelection.length === 0" @click="handleBatchDisable"
              v-hasPerm="['wms:area:type:disable']">
              {{ $t("warehouseAreaType.button.disable") }}
            </el-button>
            <el-button type="success" :disabled="multipleSelection.length === 0" @click="handleBatchEnable"
              v-hasPerm="['wms:area:type:enable']">
              {{ $t("warehouseAreaType.button.enable") }}
            </el-button>
            <el-button type="primary" @click="handleAdd" v-hasPerm="['wms:area:type:add']">
              {{ $t("warehouseAreaType.button.add") }}
            </el-button>
          </div>
        </div>
      </template>

      <el-table ref="dataTableRef" v-loading="loading" :data="warehouseAreaTypeList" stripe highlight-current-row
        @selection-change="handleSelectionChange">
        <template #empty>
          <Empty />
        </template>

        <el-table-column type="selection" width="55" align="center" />

        <el-table-column type="index" :label="$t('common.sort')" width="60" align="center" />

        <el-table-column :label="$t('warehouseAreaType.label.typeCode')" prop="typeCode" width="150"
          show-overflow-tooltip />

        <el-table-column :label="$t('warehouseAreaType.label.typeName')" prop="typeName" min-width="260"
          show-overflow-tooltip />

        <el-table-column :label="$t('warehouseAreaType.label.sort')" prop="sort" width="100" align="center" />

        <el-table-column :label="$t('warehouseAreaType.label.enableStatus')" width="150" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.enableStatus" :active-value="1" :inactive-value="0"
              :active-text="$t('warehouseAreaType.status.enabled')"
              :inactive-text="$t('warehouseAreaType.status.disabled')" inline-prompt
              style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column :label="$t('warehouseAreaType.label.updateTime')" prop="updateTime" width="180"
          show-overflow-tooltip>
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('warehouseAreaType.label.operation')" fixed="right" align="center" width="120">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)"
              v-hasPerm="['wms:area:type:edit']">
              {{ $t("warehouseAreaType.button.edit") }}
            </el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)"
              v-hasPerm="['wms:area:type:delete']">
              {{ $t("warehouseAreaType.button.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.page"
        v-model:limit="queryParams.limit" :page-sizes="[20, 50, 100, 200]" @pagination="handleQuery" />
    </el-card>

    <!-- 编辑弹窗 -->
    <el-drawer v-model="dialogVisible" :title="dialogTitle" direction="rtl" size="500px" :close-on-click-modal="false"
      @close="handleClose">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" label-position="top">
        <el-form-item :label="$t('warehouseAreaType.label.typeCode')" prop="typeCode">
          <el-input v-model="formData.typeCode" :placeholder="$t('warehouseAreaType.placeholder.typeCodePlaceholder')"
            clearable />
        </el-form-item>

        <el-form-item :label="$t('warehouseAreaType.label.typeName')" prop="typeName">
          <el-input v-model="formData.typeName" :placeholder="$t('warehouseAreaType.placeholder.typeNamePlaceholder')"
            clearable />
        </el-form-item>

        <el-form-item :label="$t('warehouseAreaType.label.sort')" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" :max="9999" :step="1"
            :placeholder="$t('warehouseAreaType.placeholder.sortPlaceholder')" controls-position="right"
            />
        </el-form-item>

        <el-form-item :label="$t('warehouseAreaType.label.enableStatus')" prop="enableStatus">
          <el-radio-group v-model="formData.enableStatus">
            <el-radio :value="1">{{ $t('warehouseAreaType.status.enabled') }}</el-radio>
            <el-radio :value="0">{{ $t('warehouseAreaType.status.disabled') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="drawer-footer">
          <el-button @click="handleClose">
            {{ $t("warehouseAreaType.button.cancel") }}
          </el-button>
          <el-button type="primary" :loading="formLoading" @click="handleSubmit">
            {{ $t("warehouseAreaType.button.save") }}
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "WarehouseAreaType",
  inheritAttrs: false,
});

import WarehouseAreaStorageTypesAPI, {
  WarehouseAreaStorageTypesPageQuery,
  WarehouseAreaStorageTypesVO,
  WarehouseAreaStorageTypesForm,
  WarehouseAreaStorageTypesPageResult,
  EnableStatus
} from "@/modules/goods/api/warehouseAreaStorageTypes";
import type { FormInstance, TableInstance } from "element-plus";
import { parseDateTime } from "@/core/utils";

const { t } = useI18n();
const queryFormRef = ref<FormInstance>();
const dataTableRef = ref<TableInstance>();
const formRef = ref<FormInstance>();
const loading = ref(false);
const formLoading = ref(false);
const total = ref(0);
const dialogVisible = ref(false);
const isEdit = ref(false);

// 查询参数
const queryParams = reactive<WarehouseAreaStorageTypesPageQuery>({
  page: 1,
  limit: 20,
  typeName: ""
});

// 库区存储类型列表
const warehouseAreaTypeList = ref<WarehouseAreaStorageTypesVO[]>([]);

// 多选数据
const multipleSelection = ref<WarehouseAreaStorageTypesVO[]>([]);

// 表单数据
const formData = reactive<WarehouseAreaStorageTypesForm>({
  typeCode: "",
  typeName: "",
  sort: 0,
  enableStatus: EnableStatus.ENABLED
});

// 弹窗标题
const dialogTitle = computed(() => {
  return isEdit.value ? t('warehouseAreaType.title.edit') : t('warehouseAreaType.title.add');
});

// 表单验证规则
const formRules = {
  typeCode: [
    { required: true, message: t('warehouseAreaType.rules.typeCode'), trigger: 'blur' }
  ],
  typeName: [
    { required: true, message: t('warehouseAreaType.rules.typeName'), trigger: 'blur' }
  ],
  sort: [
    { required: true, message: t('warehouseAreaType.rules.sort'), trigger: 'blur' },
    { type: 'number', message: t('warehouseAreaType.rules.sortNumber'), trigger: 'blur' }
  ]
};

/** 查询库区存储类型列表 */
function handleQuery() {
  loading.value = true;
  WarehouseAreaStorageTypesAPI.getPageList(queryParams)
    .then((data: WarehouseAreaStorageTypesPageResult) => {
      warehouseAreaTypeList.value = data.records;
      total.value = parseInt(data.total);
    })
    .catch(() => {
      ElMessage.error(t("warehouseAreaType.message.loadFailed"));
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery();
}

/** 处理多选变化 */
function handleSelectionChange(selection: WarehouseAreaStorageTypesVO[]) {
  multipleSelection.value = selection;
}

/** 处理状态变更 */
function handleStatusChange(row: WarehouseAreaStorageTypesVO) {
  if (row.id) {
    if (row.enableStatus === EnableStatus.ENABLED) {
      WarehouseAreaStorageTypesAPI.enableBatch([row.id]).then(() => {
        ElMessage.success(t("warehouseAreaType.message.enableSuccess"));
        handleQuery();
      }).catch(() => {
        // 状态切换失败，恢复原状态
        row.enableStatus = EnableStatus.DISABLED;
        // ElMessage.error(t("warehouseAreaType.message.operationFailed"));
      });
    }
    else if (row.enableStatus === EnableStatus.DISABLED) {
      ElMessageBox.confirm(
        t("warehouseAreaType.message.disableBatchConfirm"),
        t("common.tipTitle"),
        {
          confirmButtonText: t("common.confirm"),
          cancelButtonText: t("common.cancel"),
          type: "warning",
        }
      ).then(() => {
        WarehouseAreaStorageTypesAPI.disableBatch([row.id]).then(() => {
          ElMessage.success(t("warehouseAreaType.message.disableSuccess"));
          handleQuery();
        }).catch(() => {
          // 状态切换失败，恢复原状态
          row.enableStatus = EnableStatus.ENABLED;
          // ElMessage.error(t("warehouseAreaType.message.operationFailed"));
        });
      });






    }
  }
}

/** 新增 */
function handleAdd() {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
}

/** 编辑 */
function handleEdit(row: WarehouseAreaStorageTypesVO) {
  isEdit.value = true;
  Object.assign(formData, row);
  delete formData.tenantId;
  dialogVisible.value = true;
}

/** 删除 */
function handleDelete(row: WarehouseAreaStorageTypesVO) {
  ElMessageBox.confirm(
    t("warehouseAreaType.message.deleteConfirm"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
    WarehouseAreaStorageTypesAPI.deleteById(row.id!)
      .then(() => {
        ElMessage.success(t("warehouseAreaType.message.deleteSuccess"));
        handleQuery();
      })
      .catch(() => {
        // ElMessage.error(t("warehouseAreaType.message.operationFailed"));
      });
  });
}

/** 批量启用 */
function handleBatchEnable() {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(t("warehouseAreaType.message.selectTip"));
    return;
  }
  const ids = multipleSelection.value.map(item => item.id!);
  WarehouseAreaStorageTypesAPI.enableBatch(ids)
    .then(() => {
      ElMessage.success(t("warehouseAreaType.message.enableSuccess"));
      handleQuery();
    })
    .catch((error) => {
      // ElMessage.error(error.message || t("warehouseAreaType.message.operationFailed"));
    });
  /*  ElMessageBox.confirm(
     t("warehouseAreaType.message.enableBatchConfirm"),
     t("common.tipTitle"),
     {
       confirmButtonText: t("common.confirm"),
       cancelButtonText: t("common.cancel"),
       type: "warning",
     }
   ).then(() => {
   
   }); */
}

/** 批量禁用 */
function handleBatchDisable() {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(t("warehouseAreaType.message.selectTip"));
    return;
  }

  ElMessageBox.confirm(
    t("warehouseAreaType.message.disableBatchConfirm"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
    const ids = multipleSelection.value.map(item => item.id!);
    WarehouseAreaStorageTypesAPI.disableBatch(ids)
      .then(() => {
        ElMessage.success(t("warehouseAreaType.message.disableSuccess"));
        handleQuery();
      })
      .catch(() => {
        // ElMessage.error(t("warehouseAreaType.message.operationFailed"));
      });
  });
}

/** 提交表单 */
function handleSubmit() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      formLoading.value = true;
      const apiCall = isEdit.value ?
        WarehouseAreaStorageTypesAPI.update(formData) :
        WarehouseAreaStorageTypesAPI.add(formData);

      apiCall.then(() => {
        ElMessage.success(isEdit.value ?
          t("warehouseAreaType.message.editSuccess") :
          t("warehouseAreaType.message.addSuccess"));
        dialogVisible.value = false;
        handleQuery();
      }).catch((error) => {
        // ElMessage.error(error.message || t("warehouseAreaType.message.operationFailed"));
      }).finally(() => {
        formLoading.value = false;
      });
    }
  });
}

/** 关闭弹窗 */
function handleClose() {
  resetForm();
  dialogVisible.value = false;
}

/** 重置表单 */
function resetForm() {
  Object.assign(formData, {
    id: undefined,
    typeCode: "",
    typeName: "",
    sort: 0,
    enableStatus: EnableStatus.ENABLED,
  });
  formRef.value?.resetFields();
  formRef.value?.clearValidate();
}

/** 格式化时间 */
function formatDateTime(time: string) {
  return parseDateTime(time, "dateTime");
}

// 页面加载时查询数据
onMounted(() => {
  handleQuery();
});
</script>

<style scoped lang="scss">
.app-container {
  .search-container {
    background: #ffffff;
    padding: 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .table-container {
    margin-bottom: 20px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: 16px;

      .header-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.drawer-footer {
  text-align: right;
  padding: 0 20px 20px 0;
}

// 开关组件样式调整
:deep(.el-switch__label) {
  font-size: 12px;
}

:deep(.el-switch__label.is-active) {
  color: #13ce66;
}

:deep(.el-switch__label:not(.is-active)) {
  color: #ff4949;
}
</style>
