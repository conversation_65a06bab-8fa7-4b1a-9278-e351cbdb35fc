<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="80px">
        <el-row>
          <el-form-item :label="$t('productConfig.label.field')" prop="fieldCode">
            <el-select v-model="queryParams.fieldCode" :placeholder="$t('productConfig.placeholder.fieldPlaceholder')"
              clearable style="width: 240px">
              <el-option v-for="item in fieldEnumList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('productConfig.label.typeName')" prop="typeName">
            <el-input v-model="queryParams.typeName" :placeholder="$t('productConfig.placeholder.typeNamePlaceholder')"
              clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" v-hasPerm="['wms:product:cnfig:search']">
              {{ $t("productConfig.button.search") }}
            </el-button>
            <el-button @click="handleResetQuery" v-hasPerm="['wms:product:cnfig:search']">
              {{ $t("productConfig.button.reset") }}
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="table-header">
          <div class="header-buttons">
            <el-button type="danger" :disabled="multipleSelection.length === 0" @click="handleBatchDisable"
              v-hasPerm="['wms:product:cnfig:disable']">
              {{ $t("productConfig.button.disable") }}
            </el-button>
            <el-button type="success" :disabled="multipleSelection.length === 0" @click="handleBatchEnable"
              v-hasPerm="['wms:product:cnfig:enable']">
              {{ $t("productConfig.button.enable") }}
            </el-button>
            <el-button type="primary" @click="handleAdd" v-hasPerm="['wms:product:cnfig:add']">
              {{ $t("productConfig.button.add") }}
            </el-button>
          </div>
        </div>
      </template>

      <el-table ref="dataTableRef" v-loading="loading" :data="productConfigList" stripe highlight-current-row
        @selection-change="handleSelectionChange">
        <template #empty>
          <Empty />
        </template>

        <el-table-column type="selection" width="55" align="center" />

        <el-table-column type="index" :label="$t('common.sort')" width="60" align="center" />

        <el-table-column :label="$t('productConfig.label.field')" prop="fieldName" width="150" show-overflow-tooltip />

        <el-table-column :label="$t('productConfig.label.typeName')" prop="typeName" min-width="200"
          show-overflow-tooltip />

        <el-table-column :label="$t('productConfig.label.sort')" prop="sort" width="100" align="center" />

        <el-table-column :label="$t('productConfig.label.enableStatus')" width="150" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.enableStatus" :active-value="1" :inactive-value="0"
              :active-text="$t('productConfig.status.enabled')" :inactive-text="$t('productConfig.status.disabled')"
              inline-prompt style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column :label="$t('productConfig.label.updateTime')" prop="updateTime" width="180"
          show-overflow-tooltip>
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('productConfig.label.operation')" fixed="right" align="center" width="120">
          <template #default="scope">
            <el-button type="primary"  link @click="handleEdit(scope.row)"
              v-hasPerm="['wms:product:cnfig:edit']">
              {{ $t("productConfig.button.edit") }}
            </el-button>
            <el-button type="danger"  link @click="handleDelete(scope.row)"
              v-hasPerm="['wms:product:cnfig:del']">
              {{ $t("productConfig.button.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.page"
        v-model:limit="queryParams.limit" :page-sizes="[20, 50, 100, 200]" @pagination="handleQuery" />

      <!-- 底部保存按钮 -->
      <!-- <div class="footer-container">
        <el-button
          type="primary"
          size="large"
          :loading="saveLoading"
          @click="handleSave"
          v-hasPerm="['goods:productConfig:save']"
        >
          {{ $t("productConfig.button.save") }}
        </el-button>
      </div> -->
    </el-card>

    <!-- 编辑弹窗 -->
    <el-drawer v-model="dialogVisible" :title="dialogTitle" direction="rtl" size="500px" :close-on-click-modal="false"
      @close="handleClose">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" label-position="top">
        <el-form-item :label="$t('productConfig.label.field')" prop="fieldCode">
          <el-select v-model="formData.fieldCode" :placeholder="$t('productConfig.placeholder.fieldPlaceholder')"
            clearable class="w-full" @change="handleFieldCodeChange">
            <el-option v-for="item in fieldEnumList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('productConfig.label.typeName')" prop="typeName">
          <el-input v-model="formData.typeName" :placeholder="$t('productConfig.placeholder.typeNamePlaceholder')"
            clearable />
        </el-form-item>

        <el-form-item :label="$t('productConfig.label.sort')" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" :max="9999" :step="1"
            :placeholder="$t('productConfig.placeholder.sortPlaceholder')" controls-position="right"
            class="w-full" />
        </el-form-item>

        <el-form-item :label="$t('productConfig.label.enableStatus')" prop="enableStatus">
          <el-radio-group v-model="formData.enableStatus">
            <el-radio :value="1">{{ $t('productConfig.status.enabled') }}</el-radio>
            <el-radio :value="0">{{ $t('productConfig.status.disabled') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="drawer-footer">
          <el-button @click="handleClose">
            {{ $t("productConfig.button.cancel") }}
          </el-button>
          <el-button type="primary" :loading="formLoading" @click="handleSubmit">
            {{ $t("productConfig.button.save") }}
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "ProductConfig",
  inheritAttrs: false,
});

import ProductAttributesAPI, {
  ProductAttributesPageQuery,
  ProductAttributesVO,
  ProductAttributesForm,
  ProductAttributesPageResult,
  EnableStatus
} from "@/modules/goods/api/productAttributes";
import type { FormInstance, TableInstance } from "element-plus";
import { parseDateTime } from "@/core/utils";

const { t } = useI18n();
const queryFormRef = ref<FormInstance>();
const dataTableRef = ref<TableInstance>();
const formRef = ref<FormInstance>();
const loading = ref(false);
const formLoading = ref(false);
const saveLoading = ref(false);
const total = ref(0);
const dialogVisible = ref(false);
const isEdit = ref(false);

// 查询参数
const queryParams = reactive<ProductAttributesPageQuery>({
  page: 1,
  limit: 20,
  fieldName: "",
  fieldCode: "",
  typeName: ""
});

// 商品配置列表
const productConfigList = ref<ProductAttributesVO[]>([]);

// 多选数据
const multipleSelection = ref<ProductAttributesVO[]>([]);

// 修改状态记录
const modifiedParams = ref<Map<number, ProductAttributesVO>>(new Map());

// 字段枚举列表
const fieldEnumList = ref<string[]>([]);

// 表单数据
const formData = reactive<ProductAttributesForm>({
  fieldName: "",
  fieldCode: "",
  typeName: "",
  sort: 0,
  enableStatus: EnableStatus.ENABLED
});

// 弹窗标题
const dialogTitle = computed(() => {
  return isEdit.value ? t('productConfig.title.edit') : t('productConfig.title.add');
});

// 表单验证规则
const formRules = {
  fieldCode: [
    { required: true, message: t('productConfig.rules.fieldName'), trigger: 'change' }
  ],
  typeName: [
    { required: true, message: t('productConfig.rules.typeName'), trigger: 'blur' }
  ],
  sort: [
    { required: true, message: t('productConfig.rules.sort'), trigger: 'blur' },
    { type: 'number', message: t('productConfig.rules.sortNumber'), trigger: 'blur' }
  ]
};

const handleFieldCodeChange = (value: string) => {
  formData.fieldName = fieldEnumList.value.find(item => item.code === value)?.name || '';
}
/** 获取字段枚举列表 */
function getFieldEnumList() {
  ProductAttributesAPI.getFieldEnumList()
    .then((data: string[]) => {
      fieldEnumList.value = data;
    })
    .catch(() => {
      // ElMessage.error(t("productConfig.message.loadFailed"));
    });
}

/** 查询商品配置列表 */
function handleQuery() {
  loading.value = true;
  ProductAttributesAPI.getPageList(queryParams)
    .then((data: ProductAttributesPageResult) => {
      productConfigList.value = data.records;
      total.value = parseInt(data.total);
      // 清空修改记录
      modifiedParams.value.clear();
    })
    .catch(() => {
      // ElMessage.error(t("productConfig.message.loadFailed"));
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery();
}

/** 处理多选变化 */
function handleSelectionChange(selection: ProductAttributesVO[]) {
  multipleSelection.value = selection;
}

/** 处理状态变更 */
function handleStatusChange(row: ProductAttributesVO) {
  if (row.id) {
    if (row.enableStatus === EnableStatus.ENABLED) {
      ProductAttributesAPI.enableBatch([row.id]).then(() => {
        ElMessage.success(t("productConfig.message.enableSuccess"));
        handleQuery();
      }).catch(() => {
        ElMessage.error(t("productConfig.message.operationFailed"));
        // 失败时恢复状态
        row.enableStatus = EnableStatus.DISABLED;
      });
    }
    else if (row.enableStatus === EnableStatus.DISABLED) {
      ElMessageBox.confirm(
        t("productConfig.message.disableBatchConfirm"),
        t("common.tipTitle"),
        {
          confirmButtonText: t("common.confirm"),
          cancelButtonText: t("common.cancel"),
          type: "warning",
        }
      ).then(() => {
        ProductAttributesAPI.disableBatch([row.id]).then(() => {
          ElMessage.success(t("productConfig.message.disableSuccess"));
          handleQuery();
        }).catch(() => {
          // ElMessage.error(t("productConfig.message.operationFailed"));
          // 失败时恢复状态
          row.enableStatus = EnableStatus.ENABLED;
        });
      }).catch(() => {
        // 用户取消时恢复状态
        row.enableStatus = EnableStatus.ENABLED;
      });
    }
  }
}

/** 新增 */
function handleAdd() {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
}

/** 编辑 */
function handleEdit(row: ProductAttributesVO) {
  isEdit.value = true;
  Object.assign(formData, row);
  dialogVisible.value = true;
}

/** 删除 */
function handleDelete(row: ProductAttributesVO) {
  ElMessageBox.confirm(
    t("productConfig.message.deleteConfirm"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
    ProductAttributesAPI.deleteById(row.id!)
      .then(() => {
        ElMessage.success(t("productConfig.message.deleteSuccess"));
        handleQuery();
      })
      .catch(() => {
        // ElMessage.error(t("productConfig.message.operationFailed"));
      });
  });
}

/** 批量启用 */
function handleBatchEnable() {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(t("productConfig.message.selectTip"));
    return;
  }
  ProductAttributesAPI.enableBatch(ids)
    .then(() => {
      ElMessage.success(t("productConfig.message.enableSuccess"));
      handleQuery();
    })
    .catch(() => {
      // ElMessage.error(t("productConfig.message.operationFailed"));
    });
  /* ElMessageBox.confirm(
    t("productConfig.message.enableBatchConfirm"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
   
  }); */
}

/** 批量禁用 */
function handleBatchDisable() {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(t("productConfig.message.selectTip"));
    return;
  }

  ElMessageBox.confirm(
    t("productConfig.message.disableBatchConfirm"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
    const ids = multipleSelection.value.map(item => item.id!);
    ProductAttributesAPI.disableBatch(ids)
      .then(() => {
        ElMessage.success(t("productConfig.message.disableSuccess"));
        handleQuery();
      })
      .catch(() => {
        // ElMessage.error(t("productConfig.message.operationFailed"));
      });
  });
}

/** 批量保存 */
function handleSave() {
  if (modifiedParams.value.size === 0) {
    ElMessage.warning(t("productConfig.message.noChangesToSave"));
    return;
  }

  ElMessageBox.confirm(
    t("productConfig.message.confirmSave"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(async () => {
    saveLoading.value = true;

    try {
      // 批量更新所有修改的参数
      const updatePromises = Array.from(modifiedParams.value.values()).map(param =>
        ProductAttributesAPI.update({
          id: param.id,
          fieldName: param.fieldName,
          typeName: param.typeName,
          sort: param.sort,
          enableStatus: param.enableStatus,
        })
      );

      await Promise.all(updatePromises);

      ElMessage.success(t("productConfig.message.saveSuccess"));
      modifiedParams.value.clear();
      handleQuery(); // 重新加载数据
    } catch (error) {
      // ElMessage.error(t("productConfig.message.operationFailed"));
    } finally {
      saveLoading.value = false;
    }
  }).catch(() => {
    ElMessage.info(t("common.cancel"));
  });
}

/** 提交表单 */
function handleSubmit() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      formLoading.value = true;
      const apiCall = isEdit.value ?
        ProductAttributesAPI.update(formData) :
        ProductAttributesAPI.add(formData);

      apiCall.then(() => {
        ElMessage.success(isEdit.value ?
          t("productConfig.message.editSuccess") :
          t("productConfig.message.addSuccess"));
        dialogVisible.value = false;
        handleQuery();
      }).catch(() => {
        // ElMessage.error(t("productConfig.message.operationFailed"));
      }).finally(() => {
        formLoading.value = false;
      });
    }
  });
}

/** 关闭弹窗 */
function handleClose() {
  dialogVisible.value = false;
  resetForm();
}

/** 重置表单 */
function resetForm() {
  Object.assign(formData, {
    id: undefined,
    fieldName: "",
    fieldCode: "",
    typeName: "",
    sort: 0,
    enableStatus: EnableStatus.ENABLED,
  });
  formRef.value?.resetFields();
  formRef.value?.clearValidate();
}

/** 格式化时间 */
function formatDateTime(time: string) {
  return parseDateTime(time, "dateTime");
}

// 页面加载时查询数据
onMounted(() => {
  getFieldEnumList();
  handleQuery();
});
</script>

<style scoped lang="scss">
.app-container {
  .search-container {
    background: #ffffff;
    padding: 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .table-container {
    margin-bottom: 20px;

    .table-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-weight: 500;
      font-size: 16px;

      .header-buttons {
        display: flex;
        gap: 8px;
      }
    }

    .footer-container {
      display: flex;
      justify-content: center;
      padding: 20px;
      border-top: 1px solid #e5e7f3;
      margin-top: 20px;
    }
  }
}

.drawer-footer {
  text-align: right;
  padding: 0 20px 20px 0;
}

// 开关组件样式调整
:deep(.el-switch__label) {
  font-size: 12px;
}

:deep(.el-switch__label.is-active) {
  color: #13ce66;
}

:deep(.el-switch__label:not(.is-active)) {
  color: #ff4949;
}
</style>
