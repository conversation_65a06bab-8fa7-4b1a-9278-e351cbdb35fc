export function useCommon() {
  const currencyFilter = (val: string) => { // 货币符号过滤器
    if (val === "CNY") {
      return "￥";
    }
    else if(val === 'USD'){
      return "$";
    }
    return "--";
  };
  // 金额输入框限制
  // negativeFlag true 可以输入负数
  const moneyChange = (data, key, negativeFlag = false) => {
    let n = String(data[key]);
    const t = n.charAt(0);
    n = n.replace(/[^\d\.]/g, '')
    // 必须保证第一个为数字而不是.
    n = n.replace(/^\./g, '')
    // 保证只有出现一个.而没有多个.
    n = n.replace(/\.{2,}/g, '.')
    // 保证.只出现一次，而不能出现两次以上
    n = n.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
    // 如果第一位是负号，则允许添加
    if(t === '-' && negativeFlag) {
      n = '-' + n;
    }
    const parts = n.split('.');
    if(parts.length>2) {
      n = parts[0] + '.' + parts[1].slice(0,2);
    } else if(parts.length === 2) {
      n = parts[0] + '.' + parts[1].slice(0,2);
    }
    data[key] = n;
  };

  // 数字输入框限制
  const numberChange = (data, key) => {
    data[key]=data[key].replace(/[^0-9]/g, '');
  };


  /**
   * 格式化金额 - 数字千分化，保留两位小数
   * @param {number | string} num - 输入的数字或字符串
   * @returns {string} - 格式化后的金额字符串，空值时返回 '0.00'
   */
  function numToMoney(num) {
    // 检查输入是否为空或无效数字
    if (num === null || num === undefined || num === '' || isNaN(Number(num))) {
      return '0.00'; // 空值或非法数字返回 0.00
    }

    // 转换为数字类型并处理精度问题
    const value = Math.round(Number(num) * 100) / 100; // 先放大100倍取整，避免浮点数精度问题
    let [integerPart, decimalPart] = value.toString().split('.'); // 分割整数和小数部分

    // 如果小数部分不存在，补全为两位
    decimalPart = decimalPart ? decimalPart.padEnd(2, '0') : '00';

    // 对整数部分进行千分位格式化
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    // 合并结果并返回
    return `${formattedInteger}.${decimalPart}`;
  }
  // 转换中文大写
  function convertCurrency(money) {
    const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const cnIntRadice = ['', '拾', '佰', '仟'];
    const cnIntUnits = ['', '万', '亿', '兆'];
    const cnDecUnits = ['角', '分', '毫', '厘'];
    const cnInteger = '整';
    const cnIntLast = '元';
    const maxNum = 999999999999999.9999;
    let integerNum;
    let decimalNum;
    let chineseStr = '';
    let parts;
    if (money === '') {
      return '';
    }
    money = parseFloat(money);
    if (money >= maxNum) {
      return '';
    }
    if (money === 0) {
      chineseStr = cnNums[0] + cnIntLast + cnInteger;
      return chineseStr;
    }
    money = money.toString();
    if (money.indexOf('.') === -1) {
      integerNum = money;
      decimalNum = '';
    } else {
      parts = money.split('.');
      integerNum = parts[0];
      decimalNum = parts[1].substr(0, 4);
    }
    if (parseInt(integerNum, 10) > 0) {
      let zeroCount = 0;
      const IntLen = integerNum.length;
      for (let i = 0; i < IntLen; i++) {
        const n = integerNum.substr(i, 1);
        const p = IntLen - i - 1;
        const q = p / 4;
        const m = p % 4;
        if (n === '0') {
          zeroCount++;
        } else {
          if (zeroCount > 0) {
            chineseStr += cnNums[0];
          }
          zeroCount = 0;
          chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
        }
        if (m === 0 && zeroCount < 4) {
          chineseStr += cnIntUnits[q];
        }
      }
      chineseStr += cnIntLast;
    }
    if (decimalNum !== '') {
      const decLen = decimalNum.length;
      for (let i = 0; i < decLen; i++) {
        const n = decimalNum.substr(i, 1);
        if (n !== '0') {
          chineseStr += cnNums[Number(n)] + cnDecUnits[i];
        }
      }
    }
    if (chineseStr === '') {
      chineseStr += cnNums[0] + cnIntLast + cnInteger;
    } else if (decimalNum === '') {
      chineseStr += cnInteger;
    }
    return chineseStr;
  }
  
  return {
    currencyFilter,
    numberChange,
    moneyChange,
    numToMoney,
    convertCurrency
  }
}
