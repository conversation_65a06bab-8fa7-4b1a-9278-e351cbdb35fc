<template>
  <el-dialog v-model="visible" title="选择现金流项目" width="560" class="finance-moudle">
    <div class="dialog-content cashflow-dialog">
      <el-input v-model="keyWord" style="width: 100%" placeholder="请输入关键字搜索">
        <template #prefix>
          <el-icon class="el-input__icon"><search /></el-icon>
        </template>
      </el-input>
      <div class="summary-title">现金流内容</div>
      <div class="summary-list">
        <div class="first-item" v-for="item in filterCashFlowList" :key="item.itemCode">
          <div class="first-label">{{ item.itemName }}</div>
          <el-radio-group v-model="selectedVal">
            <el-radio v-for="radio in item.childList" :key="radio.itemCode" :value="radio.itemName">{{ radio.itemName }}</el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelHandler">取消</el-button>
        <el-button type="primary" @click="confirmHandler">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { voucher } from '@/modules/finance/api/index';
  import { TCashFlowItem } from "@/modules/finance/types/voucher";
  const visible = defineModel();
  const emits = defineEmits(['confirm']);
  const cashFlowList = ref<TCashFlowItem[]>([]);
  const getCashFlowList = async () => {
    try {
      const res = await voucher.queryCashFlowList({});
      cashFlowList.value = res;
    } catch(err) {
    }
  }
  const keyWord = ref('');
  const selectedVal = ref('');
  const filterCashFlowList = computed(() => {
    if (keyWord.value === '') {
      return cashFlowList.value;
    } else {
      const filterList = cashFlowList.value.map(item => {
        return {
          ...item,
          childList: item.childList!.filter(child => child.itemName.includes(keyWord.value))
        }
      })
      return filterList?.length ? filterList : [];
    }
  })
  const confirmHandler = () => {
    if (selectedVal.value) {
      emits('confirm', selectedVal.value)
    }
    cancelHandler();
  }
  const cancelHandler = () => {
    keyWord.value = '';
    selectedVal.value = '';
    visible.value = false;
  }
  onMounted(() => {
    getCashFlowList();
  })
</script>

<style lang="scss">
  .cashflow-dialog {
    .summary-title {
      margin-top: 16px;
      background: #F4F6FA;
      border-radius: 2px 2px 0px 0px;
      border: 1px solid #D9DCEB;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #52585F;
    }
    .summary-list {
      border: 1px solid #D9DCEB;
      border-top: none;
      height: 400px;
      overflow-y: auto;
      .first-item:last-child .el-radio-group .el-radio:last-child{
        border-bottom: none;
      }
      .first-label {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #151719;
        line-height: 22px;
        padding: 12px;
      }
      .el-radio-group {
        display: block;
        .el-radio {
          display: block;
          line-height: 32px;
          height: 32px;
          padding-left: 16px;
          border-bottom: 1px solid #D9DCEB;
          margin-right: 0;
        }
      }
    }
  }
</style>