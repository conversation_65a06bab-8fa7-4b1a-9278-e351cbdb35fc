<template>
  <div>
    <div>
      <el-select
        ref="countryRef"
        v-model="countryInfo"
        :placeholder="$t('warehouse.placeholder.countryInputTips')"
        style="width: 130px"
        @change="handleSelectChange"
        filterable
      >
        <el-option
          v-for="item in countryList"
          :key="item.id"
          :label="item.shortName"
          :value="item.id"
        />
      </el-select>
      <el-cascader
        ref="areaRef"
        :disabled="countryInfo.length === 0"
        :placeholder="$t('warehouse.placeholder.areaInputTips')"
        :props="areaProps"
        v-model="areaInfo"
        style="padding-left: 10px; width: 200px"
        @change="handleCascaderChange"
        popper-class="finance-address-area-detail-content"
        :key="refresh"
        clearable
      >
        <template #default="{ node, data }">
          <div class="defaultData" @click="clickNode">
            {{ data.shortName }}
          </div>
        </template>
      </el-cascader>
    </div>
    <!-- <el-input type="text" :placeholder="$t('warehouse.placeholder.detailAddressInputTips')" v-model="desAddress"
      :maxlength="20" style="padding-top: 10px;" @change="desAddressChange" clearable /> -->
  </div>
</template>
<script lang="ts" setup>
import type { CascaderProps } from "element-plus";
import CommonAPI, { AreaInfo, AreaListQuery } from "@/modules/wms/api/common";

let props = defineProps({
  defaultCountryInfo: {
    type: String,
    require: false,
  },
  defaultAreaInfo: {
    type: Array,
    require: false,
  },
  defaultDesAddressInfo: {
    type: String,
    require: false,
  },
});

const emit = defineEmits([
  "getCountryInfo",
  "getAreaInfo",
  "getDesAddressInfo",
]);

const countryList = ref<AreaInfo[]>([]);

const countryRef = ref();
const areaRef = ref();
const refresh = ref(0);

const countryInfo = ref("");
const areaInfo = ref([]);
const desAddress = ref("");

const queryParams = reactive<AreaListQuery>({
  pid: "0",
});
function loadCountrys() {
  queryParams.pid = "0";
  CommonAPI.getAreaList(queryParams)
    .then((data) => {
      refresh.value++;
      countryList.value = data;
      countryInfo.value = props.defaultCountryInfo;
      areaInfo.value = props.defaultAreaInfo;
      desAddress.value = props.defaultDesAddressInfo;
    })
    .finally(() => {});
}
function clickNode(e) {
  // 模拟点击对应的radio
  e.target.parentElement.parentElement.firstElementChild.click();
}
const areaProps: CascaderProps = {
  label: "shortName",
  value: "id",
  lazy: true,
  checkStrictly: true,

  lazyLoad(node, resolve) {
    const { level } = node;
    if (countryInfo.value.length > 0) {
      queryParams.pid = countryInfo.value;
      if (level > 0) {
        queryParams.pid = node.value;
      }
      CommonAPI.getAreaList(queryParams)
        .then((data) => {
          data.map((item) => {
            if (level >= 2) {
              item.leaf = true;
            }
          });
          resolve(data);
        })
        .finally(() => {});
    }
  },
};
function handleSelectChange() {
  refresh.value++;
  areaInfo.value = [];

  let data = countryList.value.find((item) => item.id === countryInfo.value);
  emit("getCountryInfo", data);
  emit("getAreaInfo", []);
  emit("getDesAddressInfo", desAddress);
}
function handleCascaderChange() {
  let values = areaRef.value.getCheckedNodes();
  if (values && values.length) {
    const area = {
      pathValues: values[0].pathValues,
      pathLabels: values[0].pathLabels,
    };
    emit("getAreaInfo", area);
    emit("getDesAddressInfo", desAddress);
  }
}
function desAddressChange() {
  emit("getDesAddressInfo", desAddress);
}
const clearMethod = () => {
  countryInfo.value = "";
  areaInfo.value = [];
  desAddress.value = "";
};
const editMethod = () => {
  countryInfo.value = props.defaultCountryInfo;
  areaInfo.value = props.defaultAreaInfo;
  desAddress.value = props.defaultDesAddressInfo;
};
defineExpose({
  clearMethod,
  editMethod,
});
onMounted(() => {
  loadCountrys();
});
</script>
<style lang="scss">
.span-click {
  width: 100%;
}

// 隐藏单选框 - 仅影响当前组件
.finance-address-area-detail-content .el-cascader-panel .el-radio__input {
  display: none;
}

.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}
</style>
