<template>
  <div>
    <el-dialog v-model="visible" title="选择科目" width="560" class="finance-moudle">
      <div class="dialog-content subject-dialog">
        <el-input v-model="keyword" style="width: 100%;margin-bottom: 16px;" placeholder="请输入关键词">
          <template #prefix>
            <el-icon class="el-input__icon"><search /></el-icon>
          </template>
        </el-input>
        <el-radio-group v-model="selectedType" @change="subjectTypeChangeHandler">
          <el-radio-button v-for="(item, index) in subjectTypeList" :key="index" :value="item.type">{{ item.label }}</el-radio-button>
        </el-radio-group>
        <div class="subject-list" v-loading="loading">
          <el-radio-group v-model="selectedSubjectId">
            <el-radio v-for="(item, index) in filterSubjectList" :key="index" :value="item.id">{{ `${item.subjectCode} ${item.subjectFullName}` }}</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelHandler">取消</el-button>
          <el-button type="primary" @click="confirmHandler">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { voucher } from '@/modules/finance/api/index';
  import type { TSubjectItem } from "@/modules/finance/types/voucher";
  const visible = defineModel();
  const loading = ref(false);
  const emits = defineEmits(['confirm']);
  // const props = defineProps(['modelValue']);
  // const emits = defineEmits(['update:modelValue']);
  const keyword = ref('');
  const selectedType = ref(1);
  const subjectTypeList = [
    { label: '资产', type: 1 },
    { label: '负债', type: 2 },
    { label: '权益', type: 3 },
    { label: '成本', type: 4 },
    { label: '损益', type: 5 }
  ]
  const subjectList = ref<TSubjectItem[]>([]);
  const filterSubjectList = computed(() => {
    if (keyword.value === '') {
      return subjectList.value;
    } else {
      return subjectList.value.filter(item => (item.subjectCode.includes(keyword.value) || item.subjectFullName.includes(keyword.value)));
    }
  })
  const selectedSubjectId = ref('');
  const confirmHandler = () => {
    if (selectedSubjectId.value) {
      const item = subjectList.value.find(item => item.id === selectedSubjectId.value);
      emits('confirm',item)
    }
    cancelHandler();
  }
  const subjectTypeChangeHandler = () => {
    selectedSubjectId.value = '';
    getSubjectList();
  }
  const cancelHandler = () => {
    selectedSubjectId.value = '';
    visible.value = false;
  }
  const getSubjectList = async () => {
    loading.value = true;
    try {
      const params = {
        showTree: false,
        subjectType: selectedType.value,
        status: 1
      }
      const res = await voucher.querySubjectList(params);
      subjectList.value = res;
      loading.value = false;
    } catch(err) {
      loading.value = false;
    }
  }
  onMounted(() => {
    getSubjectList();
  })
</script>

<style lang="scss">
  .subject-dialog {
    .subject-list {
      border: 1px solid #D9DCEB;
      height: 250px;
      overflow-y: auto;
      margin-top: 16px;
      .el-radio-group {
        display: block;
        .el-radio {
          display: block;
          line-height: 32px;
          height: 32px;
          padding-left: 16px;
          border-bottom: 1px solid #D9DCEB;
          margin-right: 0;
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
</style>