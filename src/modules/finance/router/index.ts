/*
 * @Author: ch<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-25 10:37:01
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-03-27 14:46:27
 * @FilePath: \supply-manager-web\src\modules\goods\router\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { RouteRecordRaw } from "vue-router";

export const Layout = () => import("@/core/layout/index.vue");

// 路由配置
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/finance/generalLedger',
    component: Layout,
    children: [
      {
        path: "accountsDetail",
        component: () => import("@finance/views/generalLedger/setAccounts/accountsDetail.vue"),
        meta: {
          title: "账套信息",
          hidden: true,
        },
      },
    ]
  }
];
