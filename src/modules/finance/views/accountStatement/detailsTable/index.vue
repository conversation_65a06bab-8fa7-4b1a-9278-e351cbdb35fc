<template>
  <div class="detailsTable">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true" label-width="84px">
        <el-form-item :label="t('detailsTable.label.accountingPeriod')">
          <el-date-picker
            :clearable="false"
            @change="monthRangeChange"
            v-model="searchForm.period"
            type="monthrange"
            range-separator="至"
            :start-placeholder="t('bankJournal.label.startDate')"
            :end-placeholder="t('bankJournal.label.endDate')"
            format="YYYY-MM"
            value-format="YYYYMM"
            :disabled-date="
              (date) => {
                const start = loadPeriodList.periodYearMonthStart;
                const end = loadPeriodList.periodYearMonthEnd;
                if (!start || !end) return false;
                const y = date.getFullYear();
                const m = (date.getMonth() + 1).toString().padStart(2, '0');
                const ym = `${y}${m}`;
                return ym < start || ym > end;
              }
            "
          />
        </el-form-item>
        <el-form-item :label="t('detailsTable.label.startAccount')" prop="subjectCodeStart">
          <el-select clearable filterable v-model="searchForm.subjectCodeStart" :placeholder="t('detailsTable.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="category in cashAccountList" :key="category.subjectCode" :label="category.subjectCode + '_' + category.subjectName" :value="category.subjectCode" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('detailsTable.label.endAccount')" prop="subjectCodeEnd">
          <el-select clearable filterable v-model="searchForm.subjectCodeEnd" :placeholder="t('detailsTable.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="category in cashAccountList" :key="category.subjectCode" :label="category.subjectCode + '_' + category.subjectName" :value="category.subjectCode" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('detailsTable.label.accountLevel')" prop="subjectLevelStart">
          <el-select v-model="searchForm.subjectLevelStart" :placeholder="t('detailsTable.label.pleaseSelect')" style="width: 100px">
            <el-option v-for="category in levelList" :key="category.id" :label="category.label" :value="category.id" />
          </el-select>
          <span style="margin: 0 8px">{{ t('detailsTable.label.to') }}</span>
          <el-select filterable v-model="searchForm.subjectLevelEnd" :placeholder="t('detailsTable.label.pleaseSelect')" style="width: 100px">
            <el-option v-for="category in levelList" :key="category.id" :label="category.label" :value="category.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('detailsTable.label.summary')" prop="summary">
          <el-input clearable v-model="searchForm.summary" :placeholder="t('detailsTable.label.summaryInput')" />
        </el-form-item>
        <!-- <el-form-item :label="t('detailsTable.label.paixu')" prop="orderField">
          <el-radio-group v-model="searchForm.orderField">
            <el-radio :value="0">{{ t('detailsTable.label.voucherNoSort') }}</el-radio>
            <el-radio :value="1">{{ t('detailsTable.label.voucherDateSort') }}</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <!-- <el-form-item :label="t('detailsTable.label.showContent')" prop="checkList">
          <el-select filterable v-model="searchForm.checkList" :placeholder="t('detailsTable.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="category in showContentList" :key="category.statusId" :label="category.statusName" :value="category.statusId" />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="getSubjectByFlowList" v-hasPerm="['finance:detailsTable:search']">
            {{ $t('common.search') }}
          </el-button>
          <el-button @click="onResetHandlerEvent" v-hasPerm="['finance:detailsTable:reset']">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <section class="delivery-method-container">
      <div class="left-panel">
        <div class="category-header">
          {{ t('detailsTable.label.chooseaccountingSubject') }}
        </div>
        <div class="category-list">
          <el-input style="margin-bottom: 18px" @change="selectedCategorySearchChange" v-model="selectedCategorySearch" size="large" :placeholder="t('detailsTable.label.subjectInputPrompt')" :prefix-icon="Search" />
          <el-tree class="el-tree" :data="list" :highlight-current="true" node-key="id" :props="{ label: 'subjectName', children: 'childList' }" @node-click="selectCategory" ref="treeRef">
            <template #default="{ node, data }">
                <el-tooltip v-if="(data.subjectCode + '_' + data.subjectName).length > 25" :content="data.subjectCode + '_' + data.subjectName" placement="top">
                <span style="max-width: 200px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                  {{ data.subjectCode + '_' + data.subjectName }}
                </span>
                </el-tooltip>
                <span v-else>{{ data.subjectCode + '_' + data.subjectName }}</span>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="right-panel">
        <div class="accountingSubject">
          <div>{{ t('detailsTable.label.accountingSubject') }} {{ checkedKeys || '-' }}</div>
        </div>
        <div class="table-container">
          <!-- stripe border -->
          <el-table ref="tableRef" v-loading="loading" :data="methodList" height="100%" stripe border>
            <template #empty>
              <Empty />
            </template>
            <el-table-column type="index" :label="t('detailsTable.table.serialNo')" width="80" align="center" />
            <el-table-column :label="t('detailsTable.table.date')" prop="voucherDate" show-overflow-tooltip />
            <el-table-column :label="t('detailsTable.table.voucherNo')" show-overflow-tooltip>
              <template #default="{ row }">
                <span @click="openVoucherBillDrawer(row.voucherId)" style="color: #762adb;cursor:pointer">{{ row.voucherWord }}<span>-</span>{{ row.voucherNumber }}</span>
              </template></el-table-column
            >
            <el-table-column :label="t('detailsTable.table.account')" prop="voucherNumber" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ `${row.subjectFullCode}_${row.subjectFullName}` }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="t('detailsTable.table.summary')" prop="summary" show-overflow-tooltip />
            <el-table-column :label="t('detailsTable.table.debit')" prop="debitAmount" show-overflow-tooltip />
            <el-table-column :label="t('detailsTable.table.credit')" prop="creditAmount" show-overflow-tooltip />
            <el-table-column :label="t('detailsTable.table.direction')" prop="balanceDirection" show-overflow-tooltip>
              <template #default="scope">
                <div
                  v-if="scope.row.balanceDirection"
                  class="balanceType"
                  :class="{
                    creditBalance: scope.row.balanceDirection === '贷',
                    debitBalance: scope.row.balanceDirection === '借',
                  }"
                >
                  {{ scope.row.balanceDirection }}
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="t('detailsTable.table.balance')" show-overflow-tooltip prop="balanceAmount" />
          </el-table>
        </div>
      </div>
    </section>
    <voucherBillDrawer ref='voucherBillDrawerRef' v-model:visible="voucherBillDrawerVisible"></voucherBillDrawer>
  </div>
</template>

<script setup lang="ts">
// 明细表
import API from '@/modules/finance/api/accountStatementApi';
import voucherBillDrawer from '@/modules/finance/components/voucherBillDrawer.vue';
import { ref, reactive, onMounted, type Ref } from 'vue'
import type { ElTree } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import {useRouter} from "vue-router";
const router = useRouter();
const { t } = useI18n()
// defineOptions({
//   name: "DetailsTable",
//   inheritAttrs: false,
// })
const loadPeriodList = reactive({
  periodYearMonthEnd: '',
  periodYearMonthStart: '',
  currentPeriodYearMonth:''
});
const loadPeriod = async () => {
  loading.value = true;
  try {
    const response = await API.getPeriodScope({
      queryType: 0,
    });
    loadPeriodList.periodYearMonthStart = String(response.periodYearMonthStart);
    loadPeriodList.periodYearMonthEnd = String(response.periodYearMonthEnd);
    loadPeriodList.currentPeriodYearMonth = String(response.currentPeriodYearMonth);
    searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth];
    searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
    searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
    loadcashAccountList();
    getSubjectByFlowList()
  } catch (error) {
    if(error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    loading.value = false;
  }
};
const selectedCategoryId = ref<number | null>(null)
const selectedCategorySearch = ref<string | null>(null)
const loading = ref(false)
const methodList = ref([])
const levelList = ref([
  { id: 1, label: '1' },
  { id: 2, label: '2' },
  { id: 3, label: '3' },
  { id: 4, label: '4' }
])
const searchForm = reactive({
  period:[loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth],
  subjectCodeStart: undefined,
  subjectCodeEnd: undefined,
  subjectLevelStart: 1,
  subjectLevelEnd: 4,
  summary: undefined,
  periodYearMonthStart: loadPeriodList.currentPeriodYearMonth,
  periodYearMonthEnd: loadPeriodList.currentPeriodYearMonth,
  subjectId: undefined
})
const monthRangeChange = (val: [string, string]) => {
  if (val?.length) {
    searchForm.periodYearMonthStart = val[0];
    searchForm.periodYearMonthEnd = val[1];
  } else {
    searchForm.periodYearMonthStart = '';
    searchForm.periodYearMonthEnd = '';
  }
};
interface TreeNode {
  id?: number | string;
  subjectCode?: string;
  subjectName?: string;
  childList?: TreeNode[];
  [key: string]: any;
}

function filterTree(tree: TreeNode[], keyword: string): TreeNode[] {
  return tree
    .map((node: TreeNode) => {
      const childList = node.childList ? filterTree(node.childList, keyword) : []
      const displayText = `${node.subjectCode ?? ''}_${node.subjectName ?? ''}`
      const matched =
        node.subjectName?.includes(keyword) ||
        node.subjectCode?.includes(keyword) ||
        displayText.includes(keyword)
      if (matched || childList.length) {
        return {
          ...node,
          childList: childList.length ? childList : undefined
        }
      }
      return null
    })
    .filter(Boolean) as TreeNode[]
}
const selectedCategorySearchChange = async () => {
  const keyword = selectedCategorySearch.value?.trim()
  if (!keyword) {
    list.value = JSON.parse(JSON.stringify(originalList.value))
    return
  }
  list.value = filterTree(originalList.value, keyword)
}

const treeRef = ref<InstanceType<typeof ElTree> | null>(null)
const checkedKeys = ref('')
const list = ref<TreeNode[]>([]);
const originalList = ref<TreeNode[]>([])
const getSubjectByFlowList = async () => {
  try {
    let params = { ...searchForm }
    params.subjectId = undefined
    selectedCategorySearch.value = ''
    // 补齐 subjectCodeStart 到12位
    if (params.subjectCodeStart && params.subjectCodeStart.length < 11) {
      params.subjectCodeStart = params.subjectCodeStart.padEnd(11, '0')
    }
    // 补齐 subjectCodeEnd 到12位
    if (params.subjectCodeEnd && params.subjectCodeEnd.length < 11) {
      params.subjectCodeEnd = params.subjectCodeEnd.padEnd(11, '0')
    }
    const response = await API.getSubjectByFlow(params);
    const data = Array.isArray(response) ? response : (response?.data ?? []);
    list.value = data;
    originalList.value = JSON.parse(JSON.stringify(data)); // 保存原始数据
    searchForm.subjectId = data[0] ? data[0].id : undefined;
    checkedKeys.value = data[0] ? data[0].subjectName : undefined;
    if (treeRef.value) {
      treeRef.value.setCurrentKey(searchForm.subjectId)
    }
    handleTableQuery()
  } catch (error) {
    list.value = [];
    checkedKeys.value = '';
  }
};
const selectCategory = (category: any, node: any, component: any, event: Event) => {
  selectedCategoryId.value = category?.id ?? null;
  searchForm.subjectId = category?.id ?? undefined;
  checkedKeys.value = category?.subjectName ?? '';
  handleTableQuery();
}
const handleTableQuery = () => {
  loading.value = true
  let params = { ...searchForm }
  // 补齐 subjectCodeStart 到12位
  if (params.subjectCodeStart && params.subjectCodeStart.length < 11) {
    params.subjectCodeStart = params.subjectCodeStart.padEnd(11, '0')
  }
  // 补齐 subjectCodeEnd 到12位
  if (params.subjectCodeEnd && params.subjectCodeEnd.length < 11) {
    params.subjectCodeEnd = params.subjectCodeEnd.padEnd(11, '0')
  }
  API.getDetailFlow(params).then((res: any) => {
    methodList.value = Array.isArray(res) ? res : (res?.data ?? []);
    loading.value = false
  }).catch(() => {
    methodList.value = [];
    loading.value = false
  });
}
// const showContentList = ref([
//   {
//     statusId: 1,
//     statusName: t("detailsTable.label.showauxiliaryAccounting"),
//   },
//   {
//     statusId: 2,
//     statusName: t("detailsTable.label.showOppositeAccount"),
//   },
//   {
//     statusId: 3,
//     statusName: t("detailsTable.label.showDetailAccount"),
//   },
//   {
//     statusId: 4,
//     statusName: t("detailsTable.label.hideZeroBalance"),
//   },
//   {
//     statusId: 5,
//     statusName: t("detailsTable.label.hideNoTransactionZeroBalance"),
//   }, {
//     statusId: 6,
//     statusName: t("detailsTable.label.hideSummaryIfNoTransaction"),
//   },
// ]);
interface CashAccountItem {
  id: number | string;
  subjectCode: string;
  subjectName: string;
  statusId?: number | string;
  statusName?: string;
}
const cashAccountList = ref<CashAccountItem[]>([]);
const loadcashAccountList = async () => {
  try {
    const response = await API.subjectGetList({
      status: 1,
    });
    cashAccountList.value = response || [];
  } catch (error) {
    cashAccountList.value = [];
  }
};
const onResetHandlerEvent = () => {
  searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth]
  searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
  searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
  searchForm.subjectLevelStart = 1;
  searchForm.subjectLevelEnd = 4;
  searchForm.summary = undefined;
  searchForm.subjectCodeStart = undefined;
  searchForm.subjectCodeEnd = undefined;
  searchForm.subjectId = undefined;
  getSubjectByFlowList()
};
const voucherBillDrawerVisible = ref(false);
const voucherBillDrawerRef = ref(null);
const openVoucherBillDrawer = (voucherId:string) => {
  if(!voucherId)return
  voucherBillDrawerVisible.value = true;
   if (voucherBillDrawerRef.value) {
    voucherBillDrawerRef.value.openDrawer('detail', voucherId);
  }
};
onMounted(() => {
  loadPeriod()
})
</script>

<style lang="scss" scoped>
.detailsTable {
  height: calc(100vh - 110px);
  min-height: 600px;
  // background: #f7f8fa;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.search-container {
  padding: 16px 24px 0 24px;
  background: #fff;
}

.delivery-method-container {
  flex: 1 1 0;
  display: flex;
  flex-direction: row;
  min-height: 0;
  overflow: hidden;
  box-sizing: border-box;

  .left-panel {
    width: 320px;
    min-width: 320px;
    max-width: 320px;
    background: #fff;
    border-radius: 4px;
    padding: 26px 20px 20px 20px;
    margin-right: 12px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: 100%;

    .category-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 22px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      margin-bottom: 19px;
      font-size: 16px;
      color: #151719;
      border-bottom: 1px solid #f1f3f9;
    }

    .category-list {
      flex: 1 1 0;
      display: flex;
      flex-direction: column;
      min-height: 0;

      .el-input {
        margin-bottom: 18px;
      }

      .el-tree {
        flex: 1 1 0;
        min-height: 0;
        overflow: auto;
      }
    }
  }

  .right-panel {
    flex: 1 1 0;
    background-color: #fff;
    border-radius: 4px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    min-width: 0;
    height: 100%;

    .accountingSubject {
      height: 90px;
      display: flex;
      align-items: center;

      > div {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #762adb;
        padding: 0 17px;
        width: fit-content;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(118, 42, 219, 0.08);
        border-radius: 2px;
        border: 1px solid rgba(118, 42, 219, 0.15);
      }
    }

    .table-container {
      flex: 1 1 0;
      min-height: 0;
      display: flex;
      flex-direction: column;

      .el-table {
        flex: 1 1 0;
        min-height: 0;
      }
    }
  }
}

// 选中节点高亮色
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background: rgba(118, 42, 219, 0.06);
  border-radius: 4px;
  color: #762adb;
}

// 鼠标悬停节点
:deep(.el-tree-node__content:hover) {
  background: #f5f7fa;
}

// 节点字体加粗
:deep(.el-tree-node__label) {
  font-weight: 500;
}

// 缩小节点间距
:deep(.el-tree-node__content) {
  height: 50px;
}

.balanceType {
  width: 56px;
  height: 32px;
    background: rgba(64,158,255,0.08);
  border-radius: 2px;
  border: 1px solid rgba(64,158,255,0.2);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #409EFF;
  display: flex;
  justify-content: center;
  align-items: center;
}

.balanceType.debitBalance {
  background: rgba(41, 182, 16, 0.08);
  border: 1px solid rgba(41, 182, 16, 0.2);
  color: #29b610;
}

.balanceType.creditBalance {
  background: rgba(255, 156, 0, 0.08);
  border: 1px solid rgba(255, 156, 0, 0.2);
  color: #ff9c00;
}
</style>
