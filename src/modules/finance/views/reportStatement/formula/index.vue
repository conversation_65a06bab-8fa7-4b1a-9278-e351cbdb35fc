<template>
  <el-dialog v-model="dialogVisible" title="公式编辑" :close-on-click-modal="false">
    <div class="formula-table-wrap">
      <el-button type="primary" @click="addRow" style="margin: 0px 0 20px 0">添加行</el-button>
      <el-table :data="localformulaVoList" border style="width: 100%">
        <el-table-column type="index" :label="t('formula.label.serialNumber')" width="80" align="center" />
        <!-- <el-table-column prop="type" :label="t('formula.label.dataRetrievalMethod')">
          <template #default="scope">
            <el-select v-model="scope.row.type" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
</el-table-column> -->
        <!-- 项目名称 -->
        <el-table-column prop="dataProjectName" :label="t('formula.label.projectName')" min-width="200">
          <template #default="scope">
            <el-select filterable v-model="scope.row.dataProjectName" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in accountingList" :key="item.id" :label="item.subjectFullCode + '-' + item.subjectFullName" :value="item.subjectFullCode + '-' + item.subjectFullName" />
            </el-select>
          </template>
        </el-table-column>
        <!-- 运算符号 -->
        <el-table-column prop="formulaOperator" :label="t('formula.label.operator')">
          <template #default="scope">
            <el-select v-model="scope.row.formulaOperator" :placeholder="t('formula.label.chooseTips')" style="width: 100%">
              <el-option v-for="item in operatorOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="formulaRules" :label="t('formula.label.dataRule')">
          <template #default="scope">
            <el-select v-model="scope.row.formulaRules" :placeholder="t('formula.label.chooseTips')" style="width: 100%">
              <el-option v-for="item in ruleOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="currentPeriodAmount" :label="t('formula.label.endingBalance')" align="center">
          <template #default="scope">
            {{ scope.row.currentPeriodAmount }}
          </template>
        </el-table-column>
        <el-table-column prop="beginingBalanceAmount" :label="t('formula.label.beginningBalance')" align="center">
          <template #default="scope">
            {{ scope.row.beginingBalanceAmount }}
          </template>
        </el-table-column>
        <el-table-column :label="t('formula.label.action')" align="center">
          <template #default="scope">
            <el-button text type="primary" @click="removeRow(scope.index)">{{ t('formula.button.delete') }}</el-button>
          </template>
        </el-table-column>
        <!-- <el-table-column label="" width="110">
          <template #default="scope">
            <span class="move-btn" @click="moveUp(scope.$index)" style="color:#409EFF;cursor:pointer;">∧ 上移</span>
            <span class="move-btn" @click="moveDown(scope.$index)" style="color:#409EFF;cursor:pointer;margin-left:8px;">∨ 下移</span>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <template #footer>
      <span>
        <el-button @click="dialogVisible = false">{{ t('formula.button.cancel') }}</el-button>
        <el-button type="primary" @click="save">{{ t('formula.button.save') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { generalLedger } from '@/modules/finance/api';
import API from '@/modules/finance/api/accountStatementApi';
const { t } = useI18n();
const props = defineProps({
  visible: Boolean,
  projectId: '',
  formulaVoList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:visible', 'save']);
const localformulaVoList = ref([]);
watch(
  () => props.formulaVoList,
  (val) => {
    localformulaVoList.value = JSON.parse(JSON.stringify(val || []));
    onSearchHandler();
  },
  { immediate: true }
);
const accountingList = ref([]);
function onSearchHandler() {
  const params = {
    showTree: true,
    subjectType: '',
    status: '',
  };
  generalLedger.getAccountingList(params).then((res) => {
    accountingList.value = res;
  });
}
// 用于 v-model 绑定
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
});
// 选项数据
const typeOptions = [
  { label: '科目', value: 'subject' },
  { label: '项目', value: 'item' },
];
const nameOptions = [
  { label: '1405 - 库存商品', value: '1405' },
  { label: '1406 - 原材料', value: '1406' },
];
const operatorOptions = [
  { label: '+', value: 1 },
  { label: '-', value: 2 },
];
const ruleOptions = [
  { label: '发生额', value: 1 },
  { label: '贷方发生额', value: 2 },
  { label: '余额', value: 3 },
  { label: '借方余额', value: 4 },
  { label: '贷方余额', value: 5 },
  { label: '末级借方余额', value: 6 },
  { label: '末级贷方余额', value: 7 },
  { label: '辅助核算借方余额', value: 8 },
  { label: '辅助核算贷方余额', value: 9 },
  { label: '期初余额', value: 10 },
  { label: '借方发生额', value: 11 },
];
function addRow() {
  localformulaVoList.value.push({
    dataProjectName: '',
    formulaOperator: 1,
    formulaRules: '',
    currentPeriodAmount: 0,
    beginingBalanceAmount: 0,
  });
}
function removeRow(index) {
  localformulaVoList.value.splice(index, 1);
}
// function moveUp(index) {
//   if (index === 0) return;
//   const temp = formulaVoList.value[index];
//   formulaVoList.value.splice(index, 1);
//   formulaVoList.value.splice(index - 1, 0, temp);
// }
// function moveDown(index) {
//   if (index === formulaVoList.value.length - 1) return;
//   const temp = formulaVoList.value[index];
//   formulaVoList.value.splice(index, 1);
//   formulaVoList.value.splice(index + 1, 0, temp);
// }
function save() {
  console.log(localformulaVoList.value, projectId.value,'最后的值');
  const params = localformulaVoList.value;
  localformulaVoList.value.map((item, index) => {
    item.id = projectId.value;
  });
  API.editFormula(params).then((res) => {
    console.log(res, '接口保存下发res');
    // emit('save', localformulaVoList.value);
    // dialogVisible.value = false;
  });
}
</script>

<style scoped lang="scss">
.formula-table-wrap {
  background: #fff;
  border-radius: 4px;
  padding-bottom: 16px;
}

.move-btn {
  font-size: 13px;
  user-select: none;
}
</style>
