<template>
  <div class="pageContainer" v-loading="pageLoading">
    <div class="topBtn">
      <el-button v-hasPerm="['finance:setAccounts:add']" type="primary" @click="accountsHandle('', 'add')">{{ $t('setAccounts.button.addAccounts') }}</el-button>
    </div>
    <div class="panelContent">
      <template v-if="panelList.length>0">
        <el-card class="panel" v-for="(item,index) in panelList" :key="index" :class="{checked: item.checkedFlag}">
          <div class="topTitle">
            <div>
              <span>{{item.name}}</span>
              <span>({{item.code}})</span>
            </div>
            <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              <div class="setting">{{ $t('setAccounts.setting.set') }}</div>
            </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <div class="menuListContent">
                    <el-button link v-hasPerm="['finance:setAccounts:edit']">
                      <el-dropdown-item @click="accountsHandle(item,'edit')">{{ $t('setAccounts.setting.edit') }}</el-dropdown-item>
                    </el-button>
                    <el-button link v-hasPerm="['finance:setAccounts:copy']">
                      <el-dropdown-item @click="accountsHandle(item,'copy')">{{ $t('setAccounts.setting.copy') }}</el-dropdown-item>
                    </el-button>
                    <el-button link v-hasPerm="['finance:setAccounts:remove']">
                      <el-dropdown-item @click="removeAccounts(item)">{{ $t('setAccounts.setting.remove') }}</el-dropdown-item>
                    </el-button>
                    <el-button link v-hasPerm="['finance:setAccounts:addUser']">
                      <el-dropdown-item @click="openModalDialog(item)">{{ $t('setAccounts.setting.add') }}</el-dropdown-item>
                    </el-button>
                  </div>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="panelCenter"  @click="changeAccounts(item)">
            <p class="name">{{item.enterpriseName}}</p>
            <p class="info">
              <span>{{$t('setAccounts.info.guideline')}}：</span>
              <span>{{item.accountingSystemName}}</span>
            </p>
            <p class="info">
              <span>{{$t('setAccounts.info.type')}}：</span>
              <span>{{item.vatTypeName}}</span>
            </p>
            <p class="info">
              <span>{{$t('setAccounts.info.time')}}：</span>
              <span>{{item.activationYear}}年{{item.activationMonth}}月</span>
            </p>
          </div>
          <span v-if="item.checkedFlag" class="checkedInfo">{{ $t('setAccounts.info.currentAccounts') }}</span>
        </el-card>
      </template>
      <div class="empty" v-else>
        <Empty />
      </div>
    </div>

    <el-dialog v-model="modalVisible" :align-center="true" :title="$t('setAccounts.dialogInfo.dialogTitle')" width="600px" style="padding-right: 0" @close="handleCloseModal">
      <div style="padding-right: 16px">
        <el-table ref="tableRef" v-loading="loading" :data="tableData" highlight-current-row stripe row-key="userId">
          <template #empty><Empty /></template>
          <el-table-column :selectable="selectable"  type="selection" width="55" />
          <el-table-column :label="$t('setAccounts.dialogInfo.index')" prop="index" width="80" />
          <el-table-column :label="$t('setAccounts.dialogInfo.name')" prop="nickName" show-overflow-tooltip min-width="120"/>
          <el-table-column :label="$t('setAccounts.dialogInfo.username')" prop="userName" show-overflow-tooltip min-width="120"/>
          <el-table-column :label="$t('setAccounts.dialogInfo.role')" prop="roleName" show-overflow-tooltip/>
        </el-table>
      </div>
      <!-- 弹窗底部操作按钮 -->
      <template #footer>
        <div style="padding: 0 16px">
          <el-button type="primary" @click="handleSubmit">{{ $t('setAccounts.dialogInfo.submit') }}</el-button>
          <el-button @click="handleCloseModal">{{ $t('setAccounts.dialogInfo.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
// defineOptions({
//   name: "SetAccounts",
//   inheritAttrs: false,
// });
import {generalLedger} from "@/modules/finance/api";

const { t } = useI18n();
import {useRouter} from "vue-router";
const pageLoading = ref(false);
const router = useRouter();
const panelList = ref([]);
const modalVisible:Boolean = ref(false);
const loading:Boolean = ref(false);
const tableData = ref([]);
const tableRef = ref('');
const currentBooks = ref('');
const selectable = (row) => !row.greyFlag;
// 添加/编辑/复制账套
function accountsHandle(item, type = '') {
  router.push({
    path: '/finance/generalLedger/accountsDetail',
    query: {
      type,
      id: item ? item.id : ''
    }
  })
}
// 切换账套
function changeAccounts(item) {
  let nowYear = new Date().getFullYear();
  let nowMonth = new Date().getMonth() + 1;
  let warningMessage = '';
  if(!(nowYear >= Number(item.activationYear) && nowMonth >= item.activationMonth)) {
    warningMessage = '该账套未到启用时间！';
  }
  ElMessageBox({
    title: '提示',
    message: h('p', null, [
      h('p', { style: 'color: #e6a23c' },  warningMessage),
      h('p', null,  `确认要切换到${item.name}账套吗？`),
    ]),
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then((action) => {
    const params = {
      id: item.id
    }
    generalLedger.setUserCurrentSetBooks(params).then(res => {
      ElMessage.success('账套切换成功！');
      sessionStorage.removeItem('voucherForm');
      getBooksPageData();
    });
  }).catch(() => {})
}

// 删除账套
function removeAccounts(item) {
  ElMessageBox.confirm(`${t('setAccounts.messageInfo.remove1')}"${item.name}"${t('setAccounts.messageInfo.remove2')}`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    lockScroll: false,
  }).then(() => {
    const params = {
      id: item.id
    }
    generalLedger.removeBooks(params).then(() => {
      ElMessage.success('删除成功！');
      getBooksPageData();
    })
  })
}

// 打开添加用户弹框
function openModalDialog(data) {
  modalVisible.value = true;
  currentBooks.value = data.id;
  getUserListData(data);
}

// 关闭弹窗
function handleCloseModal() {
  modalVisible.value = false;
}

// 添加用户
function handleSubmit() {
  const userList = [];
  tableRef.value.getSelectionRows().map(item => {
    if(!item.greyFlag) {
      userList.push(item.userId);
    }
  });
  const params = {
    userIdList: userList,
    setBooksId: currentBooks.value
  }
  generalLedger.setGrant(params).then(() => {
    ElMessage.success('添加用户成功！');
    modalVisible.value = false;
    getBooksPageData();
  })
}

// 获取账套列表
function getBooksPageData() {
  pageLoading.value = true;
  generalLedger.getBooksPageList().then(res => {
    pageLoading.value = false;
    panelList.value = res;
  }).catch(() => {
    pageLoading.value = false;
  });
}
// 获取用户列表
function getUserListData(data) {
  loading.value = true;
  const params = {
    setBooksId: data.id
  }
  generalLedger.getUserList(params).then(res => {
    loading.value = false;
    tableData.value = res;
    tableData.value.map((item,i) => {
      item.index = i + 1;
      if(item.grantFlag) {
        setTimeout(()=>{
          tableRef.value.toggleRowSelection(item, true);
        })
      }
    })
  })
}

onMounted(() => {
  getBooksPageData();
});

</script>
<style scoped lang="scss">
.menuListContent{
  display: flex;
  flex-wrap: wrap;
  width: 100px;
  justify-content: center;
  :deep(.el-button){
    width: 100%;
    margin: 0;
    >span{
      display: inline-block;
      width: 100%!important;
    }
  }
}
.pageContainer{
  background: #FFFFFF;
  padding: 40px 20px 40px 30px;
  height: 100%;
  width: 100%;
  .topBtn{
    margin-bottom: 20px;
  }
  .panelContent{
    display: flex;
    flex-wrap: wrap;
    .empty{
      text-align: center;
      width: 100%;
    }
  }
  .panel{
    border: 1px solid #D9DCEB;
    width: 385px;
    margin-right: 20px;
    margin-bottom: 20px;

    position: relative;
    :deep(.el-card__body) {
      padding: 0;
      border: 1px solid #D9DCEB;
      border-radius: 4px 4px 0px 0px;
    }
    &.checked:after{
      content: '';
      display: inline-block;
      position: absolute;
      bottom: 1px;
      border-top: 40px solid transparent;
      border-right: 40px solid #F7F1FF;
      border-left: 40px solid transparent;
      border-bottom: 40px solid #F7F1FF;
      right: 1px;
    }
    &.checked{
      .panelCenter .name{
        color: #762ADB;

      }
    }
    .topTitle{
      padding: 15px 20px;
      font-size: 14px;
      color: #52585F;
      display: flex;
      background: #F4F6FA;
      justify-content: space-between;
      border-bottom: 1px solid #D9DCEB;
      .setting{
        width: 30px;
        &:hover{
          color: #762ADB;
          cursor: pointer;
        }
      }
    }
    .panelCenter{
      padding: 20px 0 20px 20px;
      cursor: pointer;
      p{
        margin: 0;
        color: #90979E;
        font-size: 14px;
        margin-bottom: 10px;
        &.name{
          color: #151719;
          font-size: 16px;
          margin-bottom: 16px;
        }
      }
    }
    .checkedInfo{
      transform: rotate(-45deg);
      display: inline-block;
      color: #762ADB;
      font-size: 12px;
      position: absolute;
      bottom: 15px;
      right: 0;
      z-index: 10;
    }
  }
}
</style>
