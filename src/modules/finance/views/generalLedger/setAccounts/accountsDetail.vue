<template>
  <div class="app-container">
    <div class="accountsDetailContent">
      <div class="page-title">
        <div @click="handleClose()" class="cursor-pointer mr8px">
          <el-icon><Back /></el-icon>
        </div>
        <div>
          <span v-if="type == 'add' || type== 'copy'">{{ $t("setAccounts.button.addAccounts") }}</span>
          <span v-else>{{ $t("setAccounts.button.editAccounts") }}</span>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px" label-position="right">
          <div class="item_content">
            <div class="title">{{ $t("setAccounts.info.basicInfo") }}</div>
            <el-row :gutter="20" class="mb-20px">
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.accountsNumber')" prop="code">
                  <el-input v-model="form.code" :placeholder="$t('common.placeholder.inputTips')" maxlength="20"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.accountsName')" prop="name">
                  <el-input v-model="form.name" :placeholder="$t('common.placeholder.inputTips')" clearable maxlength="20"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.companyName')" prop="enterpriseName">
                  <el-input v-model="form.enterpriseName" :placeholder="$t('common.placeholder.inputTips')" clearable maxlength="20"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mb-20px">
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.startTime')" prop="activationTime">
                  <el-date-picker v-model="form.activationTime" type="month" value-format="YYYY-MM" :placeholder="$t('common.placeholder.selectTips')"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.accountSystem')" prop="accountingSystemCode">
                  <el-select filterable v-model="form.accountingSystemCode" :placeholder="t('balanceTable.label.pleaseSelect')" style="width: 100%">
                    <el-option v-for="item in accountingSystemCodeList" :key="item.itemCode" :label="item.itemName" :value="item.itemCode" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.type')" prop="vatTypeCode">
                  <el-radio-group v-model="form.vatTypeCode">
                    <el-radio v-for="(item,index) in vatTypeCodeList" :key="index" :value="item.itemCode" size="large">{{ item.itemName }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mb-20px">
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.industry')" prop="industryCode">
                  <el-select filterable v-model="form.industryCode" :placeholder="t('balanceTable.label.pleaseSelect')" style="width: 100%">
                    <el-option v-for="item in industryNameList" :key="item.itemCode" :label="item.itemName" :value="item.itemCode" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.address')" prop="address">
                  <el-input v-model="form.address" :placeholder="$t('common.placeholder.inputTips')" clearable maxlength="40"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.connectName')" prop="contact">
                  <el-input v-model="form.contact" :placeholder="$t('common.placeholder.inputTips')" clearable maxlength="20"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mb-20px">
              <el-col :span="8">
                <el-form-item :label="$t('setAccounts.formLabel.connectMobile')" prop="contactNumber">
                  <el-input v-model="form.contactNumber" @input="numberChange(form,'contactNumber')" :placeholder="$t('common.placeholder.inputTips')" maxlength="11"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mb-20px">
              <el-col :span="24">
                <el-form-item :label="$t('setAccounts.formLabel.memo')" prop="notes">
                  <el-input v-model="form.notes" maxlength="200" :rows="6" type="textarea" :placeholder="$t('common.placeholder.inputTips')"/>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
        <div class="page-footer">
          <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
          <el-button @click="submitForm" type="primary">{{ $t("common.confirm") }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {generalLedger} from "@/modules/finance/api";
import {useCommon} from "@/modules/finance/composables/common";
const { numberChange } = useCommon();

defineOptions({
  name: "AccountsDetail",
  inheritAttrs: false,
});
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();
const formRef = ref<FormInstance>();
const { t } = useI18n();
const currentId = route.query.id;
const type = route.query.type;

const accountingSystemCodeList = ref([]);
const vatTypeCodeList = ref([]);
const industryNameList = ref([]);
// 角色表单
let form = reactive({
  code: '',
  name: '',
  enterpriseName: '',
  activationTime: '',
  accountingSystemCode: '',
  vatTypeCode: '1',
  industryCode: '',
  address: '',
  contact: '',
  contactNumber: '',
  notes: '',
});

const rules = reactive({
  code: [{required: true, message: t("setAccounts.rules.accountsNumber"), trigger: "blur"}],
  name: [{required: true, message: t("setAccounts.rules.accountsName"), trigger: "blur"}],
  enterpriseName: [{required: true, message: t("setAccounts.rules.companyName"), trigger: "blur"}],
  activationTime: [{required: true, message: t("setAccounts.rules.startTime"), trigger: "change"}],
  accountingSystemCode: [{required: true, message: t("setAccounts.rules.accountSystem"), trigger: "blur"}],
  vatTypeCode: [{required: true, message: t("setAccounts.rules.type"), trigger: "change"}],
})

async function handleClose() {
  router.push('/finance/generalLedger/setAccounts')
}

async function submitForm() {
  if (!formRef.value) return
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      const params = {
        ...form,
        activationYear: form.activationTime.split('-')[0],
        activationMonth: form.activationTime.split('-')[1],
      }
      if(type === 'copy') {
        params.copySetBooksId = currentId;
      }
      delete params.activationTime;
      if(type === 'add' || type === 'copy') {
        generalLedger.addBooks(params).then(() => {
          ElMessage.success('新增成功!');
          router.push('/finance/generalLedger/setAccounts');
        });
      } else {
        params.id = currentId;
        generalLedger.updateBooks(params).then(() => {
          ElMessage.success('编辑成功!');
          router.push('/finance/generalLedger/setAccounts');
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 获取下拉列表数据
function getSelectItemByTypeCodeData() {
  let arr = ['accountingSystem','vatType','industry']
  generalLedger.getSelectItemByTypeCode(JSON.stringify(arr)).then(res => {
    accountingSystemCodeList.value = res.accountingSystem;
    vatTypeCodeList.value = res.vatType;
    industryNameList.value = res.industry;
  });
}

// 获取详情数据
function booksDetailData() {
  const params = {
    id: currentId
  }
  generalLedger.booksDetail(params).then(res => {
    Object.keys(form).map(key => {
      form[key] = res[key];
    });
    form.activationTime = `${res.activationYear}-${res.activationMonth > 10 ? res.activationMonth : '0' + res.activationMonth}`;
  });
}

onMounted(async () => {
  getSelectItemByTypeCodeData();
  if(type === 'edit' || type === 'copy') {
    booksDetailData();
  }
});
</script>
<style scoped lang="scss">
.accountsDetailContent {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 13px 21px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 20px 9px 20px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .mb-20px {
    margin-bottom: 20px;
  }
  :deep(.el-date-editor--month){
    width: 100%;
  }
}
</style>
