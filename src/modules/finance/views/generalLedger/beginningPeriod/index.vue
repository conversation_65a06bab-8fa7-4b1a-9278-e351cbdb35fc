<template>
  <div class="pageContainer">
    <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal">
      <el-menu-item v-for="(item,index) in menuList" :key="index" @click="handleSelect(item.value)" :index="item.value">{{ item.label }}</el-menu-item>
    </el-menu>
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true">
        <el-form-item>
          <el-button type="primary" @click="balanceTest">{{ $t('beginningPeriod.tableBtn.balance') }}</el-button>
<!--          <el-button type="default" @click="onSearchHandler">{{ $t('beginningPeriod.tableBtn.import') }}</el-button>-->
<!--          <el-button type="default" @click="onSearchHandler">{{ $t('beginningPeriod.tableBtn.export') }}</el-button>-->
        </el-form-item>
        <el-form-item prop="brandName">
          <el-input v-model="searchForm.keyword" :prefix-icon="Search" :placeholder="$t('accounting.searchPlaceholder.label')" clearable @keyup.enter="onSearchHandler"/>
        </el-form-item>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <el-table ref="tableRef" default-expand-all v-loading="loading" :data="tableData" :tree-props="{ children: 'childList'}" highlight-current-row stripe row-key="id">
        <template #empty><Empty /></template>
        <el-table-column :label="$t('beginningPeriod.tableLabel.index')" prop="index" width="80" />
        <el-table-column :label="$t('beginningPeriod.tableLabel.code')" prop="subjectCode" show-overflow-tooltip min-width="120"/>
        <el-table-column :label="$t('beginningPeriod.tableLabel.name')" prop="subjectName" show-overflow-tooltip min-width="120"/>
        <el-table-column :label="$t('beginningPeriod.tableLabel.type')" prop="subjectTypeName" show-overflow-tooltip/>
        <el-table-column :label="$t('beginningPeriod.tableLabel.balance')" prop="subjectBalanceDirection" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.subjectBalanceDirection === '借' ? 'borrow' : 'loan'">{{scope.row.subjectBalanceDirection}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('beginningPeriod.tableLabel.openingBalance')" prop="beginBalance">
          <template #default="scope">
            <el-input :disabled="checkoutFlag" v-if="scope.row.leaf === 1 && currentIndex !== '5'" @input="moneyChange(scope.row,'beginBalance', true)" @blur="inputChange(scope.row,'beginBalance',true)" type="text" v-model="scope.row.beginBalance"></el-input>
            <span v-else>{{scope.row.beginBalance === '0.00' ? '' : scope.row.beginBalance}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('beginningPeriod.tableLabel.accumulatedDebit')" prop="accumulatedDebit">
          <template #default="scope">
            <el-input :disabled="checkoutFlag" v-if="scope.row.leaf === 1" @input="moneyChange(scope.row,'accumulatedDebit', true)" @blur="inputChange(scope.row,'accumulatedDebit',true)" type="text" v-model="scope.row.accumulatedDebit"></el-input>
            <span v-else>{{scope.row.accumulatedDebit === '0.00' ? '' : scope.row.accumulatedDebit}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('beginningPeriod.tableLabel.accumulatedCredit')" prop="accumulatedCredit">
          <template #default="scope">
            <el-input :disabled="checkoutFlag" v-if="scope.row.leaf === 1" @input="moneyChange(scope.row,'accumulatedCredit', true)" @blur="inputChange(scope.row,'accumulatedCredit',true)" type="text" v-model="scope.row.accumulatedCredit"></el-input>
            <span v-else>{{scope.row.accumulatedCredit === '0.00' ? '' : scope.row.accumulatedCredit}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('beginningPeriod.tableLabel.beginningBalance')" prop="yearBeginBalance">
          <template #default="scope">
            <span>{{scope.row.yearBeginBalance === '0.00' ? '' : scope.row.yearBeginBalance}}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog v-model="isVisible" title="试算平衡检查" :close-on-click-modal="false" width="800px" class="dialog-wrapper" @close="closeVisibleDialog" header-class="dialog-header">
      <div class="dialog-content">
        <div class="title">
          <el-icon v-if="checkoutDialogFlag" style="color: #FF1500"><CircleCloseFilled /></el-icon>
          <el-icon v-else style="color: #13ce66"><CircleCheckFilled /></el-icon>
          <span class="info">{{!checkoutDialogFlag ? '您录入的初始余额平衡！' : '您录入的初始余额不平衡！'}}</span>
        </div>
        <el-table ref="tableRef" default-expand-all v-loading="dialogLoading" :data="dialogTableData.slice(0,2)"  highlight-current-row stripe>
          <template #empty><Empty /></template>
          <el-table-column label="项目" prop="name" />
          <el-table-column label="借方金额" prop="leftBalance" show-overflow-tooltip/>
          <el-table-column label="贷方金额" prop="rightBalance" show-overflow-tooltip/>
          <el-table-column label="差额" prop="differenceBalance" show-overflow-toolti/>
        </el-table>

        <div style="margin-top: 20px">
          <el-table ref="tableRef" default-expand-all v-loading="dialogLoading" :data="dialogTableData.slice(2)"  highlight-current-row stripe>
            <template #empty><Empty /></template>
            <el-table-column label="项目" prop="name" />
            <el-table-column label="资产" prop="leftBalance" show-overflow-tooltip/>
            <el-table-column label="负债和所有者权益" prop="rightBalance" show-overflow-tooltip/>
            <el-table-column label="差额" prop="differenceBalance" show-overflow-toolti/>
          </el-table>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeVisibleDialog">{{ $t("common.close") }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import {generalLedger} from "@/modules/finance/api";
import {useCommon} from '@/modules/finance/composables/common';
import {useRouter} from "vue-router";

const router = useRouter();
const moneyChange = useCommon().moneyChange;

const { t } = useI18n();
const loading = ref(false);
const activeIndex = ref('1');
const currentIndex = ref('1');
const tableData = ref([]);
const menuList = [
  {value: '1', label: t('beginningPeriod.tabLabel.property')},
  {value: '2', label: t('beginningPeriod.tabLabel.liabilities')},
  {value: '3', label: t('beginningPeriod.tabLabel.equity')},
  {value: '4', label: t('beginningPeriod.tabLabel.cost')},
  {value: '5', label: t('beginningPeriod.tabLabel.profitLoss')},
]
const searchForm = reactive({
  showTree: true,
  pageFlag: false,
  status: '',
  keyword: ''
});
const checkoutFlag = ref(false);
const checkoutDialogFlag = ref(false);
const isVisible = ref(false);
const dialogLoading = ref(false);
const dialogTableData = ref([]);

function closeDialog() {
  isVisible.value = false;
}

function balanceTest() {
  isVisible.value = true;
  checkoutDialogFlag.value = false;
  dialogLoading.value = true;
  generalLedger.trialBalance().then(res => {
    dialogLoading.value = false;
    dialogTableData.value = res;
    dialogTableData.value.map(item => {
      item.leftBalance = Number(item.leftBalance).toFixed(2);
      item.rightBalance = Number(item.rightBalance).toFixed(2);
      item.differenceBalance = Number(item.differenceBalance).toFixed(2);
      if(item.differenceBalance !== '0.00') {
        checkoutDialogFlag.value = true;
      }
    })
  }).catch(()=>{
    dialogLoading.value = false;
  });
}

// 切换类别
function handleSelect(data) {
  currentIndex.value = data;
  onSearchHandler();
}
// 获取列表数据
function onSearchHandler() {
  const params = {
    showTree: true,
    subjectType: currentIndex.value,
    status: '',
    ...searchForm
  }
  loading.value = true;
  generalLedger.getAccountingList(params).then(res => {
    loading.value = false;
    tableData.value = res;
    tableData.value.map((item,index) => {
      item.index = index + 1;
      setNumber(item);
    });
  }).catch((err) => {
    loading.value = false;
    if(err.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
  })
}

// 处理四位小数点为2位
function setNumber(data) {
  if(data.childList) {
    data.beginBalance = Number(data.beginBalance).toFixed(2);
    data.accumulatedDebit = Number(data.accumulatedDebit).toFixed(2);
    data.accumulatedCredit = Number(data.accumulatedCredit).toFixed(2);
    data.yearBeginBalance = Number(data.yearBeginBalance).toFixed(2);
    setNumber(data.childList);
  } else {
    if(data.length>0) {
      data.map(item => {
        setNumber(item);
      });
    } else {
      data.beginBalance = Number(data.beginBalance).toFixed(2);
      data.accumulatedDebit = Number(data.accumulatedDebit).toFixed(2);
      data.accumulatedCredit = Number(data.accumulatedCredit).toFixed(2);
      data.yearBeginBalance = Number(data.yearBeginBalance).toFixed(2);
    }
  }
}

// 计算年初余额
// 借方科目：年初数 = 期初数 + 本年累计贷方 - 本年累计借方
// 贷方科目：年初数 = 期初数 + 本年累计借方 - 本年累计贷方
// 资产  成本   借 +    贷 -
//负债  所有者权益  损益  借 - 贷 +
function inputChange(data,key,saveFlag) {
  if(data[key] === '') {
    data[key] = '0.00';
  }
  // 计算当前行数据
  if(data.subjectBalanceDirection === '借') {
    data.yearBeginBalance = (Number(data.beginBalance) + Number(data.accumulatedCredit) - Number(data.accumulatedDebit)).toFixed(2);
  }
  if(data.subjectBalanceDirection === '贷') {
    data.yearBeginBalance = (Number(data.beginBalance) + Number(data.accumulatedDebit) - Number(data.accumulatedCredit)).toFixed(2);
  }

  if(key) {
    calcParentData(data, key)
  }
  if(saveFlag) {
    const params = {
      id: data.id,
      beginBalance: data.beginBalance,
      accumulatedDebit: data.accumulatedDebit,
      accumulatedCredit: data.accumulatedCredit,
      yearBeginBalance: data.yearBeginBalance
    }
    generalLedger.initialBalance(params).then(() => {
      // ElMessage.success('设置成功！');
      // onSearchHandler();
    });
  }
}

function calcParentData(data, key) {
  if(data.level === 1) return;
  let calcData = 0;
  let parentList = data.subjectFullCode.split('_');
  let firstLevel,secondLevel,thirdLevel = '';
  let firstLevelData,secondLevelData,thirdLevelData = {};
  firstLevel = parentList[0];
  firstLevelData = tableData.value.filter(item => item.subjectCode === firstLevel)[0];

  if(data.level === 3) {
    secondLevel = parentList[1];
    secondLevelData = firstLevelData.childList.filter(item => item.subjectCode === secondLevel)[0];
  }

  if(data.level === 4) {
    secondLevel = parentList[1];
    secondLevelData = firstLevelData.childList.filter(item => item.subjectCode === secondLevel)[0];
    thirdLevel = parentList[2];
    thirdLevelData =  secondLevelData.childList.filter(item => item.subjectCode === thirdLevel)[0];
  }


  // 计算三级数据
  if(thirdLevelData && thirdLevelData.childList) {
    thirdLevelData.childList.map(item => {
      calcData = calcNumber(item, thirdLevelData, key, calcData);
    });
    thirdLevelData[key] = calcData.toFixed(2);
    inputChange(thirdLevelData);
  }
  if(secondLevelData && secondLevelData.childList) {
    // 计算二级数据
    calcData = 0;
    secondLevelData.childList.map(item => {
      calcData = calcNumber(item, secondLevelData, key, calcData);
    });
    secondLevelData[key] = calcData.toFixed(2);
    inputChange(secondLevelData);
  }

  // 计算一级数据
  calcData = 0;
  firstLevelData.childList.map(item => {
    calcData = calcNumber(item, firstLevelData, key, calcData);
  });
  firstLevelData[key] = calcData.toFixed(2);
  inputChange(firstLevelData);
}

function calcNumber(data,parentData,key,account) {
  if(key === 'beginBalance') {
    if(data.subjectBalanceDirection === parentData.subjectBalanceDirection) {
      account += Number(data[key]);
    } else {
      account -= Number(data[key]);
    }
  } else {
    account += Number(data[key]);
  }

  return account;
}

// 检查是否结算
function getCheckoutFlagData() {
  generalLedger.getCheckoutFlag().then(res => {
    checkoutFlag.value = res;
  })
}

function closeVisibleDialog() {
  isVisible.value = false;
  dialogTableData.value = [];
  console.log(111)
}
onMounted(() => {
  onSearchHandler();
  getCheckoutFlagData();
})
</script>
<style scoped lang="scss">
.pageContainer{
  background: #FFFFFF;
  padding: 20px 20px 40px 20px;
  height: 100%;
  width: 100%;
  .el-menu--horizontal{
    height: 40px;
    .el-menu-item{
      height: 39px!important;
      background: #fff!important;
      padding: 10px 46px!important;
      &.is-active:before{
        display: none;
      }
    }
  }
  .search-container{
    padding: 20px 0 0;
    margin-bottom: 0;
    .el-form{
      width: 100%;
      display: flex;
      justify-content: space-between;
      .el-form-item{
        margin-right: 0;
      }
      .el-button--default{
        --el-border-color: #762ADB!important;
        --el-text-color-regular: #762ADB!important;
      }
    }
  }
  :deep(.el-card__body){
    padding: 0!important;
  }
  .dialog-content{
    .title{
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      margin-bottom: 31px;
      margin-top: 37px;
      span.info{
        color: #52585F;
        font-size: 16px;
        margin-left: 10px;
      }
    }
  }
  .borrow{
    background: rgba(41,182,16,0.08);
    border-radius: 2px;
    border: 1px solid rgba(41,182,16,0.2);
    padding: 3px 21px;
    display: inline-block;
    color: #29B610;
  }
  .loan{
    background: rgba(255,156,0,0.08);
    border-radius: 2px;
    border: 1px solid rgba(255,156,0,0.2);
    padding: 3px 21px;
    display: inline-block;
    color: #FF9C00;
  }
}
</style>
