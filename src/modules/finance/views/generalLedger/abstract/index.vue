<template>
  <div class="pageContainer">
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true">
        <el-form-item>
          <el-button v-hasPerm="['finance:abstract:add']" type="primary" @click="onAddHandler();dialogMode = 'add';">{{ $t('abstract.tableBtn.add') }}</el-button>
        </el-form-item>
        <el-form-item prop="summaryContent">
          <el-input v-model="searchForm.summaryContent" :prefix-icon="Search" :placeholder="$t('abstract.searchPlaceholder.label')" clearable @keyup.enter="onSearchHandler"/>
        </el-form-item>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <el-table ref="tableRef" v-loading="loading" :data="tableData" highlight-current-row stripe row-key="id">
        <template #empty><Empty /></template>
        <el-table-column :label="$t('abstract.tableLabel.index')" prop="index" width="80" />
        <el-table-column :label="$t('abstract.tableLabel.abstractContent')" prop="summaryContent" show-overflow-tooltip min-width="120"/>
        <el-table-column :label="$t('abstract.tableLabel.operate')" fixed="right">
          <template #default="scope">
            <el-button v-hasPerm="['finance:abstract:edit']" type="primary" size="small" link @click="onEditHandler(scope.row)">{{ $t('abstract.tableBtn.edit') }}</el-button>
            <el-button v-hasPerm="['finance:abstract:remove']" type="danger" size="small" link @click="removeHandle(scope.row)">{{ $t('abstract.tableBtn.remove') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-if="total > 0" v-model:total="total" v-model:page="paginationInfo.pageNo" v-model:limit="paginationInfo.pageSize" @pagination="onPaginationChangeHandler"/>
    </el-card>
    <el-drawer v-model="showDialog" :title=" dialogMode === 'add'? $t('abstract.tableBtn.addTitle') : $t('abstract.tableBtn.editTitle')" :close-on-click-modal="false" @close="onCloseHandler">
      <el-form :model="contentForm" :rules="rules" ref="contentFormRef" label-position="top">
        <el-form-item :label="$t('abstract.formLabel.content')" prop="summaryContent">
          <el-input type="textarea" :rows="6" v-model="contentForm.summaryContent" :maxlength="200"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseHandler()">{{ $t("common.cancel") }}</el-button>
          <el-button type="primary" :loading="dialogLoading" @click="onSaveHandler">{{ $t("common.confirm") }}</el-button>
        </span>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import tableMixin from "@/modules/finance/mixins/table";
import formMixin from "@/modules/finance/mixins/form";
import { Search } from '@element-plus/icons-vue'
import {generalLedger} from "@/modules/finance/api";
const { t } = useI18n();



const searchForm = reactive({
  summaryContent: ''
});
const dialogMode = ref(false);
const contentForm = reactive({
  summaryContent: '',
  id: ''
});
const rules = reactive({
  summaryContent: [{required: true, message: t("abstract.rules.summaryContent"), trigger: "blur"}],
})
function saveCallbackHandler () {
  onSearchHandler();
}
const {
  loading,
  tableData,
  total,
  paginationInfo,
  headFormRef,
  router,
  path,
  onSearchHandler,
  onResetHandler,
  onPaginationChangeHandler,
  onDeleteHandler,
  onStatusChangeHandler,
} = tableMixin({
  searchForm,
  tableGetApi: generalLedger.queryVoucherSummaryPageList,
  tableCallback: tableCallbackFun,
  tableDeleteApi: generalLedger.deleteVoucherSummary,
});
const {
  showDialog,
  dialogLoading,
  contentFormRef,
  onAddHandler,
  onEditHandler,
  onSaveHandler,
  onCloseHandler,
} = formMixin({
  contentForm,
  idName: "id",
  formAddApi: generalLedger.saveVoucherSummary,
  formEditApi: generalLedger.updateVoucherSummary,
  saveCallback: saveCallbackHandler,
  formatParamsCallback: (params: any) => {
    // params.imagesUrl = JSON.stringify(params.imagesUrl);
  },
});
// 处理列表数据
function tableCallbackFun() {
  tableData.value.map((item,i) => {
    item.index = i + ((paginationInfo.pageNo - 1) * paginationInfo.pageSize) + 1;
  })
}
// 删除
function removeHandle(row: any) {
  contentForm.parentId = row.parentId;
  onDeleteHandler(t("abstract.messageInfo.remove"), "id", row.id);
}
onMounted(() => {
  onSearchHandler();
})
</script>
<style scoped lang="scss">
.pageContainer{
  background: #FFFFFF;
  padding: 20px 20px 40px 20px;
  height: 100%;
  width: 100%;
  .el-menu--horizontal{
    height: 40px;
    .el-menu-item{
      height: 39px!important;
      background: #fff!important;
      padding: 10px 46px!important;
      &.is-active:before{
        display: none;
      }
    }
  }
  .search-container{
    padding: 0;
    margin-bottom: 0;
    .el-form{
      width: 100%;
      display: flex;
      justify-content: space-between;
      .el-form-item{
        margin-right: 0;
      }
      .el-button--default{
        --el-border-color: #762ADB!important;
        --el-text-color-regular: #762ADB!important;
      }
    }
  }
  :deep(.el-card__body){
    padding: 0!important;
  }
}
</style>
