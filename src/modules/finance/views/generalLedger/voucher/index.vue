<template>
  <div class="pageContainer">
    <div class="search-container">
      <el-form ref="headFormRef" :inline="true">
        <el-form-item>
          <el-button v-hasPerm="['finance:voucher:add']" type="primary" @click="onAddHandler();dialogMode = 'add'">{{ $t('voucher.tableBtn.add') }}</el-button>
          <el-button v-hasPerm="['finance:voucher:remove']" :disabled="!checkFlag" type="danger" @click="batchRemoveVoucherHandler">{{ $t('voucher.tableBtn.remove') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <el-table ref="tableRef" v-loading="loading" :data="tableData" highlight-current-row @select="selectItem" @select-all="selectItem">
        <template #empty><Empty /></template>
        <el-table-column :selectable="selectable" type="selection" width="55" />
        <el-table-column :label="$t('voucher.tableLabel.index')" prop="index" width="80" />
        <el-table-column :label="$t('voucher.tableLabel.voucher')" prop="voucherWord" show-overflow-tooltip min-width="120">
          <template #default="scope">
            <div class="voucherContent">
              <span>{{scope.row.voucherWord}}</span>
              <span v-if="scope.row.isDefault === 1" class="isDefault">默认</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('voucher.tableLabel.title')" prop="displayTitle" show-overflow-tooltip min-width="120"/>
        <el-table-column :label="$t('voucher.tableLabel.isDefault')" prop="isDefault" show-overflow-tooltip>
          <template #default="scope">
            <span>{{scope.row.isDefault === 1 ? '是' : '否'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('voucher.tableLabel.operate')" fixed="right">
          <template #default="scope">
            <el-button v-hasPerm="['finance:voucher:edit']" v-if="scope.row.isDeleted != 0 && scope.row.isExistVoucher != 1" type="primary" size="small" link @click="onEditHandler(scope.row);dialogMode = 'edit'">{{ $t('voucher.tableBtn.edit') }}</el-button>
            <el-button v-hasPerm="['finance:voucher:remove']" v-if="scope.row.isDeleted != 0 && scope.row.isExistVoucher != 1" type="danger" size="small" link @click="removeHandle(scope.row)">{{ $t('voucher.tableBtn.remove') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-if="total > 20" v-model:total="total" v-model:page="paginationInfo.pageNo" v-model:limit="paginationInfo.pageSize" @pagination="onPaginationChangeHandler"/>
    </el-card>
    <el-drawer v-model="showDialog" :title=" dialogMode === 'add'? $t('voucher.dialogTitle.add') : $t('voucher.dialogTitle.edit')" :close-on-click-modal="false" @close="onCloseHandler">
      <el-form :model="contentForm" :rules="contentFormRules" ref="contentFormRef" label-position="top">
        <el-form-item :label="$t('voucher.formLabel.voucherWord')" prop="voucherWord">
          <el-input :placeholder="$t('common.placeholder.inputTips')" type="text" v-model="contentForm.voucherWord" :maxlength="20"/>
        </el-form-item>
        <el-form-item :label="$t('voucher.formLabel.displayTitle')" prop="displayTitle">
          <el-input :placeholder="$t('common.placeholder.inputTips')" v-model="contentForm.displayTitle"  :maxlength="20"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseHandler()">{{ $t("common.cancel") }}</el-button>
          <el-button type="primary" :loading="dialogLoading" @click="onSaveHandler">{{ $t("common.confirm") }}</el-button>
        </span>
      </template>
    </el-drawer>

  </div>
</template>
<script setup lang="ts">
import tableMixin from "@/modules/finance/mixins/table";
import formMixin from "@/modules/finance/mixins/form";
import {generalLedger} from "@/modules/finance/api";

const { t } = useI18n();
const tableRef = ref('');
const selectable = (row: User) => row.isDeleted !== 0 && row.isExistVoucher != 1;
const searchForm = reactive({
});
const dialogMode = ref(false);
const contentForm = reactive({
  voucherWord: '',
  displayTitle: '',
  id: ''
});
const checkFlag = ref(false);
const contentFormRules = reactive({
  voucherWord: [{required: true, message: t("voucher.rules.voucherWord"), trigger: "blur"}],
  displayTitle: [{required: true, message: t("voucher.rules.displayTitle"), trigger: "blur"}],
});
function saveCallbackHandler () {
  onSearchHandler()
}
function selectItem() {
  if(tableRef.value.getSelectionRows().length>0) {
    checkFlag.value = true;
  } else {
    checkFlag.value = false;
  }
}
const {
  loading,
  tableData,
  total,
  paginationInfo,
  headFormRef,
  router,
  path,
  onSearchHandler,
  onResetHandler,
  onPaginationChangeHandler,
  onDeleteHandler,
  onStatusChangeHandler,
} = tableMixin({
  searchForm,
  tableGetApi: generalLedger.queryVoucherTypeList,
  tableCallback: tableCallbackFun,
  tableDeleteApi: generalLedger.removeVoucher,
});
const {
  showDialog,
  dialogLoading,
  contentFormRef,
  onAddHandler,
  onEditHandler,
  onSaveHandler,
  onCloseHandler,
} = formMixin({
  contentForm,
  idName: "id",
  formAddApi: generalLedger.saveVoucher,
  formEditApi: generalLedger.updateVoucher,
  saveCallback: saveCallbackHandler,
  formatParamsCallback: (params: any) => {
    // params.imagesUrl = JSON.stringify(params.imagesUrl);
  },
});
// 处理列表数据
function tableCallbackFun() {
  tableData.value.map((item,index) => {
    item.index = index + 1;
  });
  tableRef.value.clearSelection();
  selectItem()
}
// 删除
function removeHandle(row: any) {
  onDeleteHandler(t("voucher.messageInfo.remove"), "id", row.id);
}
// 批量删除
function batchRemoveVoucherHandler() {
  const idList = [];
  tableRef.value.getSelectionRows().map(item => {
    idList.push(item.id);
  });
  ElMessageBox.confirm(t('voucher.messageInfo.batchRemove', t('purchasePayable.message.reconciled')), {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    type: 'warning'
  }).then(() => {
    generalLedger.batchRemoveVoucher(JSON.stringify(idList)).then(() => {
      ElMessage.success(t('voucher.messageInfo.batchRemoveSuccess'));
      onSearchHandler();
    });
  });
}

onMounted(() => {
  onSearchHandler();
})
// 编辑
</script>
<style scoped lang="scss">
.pageContainer{
  background: #FFFFFF;
  padding: 20px 20px 40px 20px;
  height: 100%;
  width: 100%;
  .el-menu--horizontal{
    height: 40px;
    .el-menu-item{
      height: 39px!important;
      background: #fff!important;
      padding: 10px 46px!important;
      &.is-active:before{
        display: none;
      }
    }
  }
  .search-container{
    padding: 0;
    margin-bottom: 0;
    .el-form{
      width: 100%;
      display: flex;
      justify-content: space-between;
      .el-form-item{
        margin-right: 0;
      }
      .el-button--default{
        --el-border-color: #762ADB!important;
        --el-text-color-regular: #762ADB!important;
      }
    }
  }
  :deep(.el-card__body){
    padding: 0!important;
  }
  .voucherContent{
    //position: relative;
    .isDefault{
      position: absolute;
      display: inline-block;
      padding: 0 7px;
      background: #FE8200;
      border-radius: 4px;
      font-size: 12px;
      color: #fff;
      bottom: 15px;
      left: 32px;
      //width: 36px;
      //height: 28px;
    }
  }
}
</style>
