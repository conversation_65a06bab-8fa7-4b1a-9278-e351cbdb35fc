<template>
  <div class="final-balance app-container">
    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="btn-wrap">
          <div class="left-btn">会计期间：{{ periodYearMonthZh }}</div>
          <div class="right-btn">
            <el-button type="primary" plain>打印</el-button>
            <el-button type="primary">导出</el-button>
          </div>
        </div>
      </template>
      <el-table v-loading="loading" :data="tableData" style="width: 100%" row-key="id" :tree-props="{ children: 'childList', hasChildren: 'hasChildren' }">
        <template #empty>
          <Empty />
        </template>
        <el-table-column label="序号" type="index" width="55" align="center"/>
        <el-table-column label="科目编码" prop="subjectCode" width="120" align="center"/>
        <el-table-column label="科目名称" prop="subjectFullName" min-width="120" align="center"/>
        <el-table-column label="期初余额" align="center">
          <el-table-column label="借方金额" min-width="120" align="center">
            <template #default="{ row }">{{ row.balanceSubjectVo.beginBalanceJie }}</template>
          </el-table-column>
          <el-table-column label="贷方金额" min-width="120" align="center">
            <template #default="{ row }">{{ row.balanceSubjectVo.beginBalanceDai }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="本月累计发生额" align="center">
          <el-table-column label="借方金额" min-width="120" align="center">
            <template #default="{ row }">{{ row.balanceSubjectVo.curPeriodJie }}</template>
          </el-table-column>
          <el-table-column label="贷方金额" min-width="120" align="center">
            <template #default="{ row }">{{ row.balanceSubjectVo.curPeriodDai }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="期末余额" align="center">
          <el-table-column label="借方金额" min-width="120" align="center">
            <template #default="{ row }">{{ row.balanceSubjectVo.endBalanceJie }}</template>
          </el-table-column>
          <el-table-column label="贷方金额" min-width="120" align="center">
            <template #default="{ row }">{{ row.balanceSubjectVo.endBalanceDai }}</template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog v-model="showDialog" title="试算平衡检查" width="800" class="finance-moudle">
      <div class="dialog-content">
        <div class="result-line">
          <el-icon color="#72BE60" size="24" v-if="trialBalance"><SuccessFilled /></el-icon>
          <el-icon color="#FF1500" size="24" v-else><WarningFilled /></el-icon>
          <span class="result-text">{{ trialBalance ? '试算平衡!' : '试算不平衡!'}}</span>
        </div>
        <el-table :data="dialogTableData" style="width: 100%" border>
          <el-table-column label="项目" prop="project"/>
          <el-table-column label="借方金额" prop="debitAmount"/>
          <el-table-column label="贷方金额" prop="creditAmount"/>
          <el-table-column label="差额" prop="diffAmount"/>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from 'vue';
import { voucher } from '@/modules/finance/api/index';
import {useRouter} from "vue-router";
const router = useRouter();
const periodYearMonth = ref('');
const periodYearMonthZh = computed(() => {
  if (!periodYearMonth.value) return '';
  const year = periodYearMonth.value.slice(0, 4);
  const month = periodYearMonth.value.slice(4, 6);
  return `${year}年${month}月`;
})
const trialBalance = ref(false);
const loading = ref(false);
const tableData = ref([]);
const dialogTableData = reactive([
  {
    project: '期初余额(综合本位币)',
    type: 'Start',
    debitAmount: '',
    creditAmount: '',
    diffAmount: ''
  },
  {
    project: '累计发生额(综合本位币)',
    type: 'Sum',
    debitAmount: '',
    creditAmount: '',
    diffAmount: ''
  }
])
const showDialog = ref(false);
const getFinalTrialBalance = async () => {
  loading.value = true;
  try {
    const res = await voucher.queryFinalTrialBalance({});
    periodYearMonth.value = String(res.periodYearMonth);
    trialBalance.value = res.trialBalance;
    dialogTableData.forEach(item => {
      for (let k in item) {
        if (k !== 'project' && k !== 'type') {
          item[k] = res[k + item.type]
        }
      }
    })
    tableData.value = res.balanceSubjects || [];
    showDialog.value = true;
    console.log(res);
  } catch(e) {
    if(e.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    // ElMessage.error(e as string);
  }
  loading.value = false;
}
onMounted(() => {
  getFinalTrialBalance();
})
</script>
<style scoped lang='scss'>
.final-balance {
  .btn-wrap {
    display: flex;
    justify-content: space-between;
    .left-btn {
      width: 172px;
      height: 32px;
      background: rgba(118,42,219,0.08);
      border-radius: 2px;
      border: 1px solid rgba(118,42,219,0.15);
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #762ADB;
      line-height: 32px;
      text-align: center;
    }
  }
  .dialog-content {
    .result-line {
      text-align: center;
      margin-bottom: 30px;
      .result-text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #52585F;
        line-height: 22px;
        vertical-align: middle;
        margin-left: 8px;
      }
      .el-icon {
        vertical-align: middle;
      }
    }
  }
}
</style>
