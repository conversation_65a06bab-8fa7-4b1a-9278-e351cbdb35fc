<template>
  <div class="final-settlement" v-loading="loading">
    <div class="settle-content">
      <div class="settle-date">会计期间：{{ periodYearMonthZh }}</div>
      <div class="settle-list">
        <el-card style="width: 385px" v-for="(item, index) in listData" :key="index">
          <template #header>
            <div class="card-header">{{ item.title }}</div>
          </template>
          <div class="card-body">
            <div :class="['body-mount', item.settleStatus === 0 ? 'not' : '']">{{ item.settleStatus === 1 ? item.forwardAmount : item.notForwardAmount }}</div>
            <div class="body-btn">
              <el-button v-if="item.settleStatus === 1" type="primary" @click="viewVoucherHandler(item.type)">查看凭证</el-button>
              <template v-else>
                <el-button type="primary" plain @click="calculateHandler">测算金额</el-button>
                <el-button type="primary" @click="createVoucherHandler(item.type)">生成凭证</el-button>
              </template>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <div class="settle-footer">
      <el-button v-hasPerm="['finance:finalsettle:settleback']" type="primary" plain @click="reverseSettleHandler">反结账</el-button>
      <el-button v-hasPerm="['finance:finalsettle:settle']" type="primary" @click="openCheckDialog">结账</el-button>
    </div>
    <!-- 结转检查 -->
    <el-dialog v-model="showDialog" title="结账检查" width="500" class="finance-moudle">
      <div class="dialog-content">
        <div class="count-period">会计期间：{{ periodYearMonthZh }}</div>
        <div class="count-list">
          <div class="count-item" v-for="(item, index) in settleResultList" :key="index">
            <template v-if="item.flag">
              <el-icon color="#72BE60" size="12"><SuccessFilled /></el-icon>
              <div class="label-text">{{ item.successMsg }}</div>
            </template>
            <template v-else>
              <el-icon color="#FF1500" size="12"><WarningFilled /></el-icon>
              <div class="label-text">{{ item.errorMsg }}</div>
              <div class="error-opt" @click="errorOptHandler(item.key)">{{ item.errorOpt }}</div>
            </template>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="checkSettleFlag">重新检查</el-button>
          <el-button type="primary" @click="settleConfirmHandler" :disabled="settleFlag">已完成检查继续结账</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 生成凭证 -->
    <el-drawer v-model="showDrawer" title="生成凭证" size="80%" :close-on-click-modal="false">
      <voucher-bill :initData="voucherInit" :key="activeSettleType" :type="drawerType" ref="voucherRef" :loading="drawLoadind">
        <template #user>
          <div style="margin-right: 32px;">制单人: {{ userStore.user.nickName }}</div>
        </template>
        <template #btn>
          <el-button @click="showDrawer = false">关闭</el-button>
          <el-button type="primary" @click="drawerSaveHandler">保存</el-button>
        </template>
      </voucher-bill>
    </el-drawer>
    <!-- 查看凭证 -->
    <el-drawer v-model="showVoucherDialog" title="查看凭证" size="80%">
      <el-table v-loading="loading" :data="tableData" style="width: 100%" :span-method="rowMergeHanlder" border>
        <template #empty>
          <Empty />
        </template>
        <el-table-column label="凭证字号" prop="voucherWord" min-width="90">
          <template #default="{ row }">
            <span style="color: #762ADB;">{{ `${row.voucherWord}-${row.voucherNumber}` }}</span>
          </template>
        </el-table-column>
        <el-table-column label="日期" prop="voucherDate" min-width="140"/>
        <el-table-column label="摘要" prop="summary" min-width="160"/>
        <el-table-column label="会计科目" prop="subjectName" min-width="160">
          <template #default="{ row }">{{ row.subjectCode + ':'  + row.subjectFullName }}</template>
        </el-table-column>
        <el-table-column label="借方金额" prop="debitAmount" min-width="120"/>
        <el-table-column label="贷方金额" prop="creditAmount" min-width="120"/>
        <el-table-column label="操作人员" prop="operater" min-width="120">
          <template #default="{ row }">
            <div v-for="(opt, index) in row.operateLogs" :key="index">
              {{ opt.operateType === 0 ? '制单人' :  opt.operateType === 1 ? '审核人' : '记账人' }}: {{ opt.createUserName }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from 'vue';
import { voucher } from '@/modules/finance/api/index';
import { useUserStore } from '@/core/store';
import { useRouter } from 'vue-router';
import type { TVoucherFormBalanceItem, TVoucherForm, TVoucherDetailFlowItem, TVoucherItem, TCombineVoucherItem } from "@/modules/finance/types/voucher";
import moment from 'moment';
const router = useRouter();
const userStore = useUserStore();
const loading = ref(false);
const periodYearMonth = ref('');
const yearMonthToZh = (val: string) => {
  if (!val) return '';
  const year = val.slice(0, 4);
  const month = val.slice(4, 6);
  return `${year}年${month}月`;
}
const periodYearMonthZh = computed(() => {
  if (!periodYearMonth.value) return '';
  const year = periodYearMonth.value.slice(0, 4);
  const month = periodYearMonth.value.slice(4, 6);
  return `${year}年${month}月`;
})
const activeSettleType = ref(0);
const listData = reactive([
  {
    title: '结转损益',
    key: 'profitAndLoss',
    type: 0,
    forwardAmount: '',
    notForwardAmount: '',
    settleStatus: 0
  },
  {
    title: '结转本年利润',
    key: 'yearProfit',
    type: 1,
    forwardAmount: '',
    notForwardAmount: '',
    settleStatus: 0
  }
])
const calculateHandler = async () => {
  try {
    const res = await voucher.querySettleDetail({});
    ElMessage.success('已完成金额测算');
  } catch(e) {
    console.log(e);
  }
}
const getSettleDetail = async () => {
  loading.value = true;
  try {
    const res = await voucher.querySettleDetail({});
    periodYearMonth.value = String(res.periodYearMonth);
    listData.forEach(item => {
      for (let k in item) {
        if (k !== 'title' && k !== 'key' && k!== 'type') {
          item[k] = res[item.key][k];
        }
      }
    })
  } catch(e) {
    if(e.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    console.log(e);
  }
  loading.value = false;
}
/*
  结账与反结账
*/
const showDialog = ref(false);
const settleResultList = reactive([
  { successMsg: '凭证无断号', errorMsg: '凭证有断号', flag: false, key: 'brokenNum', errorOpt: '按凭证号依次整理短号' },
  { successMsg: '无结转损益', errorMsg: '待结转损益', flag: false, key: 'settleProfitAndLoss', errorOpt: '生成结转损益凭证' },
  { successMsg: '凭证已审核', errorMsg: '凭证未审核', flag: false, key: 'voucherAudit', errorOpt: '一键审核当月凭证' },
  { successMsg: '凭证已记账', errorMsg: '凭证未记账', flag: false, key: 'voucherAccount', errorOpt: '一键记账当月凭证' },
  { successMsg: '试算平衡', errorMsg: '试算不平衡', flag: false, key: 'trialBalance', errorOpt: '前往期初余额' }
])
const settleFlag = computed(() => {
  return settleResultList.some(item => item.flag === false);
})
const checkSettleFlag = async () => {
  try {
    const params = {
      periodYearMonth: periodYearMonth.value
    }
    const res = await voucher.settleVoucherCheck(params);
    settleResultList.forEach(item => {
      item.flag = res[item.key];
    })
    if (!res.trialBalance) {
      if (!res.trialBalanceStart && !res.trialBalanceEnd) {
        settleResultList[4].errorOpt = '期初余额、期末试算不平衡';
      } else if (!res.trialBalanceEnd) {
        settleResultList[4].errorOpt = '期末试算不平衡';
      } else {
        settleResultList[4].errorOpt = '期初余额不平衡';
      }
    }
  } catch(err) {
    console.log(err);
  }
}
const openCheckDialog = async () => {
  loading.value = true;
  await checkSettleFlag();
  showDialog.value = true;
  loading.value = false;
}
const createForCheck = ref(false);
const errorOptHandler = async (optType: string) => {
  switch (optType) {
    case 'brokenNum':
      const params = {
        refreshType: 0,
        voucherWord: '',
        periodYearMonth: periodYearMonth.value
      }
      try {
        await voucher.refreshNumVoucher(params);
        ElMessage.success('凭证号整理成功!');
        await checkSettleFlag();
      } catch(err) {
        console.log(err);
      }
      break;
    case 'settleProfitAndLoss':
      createForCheck.value = true;
      createVoucherHandler(0);
      break;
    case 'voucherAudit':
      const params1 = {
        auditStatus: 1,
        periodYearMonth: periodYearMonth.value
      }
      try {
        await voucher.settleVoucherAudit(params1);
        ElMessage.success('一键审核成功!');
        await checkSettleFlag();
      } catch(err) {
        console.log(err);
      }
      break;
    case 'voucherAccount':
      const params2 = {
        auditStatus: 2,
        periodYearMonth: periodYearMonth.value
      }
      try {
        await voucher.settleVoucherAudit(params2);
        ElMessage.success('一键记账成功!');
        await checkSettleFlag();
      } catch(err) {
        console.log(err);
      }
      break;
    case 'trialBalance':
      router.push('/finance/finalManage/balance');
      break;
  }
}
const settleConfirmHandler = async () => {
  const params = {
    periodYearMonth: periodYearMonth.value,
    settleStatus: 2
  }
  try {
    await voucher.settleVoucher(params);
    ElMessage.success('结账成功!');
    getSettleDetail();
    showDialog.value = false;
  } catch(err) {
    console.log(err);
  }
}
const reverseSettleHandler = () => {
  const prevMonth = moment(periodYearMonth.value).subtract(1, 'months').format('YYYYMM');
  const prevMonthZh = yearMonthToZh(prevMonth);
  ElMessageBox.confirm(`确定对 ${prevMonthZh} 进行反结账吗？`,'提示',
    {
      confirmButtonText: '提交',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
        const params = {
          periodYearMonth: periodYearMonth.value,
          settleStatus: 3
        }
        try {
          await voucher.settleVoucher(params);
          ElMessage.success('反结账成功!');
          getSettleDetail();
        } catch(err) {
          console.log(err);
        }
    }).catch(() => {
      ElMessage.info('已取消');
    })
}

/* 生成凭证 */
const voucherRef = ref();
const showDrawer = ref(false);
const drawLoadind = ref(false);
const drawerType = ref('');
const voucherInit = reactive<TVoucherForm>({
  id: '',
  auditStatus: '',
  voucherWord: '',
  voucherNumber: 0,
  voucherDate: '',
  remark: '',
  attachment: [],
  balanceFlows: [],
  voucherSource: 0
})
const tanslateStringToArr = (value: string) => { // 将数字字符串转换成对应位置的数组
  if (value === '' || value === '0' || value === null) return ['', '', '', '', '', '', '', '', '', '', '', '', '', ''];
  if (!value.includes('.')) {
    value = value+'.00';
  } else {
    const index = value.indexOf('.');
    const len = value.slice(index).length;
    value = len === 2 ? value + '0' : value;
  }
  const formateValue = value.trim().replace(/[-.]/g, '');
  const resultArr = formateValue.split('');
  while(resultArr.length < 14) {
    resultArr.unshift('');
  }
  return resultArr;
}
const initBalanceFlowsHandler = (balanceFlow:TVoucherDetailFlowItem[] ):TVoucherFormBalanceItem[] => {
  return balanceFlow.map(item => {
    return {
      summary: item.summary,
      showSummaryInput: false,
      subjectId: item.subjectId,
      equivalentFlag: item.equivalentFlag,
      subjectCode: item.subjectCode,
      subjectName: item.subjectFullName,
      debitAmount: item.debitAmount || '',
      debitAmountArr: tanslateStringToArr(item.debitAmount),
      showDebitInput: false,
      creditAmount: item.creditAmount || '',
      creditAmountArr: tanslateStringToArr(item.creditAmount),
      showCreditInput: false,
      auxiliaryName: item.auxiliaryName,
      initBalance: item.subjectBalanceDirection === '借' ? Number(item.currentBalanceAmount)  - Number(item.debitAmount) + Number(item.creditAmount) : Number(item.currentBalanceAmount)  - Number(item.creditAmount) + Number(item.debitAmount),
      finalBalance: +item.currentBalanceAmount,
      subjectBalanceDirection: item.subjectBalanceDirection
    }
  })
}
const createVoucherHandler = async (type: number) => {
  loading.value = true;
  activeSettleType.value = type;
  try {
    const params = {
      periodYearMonth: periodYearMonth.value,
      settleType: type
    }
    const res = await voucher.settleCreateVoucher(params);
    drawerType.value = 'create';
    for (let k in voucherInit) {
      if (k === 'balanceFlows') {
        voucherInit.balanceFlows = initBalanceFlowsHandler(res.balanceFlows);
      } else if (k === 'voucherDate') {
        voucherInit.voucherDate = String(res.voucherDate);
      } else if (k === 'attachment') {
        voucherInit.attachment = res.attachment?.length ? JSON.parse(res.attachment) : [];
      } else {
        voucherInit[k] = res[k];
      }
    }
    showDrawer.value = true;
    loading.value = false;
  } catch(e) {
    loading.value = false;
    console.log(e);
  }
}
const drawerSaveHandler = async () => {
  drawLoadind.value = true;
  if (!voucherRef.value) return;
  const isValid = voucherRef.value.checkVoucherFormValid();
  if (isValid) {
    const params = voucherRef.value.getSubmitParams();
    params.settleType = activeSettleType.value;
    try {
      loading.value = true;
      const res = await voucher.saveVoucher(params);
      if (res) {
        ElMessage.success('生成凭证成功');
        showDrawer.value = false;
        if (createForCheck.value) {
          checkSettleFlag();
          createForCheck.value = false;
        } else {
          getSettleDetail();
        }
      }
      loading.value = false;
    } catch(err) {
      loading.value = false;
      console.log(err)
    }
  }
  drawLoadind.value = false;
}
/* 查看凭证 */
const showVoucherDialog = ref(false);
const tableData = reactive<TVoucherItem[]>([]);
const combineTableData = ref<TCombineVoucherItem[]>([]);
const countElement = (arr: TVoucherItem[]) => {
  const newArr = [...new Set(arr.map(item => item.voucherId))];
  return newArr.map(item => {
    return {
      keys: item,
      start: tableData.findIndex(start => start.voucherId === item),
      end: tableData.findLastIndex(start => start.voucherId === item)
    }
  })
}
const rowMergeHanlder = ( { row, column, rowIndex, columnIndex }) => {
  if (columnIndex < 3 || columnIndex > 5) {
    const activeItem = combineTableData.value.find(item => item.keys === row.voucherId);
    if (rowIndex >= activeItem!.start && rowIndex <= activeItem!.end) {
      if (rowIndex === activeItem!.start) {
        return {
          rowspan: activeItem!.end + 1 - activeItem!.start,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    }
  }
}
const viewVoucherHandler = async (type: number) => {
  tableData.length = 0;
  loading.value = true;
  const params = {
    page: 1,
    limit: 100,
    periodYearMonth: periodYearMonth.value,
    settleType: type
  }
  try {
    const res = await voucher.settleViewVoucherList(params);
    tableData.push(...res.records);
    combineTableData.value = countElement(tableData);
    showVoucherDialog.value = true;
  } catch(err) {
    console.log(err);
  }
  loading.value = false;
}
onMounted(() => {
  getSettleDetail();
})
</script>
<style scoped lang='scss'>
.final-settlement {
  min-height: 100%;
  background-color: #ffffff;
  position: relative;
  .settle-content {
    padding: 40px;
    .settle-date {
      width: 172px;
      height: 32px;
      background: rgba(118,42,219,0.08);
      border-radius: 2px;
      border: 1px solid rgba(118,42,219,0.15);
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #762ADB;
      line-height: 32px;
      text-align: center;
    }
    .settle-list {
      margin-top: 20px;
      display: grid;
      grid-template-columns: repeat(auto-fill, 385px);
      gap: 20px;
      :deep(.el-card__header) {
        padding: 15px 20px;
        background-color: #F4F6FA;
        font-weight: 600;
      }
      :deep(.el-card__body) {
        padding: 35px 20px 40px 20px;
      }
      .card-body {
        text-align: center;
        .body-mount {
          text-align: center;
          font-family: DINPro, DINPro;
          font-weight: bold;
          font-size: 24px;
          color: #762ADB;
          line-height: 31px;
          &.not {
            color:#151719;
          }
        }
        .body-btn {
          margin-top: 24px;
        }
      }
    }
  }
  .settle-footer {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    padding: 12px 30px;
    box-shadow: inset 0px 1px 0px 0px #E5E7F3;
    text-align: right;
  }
  .dialog-content {
    .count-period {
      padding-left: 16px;
      height: 32px;
      background: rgba(118,42,219,0.08);
      border-radius: 2px;
      border: 1px solid rgba(118,42,219,0.15);
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #762ADB;
      line-height: 32px;
    }
    .count-list {
      margin-top: 40px;
      .count-item {
        margin-bottom: 28px;
        display: flex;
        align-items: center;
        padding: 0 40px;
        &:last-child {
          margin-bottom: 0;
        }
        .label-text {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #52585F;
          line-height: 12px;
          margin-left: 8px;
        }
        .error-opt {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #762ADB;
          line-height: 12px;
          flex: 1;
          text-align: right;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
