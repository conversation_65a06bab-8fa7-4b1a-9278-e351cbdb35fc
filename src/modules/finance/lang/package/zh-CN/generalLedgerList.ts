export default {
  generalLedgerList: {
    label: {
      accountingPeriod: "会计期间：",
      startAccount: "起始科目：",
      endAccount: "结束科目：",
      accountLevel: "科目级别：",
      pleaseSelect: "请选择",
      to: "至",
      showContent: "显示内容：",
      start: "开始日期",
      end: "结束日期",
      showauxiliaryAccounting: "显示明细栏余额",
      hideZeroBalance: "余额为0不显示",
      hideNoTransactionZeroBalance: "无发生额且余额为0不显示",
      hideSummaryIfNoTransaction: "无发生额不显示本期合计、本年累计"
    },
    table: {
      accountCode: "科目编码",
      period: "期间",
      summary: "摘要",
      debit: "借方",
      credit: "贷方",
      direction: "方向",
      balance: "余额",
      creditBalance: "贷",
      debitBalance: "借",
      accountName: "科目名称",
    }
  }
}