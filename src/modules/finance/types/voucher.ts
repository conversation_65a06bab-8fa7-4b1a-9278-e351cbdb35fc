export interface TVoucherTypeItem { //凭证字项
  displayTitle: string,
  id: string,
  isDefault: number,
  voucherWord: string,
  nextNum: number
}

export interface TSummaryItem { //摘要项
  id: string,
  summaryContent: string
}

export interface TSubjectItem { //会计科目项
  id: string,
  subjectCode: string,
  subjectFullName: string,
  subjectAttribute: number, //核算属性，0-无，1-现金科目，2-银行科目
  equivalentFlag: number //是否等价物，0-否，1-是,
  subjectBalanceDirection: string
}

export interface TPaginationList<T> { //分页列表公用
    current: string;
    size: string;
    total: string;
    pages: string;
    records: T[];
}

export interface TCombineVoucherItem {
  keys: string,
  start: number,
  end: number
}
export interface TVoucherItem { //凭证列表项
  auditStatus: number,
  auxiliaryName: string,
  creditAmount: string,
  debitAmount: string,
  occurAmount: string,
  operateLogs: string[],
  periodYearMonth: number,
  subjectCode: string,
  subjectId: string,
  subjectName: string,
  summary: string,
  voucherDate: number,
  voucherId: string,
  voucherNumber: number,
  voucherWord: string
}

export interface TVoucherFormBalanceItem { //重组凭证项-页面展示
  summary: string,
  showSummaryInput: boolean,
  subjectId: string,
  equivalentFlag: number,
  subjectCode: string,
  subjectName: string,
  debitAmount: string,
  debitAmountArr: string[]
  showDebitInput: boolean,
  creditAmount: string,
  creditAmountArr: string[],
  showCreditInput: boolean,
  auxiliaryName: string,
  initBalance: string,
  finalBalance: number,
  subjectBalanceDirection: string
}

export interface TVoucherForm { //重组凭证-页面展示
  id: string,
  auditStatus: string | number,
  voucherWord: string,
  voucherNumber: number,
  voucherDate: string,
  remark: string,
  attachment: [],
  voucherSource?: number,
  balanceFlows: TVoucherFormBalanceItem[]
}

export interface TVoucherDetail { //凭证详情-接口返回
  id: string,
  auditStatus: number,
  voucherWord: string,
  voucherNumber: number,
  voucherDate: string,
  remark: string,
  attachment: string,
  balanceFlows: TVoucherDetailFlowItem[]
  createUser: string,
  createTime: string,
  operateLogs: TVoucherDetailLogsItem[]
}
export interface TVoucherDetailFlowItem {//凭证详情具体科目-接口返回
  summary: string,
  subjectId: string,
  equivalentFlag: number,
  subjectCode: string,
  subjectName: string,
  subjectFullName: string,
  debitAmount: string,
  creditAmount: string,
  auxiliaryName: string,
  subjectBalanceDirection: string,
  currentBalanceAmount: string
}
export interface TVoucherDetailLogsItem {//凭证详情操纵日志-接口返回
  createTime: string,
  createUserName: string,
  operateType: number
}

export interface TCashFlowItem {
  childList: TCashFlowItem[] | null,
  itemCode: string,
  itemName: string
}
