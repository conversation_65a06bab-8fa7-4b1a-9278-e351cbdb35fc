import { defineStore } from "pinia";

interface Product {
  productImg: string;
  productName: string;
  unitName: string;
  brandName: string;
  purchasePrice: number;
  defaultSupplierName: string;
  remark: string;
  // Add other product properties as needed
}

export const useInquiryStore = defineStore("inquiry", {
  state: () => ({
    selectedProducts: [] as Product[],
    inquiryForm: {
      deliveryPeriod: [],
      startDeliveryDate: "",
      endDeliveryDate: "",
      inquiryType: "",
      warehouseId: "",
      keywords: "",
      chooseType: 1,
    },
    createInquiryForm: {
      deliveryPeriod: [], // 计划交货周期日期范围
      startDeliveryDate: "", // 计划交货周期开始日期
      endDeliveryDate: "", // 计划交货周期结束日期
      inquiryType: "", // 询价类型
      warehouseId: "", // 仓库
      warehouseName: "", // 仓库名称
      title: "", // 名称,关键字搜索
      chooseType: 1, // 1-新增询价单选择产品 2-新增采购单选择商品 3-新增采购需求和采购任务选择商品
    }
  }),

  actions: {
    setSelectedProducts(products: Product[]) {
      this.selectedProducts = products;
    },

    setInquiryForm(form: any) {
      this.inquiryForm = { ...form };
    },

    setCreateInquiryForm(form: any) {
      this.createInquiryForm = { ...form };
    },

    clearStore() {
      this.selectedProducts = [];
      this.inquiryForm = {
        deliveryPeriod: [],
        startDeliveryDate: "",
        endDeliveryDate: "",
        inquiryType: "",
        warehouseId: "",
        keywords: "",
        chooseType: 1,
      };
    },
  },
});
