import request from "@/core/utils/request";

const SUPPLY_FINANCE_SERVER = 'supply-finance-server';

class API {
  /** 账套相关接口 */
  // 获取账套列表数据
  static getBooksPageList(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/setBooks/list`,
      method: "post",
      data,
    });
  }
  // 添加账套数据
  static addBooks(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/setBooks/add`,
      method: "post",
      data,
    });
  }
  // 获取账套详情数据
  static booksDetail(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/setBooks/detail`,
      method: "post",
      data,
    });
  }
  // 编辑账套数据
  static updateBooks(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/setBooks/update`,
      method: "post",
      data,
    });
  }
  // 删除账套数据
  static removeBooks(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/setBooks/remove`,
      method: "post",
      data,
    });
  }
  // 切换账套
  static setUserCurrentSetBooks(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/loginExt/setUserCurrentSetBooks`,
      method: "post",
      data,
    });
  }

  // 获取用户列表
  static getUserList(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/setBooksUser/getUserList`,
      method: "post",
      data,
    });
  }
  // 设置账套用户列表
  static setGrant(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/setBooksUser/grant`,
      method: "post",
      data,
    });
  }

  /** 会计科目相关接口*/
  // 获取科目列表数据
  static getAccountingList(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/getList`,
      method: "post",
      data,
    });
  }
  // 生成科目编码
  static generateCode(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/generateCode`,
      method: "post",
      data,
    });
  }
  // 新增科目
  static addAccounting(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/add`,
      method: "post",
      data,
    });
  }
  // 编辑科目
  static updateAccounting(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/update`,
      method: "post",
      data,
    });
  }
  // 删除科目
  static removeAccounting(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/remove`,
      method: "post",
      data,
    });
  }
  // 科目启用禁用
  static updateStatusAccounting(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/updateStatus`,
      method: "post",
      data,
    });
  }
  // 获取科目详情
  static queryAccountingDetail(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/detail`,
      method: "post",
      data,
    });
  }
  // 获取科目详情
  static initialBalance(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/initialBalance`,
      method: "post",
      data,
    });
  }
  // 获取科目详情
  static getCheckoutFlag(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/getCheckoutFlag`,
      method: "post",
      data,
    });
  }
  // 获取科目详情
  static trialBalance(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/trialBalance`,
      method: "post",
      data,
    });
  }


  /** 凭证字相关接口*/
  // 获取凭证字列表数据
  static queryVoucherTypeList(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/voucherType/queryPageList`,
      method: "post",
      data,
    });
  }
  // 新增凭证字
  static saveVoucher(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/voucherType/save`,
      method: "post",
      data,
    });
  }
  // 删除凭证字
  static removeVoucher(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/voucherType/delete`,
      method: "post",
      data,
    });
  }
  // 批量删除凭证字
  static batchRemoveVoucher(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/voucherType/batchDelete`,
      method: "post",
      data,
    });
  }
  // 修改凭证字
  static updateVoucher(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/voucherType/update`,
      method: "post",
      data,
    });
  }
  /** 摘要相关接口*/
  // 获取摘要列表数据
  static queryVoucherSummaryPageList(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/voucherSummary/queryPageList`,
      method: "post",
      data,
    });
  }
  // 新增摘要
  static saveVoucherSummary(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/voucherSummary/save`,
      method: "post",
      data,
    });
  }
  // 删除摘要
  static deleteVoucherSummary(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/voucherSummary/delete`,
      method: "post",
      data,
    });
  }
  // 修改摘要
  static updateVoucherSummary(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/voucherSummary/update`,
      method: "post",
      data,
    });
  }

  // 获取数据字典
  static getSelectItemByTypeCode(data:any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/baseDict/getSelectItemByTypeCode`,
      method: "post",
      data,
    });
  }












}



export default API;

