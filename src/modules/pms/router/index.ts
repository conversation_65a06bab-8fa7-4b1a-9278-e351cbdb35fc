import { RouteRecordRaw } from "vue-router";

export const Layout = () => import("@/core/layout/index.vue");

// 路由配置
export const constantRoutes: RouteRecordRaw[] = [
  /* {
    path: "/pms/supplier",
    component: Layout,
    children: [
      {
        path: "supplierManagement",
        component: () => import("@pms/views/supplier/supplierManagement.vue"),
        name: "SupplierManagement",
        meta: {
          title: "供应商管理",
          hidden: true,
          keepAlive: true,
        },
      },
      {
        path: "addSupplier",
        component: () => import("@pms/views/supplier/addSupplier.vue"),
        meta: {
          title: "添加供应商",
          hidden: true,
        },
      },
    ],
  },*/
  {
    path: "/pms/supplier",
    component: Layout,
    children: [
      {
        path: "addSupplier",
        name: "AddSupplier",
        component: () => import("@pms/views/supplier/addSupplier.vue"),
        meta: {
          title: "添加供应商",
          hidden: true,
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/pms/purchase",
    component: Layout,
    children: [
      {
        path: "purchaseRequirementsDetail",
        component: () =>
          import("@pms/views/purchase/purchaseRequirements/detail.vue"),
        meta: {
          title: "采购需求详情",
          hidden: true,
        },
      },
      {
        path: "addPurchaseRequirements",
        component: () =>
          import("@pms/views/purchase/purchaseRequirements/add.vue"),
        meta: {
          title: "新建采购需求",
          hidden: true,
        },
      },
      {
        path: "addPurchaseOrder",
        name: "AddPurchaseOrder",
        component: () =>
          import("@pms/views/purchase/purchaseOrder/addPurchaseOrder.vue"),
        meta: {
          title: "新增采购单",
          hidden: true,
          keepAlive: true,
        },
      },
      {
        path: "purchaseOrderDetail",
        component: () =>
          import("@pms/views/purchase/purchaseOrder/purchaseOrderDetail.vue"),
        meta: {
          title: "采购单详情",
          hidden: true,
        },
      },
      {
        path: "purchaseOrderAuditDetail",
        name: "PurchaseOrderAuditDetail",
        component: () =>
          import("@pms/views/purchase/purchaseOrderAudit/purchaseOrderAuditDetail.vue"),
        meta: {
          title: "采购单审核详情",
          hidden: true,
          keepAlive: true,
        },
      },
      {
        path: "createInquery",
        component: () =>
          import("@pms/views/purchase/inquery/createInquery.vue"),
        meta: {
          title: "新建询价单",
          hidden: true,
        },
      },
      {
        path: "copyInquery",
        component: () => import("@pms/views/purchase/inquery/copyInquery.vue"),
        meta: {
          title: "复制询价单",
          hidden: true,
        },
      },
      {
        path: "inqueyDetail", // 查看
        component: () =>
          import("@pms/views/purchase/inquery/inqueryDetail.vue"),
        meta: {
          title: "询价详情",
          hidden: true,
        },
      },
      {
        path: "productMatching", // 查看
        component: () =>
          import("@pms/views/purchase/inquery/productMatching.vue"),
        meta: {
          title: "新建询价单",
          hidden: true,
        },
      },
      {
        path: "inqueyReport", // 报价
        component: () =>
          import("@pms/views/purchase/inquery/inqueryReport.vue"),
        meta: {
          title: "报价",
          hidden: true,
        },
      },
      {
        path: "inqueyPricing", // 定价
        component: () =>
          import("@pms/views/purchase/inquery/inqueryPricing.vue"),
        meta: {
          title: "定价",
          hidden: true,
        },
      },
      // {
      //   path: "purchaseTasks",
      //   component: () => import("@pms/views/purchase/purchaseTasks/index.vue"),
      //   name: "PurchaseTasks",
      //   meta: {
      //     title: "采购任务",
      //     keepAlive: true,
      //   },
      // },
      {
        path: "addPurchaseTasks",
        component: () =>
          import("@pms/views/purchase/purchaseTasks/addPurchaseTasks.vue"),
        meta: {
          title: "新建采购任务",
          hidden: true,
        },
      },
      {
        path: "purchaseReturnDetail",
        component: () =>
          import("@pms/views/purchase/purchaseReturn/detail.vue"),
        meta: {
          title: "采购退货详情",
          hidden: true,
        },
      },
      {
        path: "purchaseReturnApprove",
        component: () =>
          import("@pms/views/purchase/purchaseReturn/approve.vue"),
        meta: {
          title: "采购退货审核",
          hidden: true,
        },
      },
      {
        path: "purchaseReturnAddOrEdit",
        name: "PurchaseReturnAddOrEdit",
        component: () =>
          import("@pms/views/purchase/purchaseReturn/addPurchaseReturn.vue"),
        meta: {
          title: "采购退货新增或修改",
          hidden: true,
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/pms/product",
    component: Layout,
    children: [
      {
        path: "addPurchase",
        component: () => import("@pms/views/product/purchase/addPurchase.vue"),
        meta: {
          title: "新增采购商品",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/pms/finance",
    component: Layout,
    children: [
      {
        path: "reconcile",
        component: () =>
          import("@pms/views/finance/purchaseReconcile/reconcile.vue"),
        meta: {
          title: "对账",
          hidden: true,
        },
      },
      {
        path: "purchaseReceivablesDetails", // 采购应收账单明细
        component: () =>
          import("@pms/views/finance/purchaseReceivables/detail.vue"),
        meta: {
          title: "明细",
          hidden: true,
        },
      },
      {
        path: "purchaseCollection", // 采购应收账单收款
        component: () =>
          import("@pms/views/finance/purchaseReceivables/collection.vue"),
        meta: {
          title: "收款",
          hidden: true,
        },
      },
      {
        path: "payment", // 采购应付账单付款
        component: () =>
          import("@pms/views/finance/payableManagement/payment/index.vue"),
        meta: {
          title: "付款",
          hidden: true,
        },
      },
      {
        path: "payableDetails", // 采购应付账单明细
        component: () =>
          import(
            "@pms/views/finance/payableManagement/payableDetails/index.vue"
          ),
        meta: {
          title: "明细",
          hidden: true,
        },
      },
      {
        path: "payableAggregationDetails", // 应付账单汇总明细
        name: "PayableAggregationDetails",
        component: () =>
          import(
            "@pms/views/finance/payableAggregation/details.vue"
            ),
        meta: {
          title: "应付账单汇总明细",
          hidden: true,
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/pms/contract",
    component: Layout,
    children: [
      {
        path: "addContract",
        name: "AddContract",
        component: () => import("@pms/views/contract/add.vue"),
        meta: {
          title: "新增合同",
          hidden: true,
          keepAlive: true,

        },
      },
      {
        path: "updateContract",
        component: () => import("@pms/views/contract/add.vue"),
        meta: {
          title: "编辑合同",
          hidden: true,
        },
      },
      {
        path: "contractDetail",
        component: () => import("@pms/views/contract/detail.vue"),
        meta: {
          title: "合同详情",
          hidden: true,
        },
      },
      {
        path: "contractAudit",
        component: () => import("@pms/views/contract/audit.vue"),
        meta: {
          title: "审核合同",
          hidden: true,
        },
      },
      {
        path: "reviewDetail",
        component: () => import("@pms/views/contract/reviewDetail.vue"),
        meta: {
          title: "审核详情",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/pms/workOrder",
    component: Layout,
    children: [
      {
        path: "dealwith",
        component: () =>
          import("@pms/views/workOrder/abnormalManagement/dealwith.vue"),
        meta: {
          title: "处理",
          hidden: true,
        },
      },
    ],
  },
  /*  {
    path: "/supply/system",
    component: Layout,
    children: [
      {
        path: "menuAuthority",
        component: () => import("@pms/views/system/menuAuthority/index.vue"),
        meta: {
          title: "权限管理",
          hidden: true,
        },
      },
      {
        path: "role",
        component: () => import("@pms/views/system/role/index.vue"),
        meta: {
          title: "角色管理",
        },
      },
      {
        path: "accountManagement",
        component: () =>
          import("@pms/views/system/accountManagement/index.vue"),
        meta: {
          title: "账号管理",
        },
      },
      {
        path: "dept",
        component: () => import("@pms/views/system/dept/index.vue"),
        meta: {
          title: "部门管理",
        },
      },
    ],
  }, */
];
