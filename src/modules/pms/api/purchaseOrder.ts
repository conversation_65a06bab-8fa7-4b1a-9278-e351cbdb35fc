import request from "@/core/utils/request";
import { SupplierForm } from "@/modules/pms/api/supplier";

const PURCHASE_BASE_URL = "/supply-pms/purchaseOrder";

class PurchaseOrderAPI {
  /** 仓库列表 */
  static getStorehouseList() {
    return request({
      url: `${PURCHASE_BASE_URL}/getStorehouseList`,
      method: "get",
    });
  }

  /** 获取采购商品分页数据 */
  static getPurchaseOrderPage(queryParams?: PurchaseOrderPageQuery) {
    return request<any, PageResult<PurchaseOrderPageVO[]>>({
      url: `${PURCHASE_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }

  /** 导出采购商品 */
  static exportPurchaseOrder(queryParams?: PurchaseOrderPageQuery) {
    return request({
      url: `${PURCHASE_BASE_URL}/export`,
      method: "post",
      data: queryParams,
    });
  }

  /** 关闭采购单 */
  static closePurchaseOrder(data: ClosePurchaseOrderFrom) {
    return request({
      url: `${PURCHASE_BASE_URL}/shutdown`,
      method: "post",
      data: data,
    });
  }

  /**发送采购单 */
  static sendPurchaseOrder(data: { ids?: [string] }) {
    return request({
      url: `${PURCHASE_BASE_URL}/send`,
      method: "post",
      data: data,
    });
  }

  /** 查询所有可选的商品列表(分页展示采购单选择的商品)*/
  static queryProductAll(data?: ProductAllPageQuery) {
    return request<any, PageResult<ProductAllPageVO>>({
      // url: `${PURCHASE_BASE_URL}/queryProductAll`,
      url: `/supply-pms/inquiry/queryInquiryPurchaseChooseProductListPage`,
      method: "post",
      data: data,
      // filterImgFields:['productImg']
    });
  }

  /** 根据商品id查询采购单编辑详情 */
  static queryPurchaseOrderDetail(data: { id?: string }) {
    return request<any, PurchaseOrderFrom>({
      url: `${PURCHASE_BASE_URL}/detail`,
      method: "post",
      data: data,
      // filterImgFields:['imagesUrls']
    });
  }

  /** 添加采购单 */
  static addPurchaseOrder(data: PurchaseOrderFrom) {
    return request({
      url: `${PURCHASE_BASE_URL}/save`,
      method: "post",
      data: data,
    });
  }

  /** 添加编辑采购单 */
  static editPurchaseOrder(data: PurchaseOrderFrom) {
    return request({
      url: `${PURCHASE_BASE_URL}/update`,
      method: "post",
      data: data,
    });
  }

  /** 保存并发送采购单 */
  static saveAndSendPurchaseOrder(data: PurchaseOrderFrom) {
    return request({
      url: `${PURCHASE_BASE_URL}/saveAndSendPurchaseOrder`,
      method: "post",
      data: data,
    });
  }

  /** 查询供应商绑定的未执行和执行中的合同列表 */
  static querySupplierContractList(data: { supplierId?: string }) {
    return request<any, any>({
      url: `/supply-pms/contract/querySupplierContractList`,
      method: "post",
      data: data,
    });
  }

  /** 审核分页查询*/
  static getPurchaseOrderApprovePage(data:any) {
    return request({
      url: `${PURCHASE_BASE_URL}/approvePage`,
      method: "post",
      data: data,
    });
  }
  /** 审核*/
  static approvePurchaseOrder(data:any) {
    return request({
      url: `${PURCHASE_BASE_URL}/approve`,
      method: "post",
      data: data,
    });
  }

  /** 采购单关闭同步 */
  static syncShutdowm(data:any) {
    return request({
      url: `${PURCHASE_BASE_URL}/syncShutdowm`,
      method: "post",
      data: data,
    });
  }
  /** 采购单审核 同步*/
  static syncApprove(data:any) {
    return request({
      url: `${PURCHASE_BASE_URL}/syncApprove`,
      method: "post",
      data: data,
    });
  }

  /**
   * 获取审核人员
   */
  static queryApproveUser() {
    return request({
      url: `/supply-base/user/all`,
      method: "get",
    });
  }
  /**
   * 查询配置是否启用
   */
  static querySystemParameters(functionCode:string) {
    return request({
      url: `/supply-biz-common/systemParameters/queryEnable?functionCode=${functionCode}`,
      method: "post",
    });
  }

}





export default PurchaseOrderAPI;

/** 采购单分页查询参数 */
export interface PurchaseOrderPageQuery extends PageQuery {
  /** 时间类型 */
  dateType?: number;
  /** 时间范围 */
  dateRange?: string[];
  startCreateTime?: string;
  endCreateTime?: string;
  startPlanDeliveryDate?: string;
  endPlanDeliveryDate?: string;
  /** 采购类型 */
  orderType?: number;
  /** 单据来源 */
  orderSource?: number;
  /** 供应商 */
  supplierId?: string;
  /** 状态 */
  purchaseStatus?: number;
  /** 采购员 */
  purchaseUserId?: string;
}

/** 采购单分页对象 */
export interface PurchaseOrderPageVO {
  /** ID */
  id?: string;
  /** 采购单号 */
  orderCode?: string;
  /** 采购类型（1-供应商直供 2-市场自采） */
  orderType?: number;
  /** 单据来源 （1-手动新增 2-采购任务）*/
  orderSource?: number;
  /** 采购员 */
  purchaseUserName?: string;
  /** 供应商 */
  supplierName?: string;
  /** 商品数量 */
  totalPurchaseCount?: number;
  /** 计划交货日期 */
  planDeliveryDate?: string;
  /** 制单人 */
  createUserName?: string;
  /** 备注 */
  remark?: string;
  /** 采购状态(1-待采购 2-部分收货 3-全部收货 0-已关闭  4-待收货) */
  orderPurchaseStatus?: number;
  /** 要货申请状态(1-已发送 2-未发送) */
  sendStatus?: number;
  /** 创建时间 */
  createTime?: Date;
  /** 关闭原因 */
  shutdownReason?: string;
}

/** 关闭采购单对象 */
export interface ClosePurchaseOrderFrom {
  /** 采购单id */
  id?: string;
  /** 采购单号 */
  orderCode?: string;
  /** 关闭采购单原因 */
  shutdownReason?: string;
}

/** 采购单对象 */
export interface PurchaseOrderFrom {
  /** ID */
  id?: string;
  currencyCode?: string;
  /** 采购单号 */
  orderCode?: string;
  /** 采购类型（1-供应商直供 2-市场自采） */
  orderType?: number;
  /** 仓库*/
  warehouseId?: string;
  warehouseCode?: string;
  warehouseName?: string;
  /** 计划交货日期*/
  planDeliveryDate?: string;
  /** 采购员*/
  purchaseUserId?: string;
  purchaseUserName?: string;
  // purchasePersonnel?: string;
  /** 供应商*/
  supplierId?: string;
  supplierName?: string;
  /** 商品列表 */
  purchaseOrderDetailAddDTOList?: ProductAllPageVO[];
  purchaseOrderDetailVOList?: ProductAllPageVO[];
  totalReceivedCount?: string;
  totalReceivedAmount?: string;
  totalPurchaseCount?: string;
  totalPurchaseAmount?: string;
  /** 采购单附件信息 */
  orderAttachmentFiles?: string[];
  /** 备注 */
  remark?: string;
  /** 采购单位Id */
  unitId?: string;
  /** 采购单位 */
  unitName?: string;
  inventoryCount?: string;
  /** 送达图片 */
  deliveryAttachmentFileObject?: string[];
  /** 收运单据 */
  receiveTransportRecordVOList?: ReceivingDocumentVO[];
  /** 单据操作记录 */
  operationLogVOList?: DocumentOperationRecordVO[];
}

/** 可选商品列表弹窗分页查询参数 */
export interface ProductAllPageQuery extends PageQuery {
  chooseType?: number;
  /** 采购类型*/
  orderType?: number;
  /** 仓库*/
  warehouseId?: string;
  /** 计划交货日期*/
  startExpectedDeliveryDate?: string;
  /** 供应商*/
  supplierId?: string;
  keywords?: string;
  productName?: string;
  /** 商品分类 */
  productCategory?: string[];
  firstCategoryId?: string;
  secondCategoryId?: string;
  thirdCategoryId?: string;
  productCode?: string;
}

/** 可选商品列表弹窗分页对象 */
export interface ProductAllPageVO {
  /** ID */
  id?: string;
  /** 商品图片 */
  productImg?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 规格名称 */
  productSpecName?: string;
  /** 商品分类 */
  productCategoryFullName?: string;
  productCategory?: string;
  firstCategoryId?: string;
  secondCategoryId?: string;
  thirdCategoryId?: string;
  /** 币种Id */
  currency?: string;
  currencyCode?: string;
  /** 采购单位Id */
  unitId?: string;
  /** 采购单位 */
  unitName?: string;
  /** 现有库存 */
  onHandStock?: string;
  /** 计划采购量 */
  planPurchaseCount?: number;
  /** 计划采购价 */
  purchasePrice?: string;
  planPurchasePrice?: string;
  /** 计划采购金额 */
  planPurchaseAmount?: string;
  /** 已收货量 */
  receivedCount?: number;
  /** 已收货金额 */
  receivedAmount?: string;
  imagesUrls?: string;
}

/** 单据操作记录 */
export interface DocumentOperationRecordVO {
  /** ID */
  id?: string;
  /** 操作时间 */
  operationTime?: string;
  /** 操作类型 */
  operationRemark?: string;
  /** 操作人 */
  operationName?: string;
}

/** 收运单据 */
export interface ReceivingDocumentVO {
  /** ID */
  id?: string;
  /** 收运单号 */
  receiveTransportCode?: string;
  /** 收运时间 */
  receivingTime?: string;
  /** 收货数量 */
  totalReceivedCountThisTime?: number;
  /** 收货金额 */
  totalReceivedAmountThisTime?: string;
  /** 备注 */
  remark?: string;
  /** 操作人 */
  updateUserName?: string;
}
