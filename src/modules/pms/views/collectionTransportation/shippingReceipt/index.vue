<template>
  <div class="app-container">
    <!-- 收运单 -->
    <div class="receive-Transport">
      <div class="search-container">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto" label-position="left">
          <el-form-item prop="receiveTransportCode" :label="$t('shippingReceipt.label.receiveTransportCode')">
            <el-input v-model="queryParams.receiveTransportCode" :placeholder="$t('common.placeholder.inputTips')"
              clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item prop="purchaseCode" :label="$t('shippingReceipt.label.purchaseCode')">
            <el-input v-model="queryParams.purchaseCode" :placeholder="$t('common.placeholder.inputTips')" clearable
              @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item prop="supplierId" :label="$t('shippingReceipt.label.supplier')">
            <el-select v-model="queryParams.supplierId" clearable :placeholder="$t('common.placeholder.selectTips')"
              class="!w-[256px]">
              <el-option v-for="item in supplierList" :key="item.supplierId" :label="item.supplierName"
                :value="item.supplierId" />
            </el-select>
          </el-form-item>
          <el-form-item prop="buyerId" :label="$t('shippingReceipt.label.buyerName')">
            <el-select v-model="queryParams.buyerId" clearable :placeholder="$t('common.placeholder.selectTips')"
              class="!w-[256px]">
              <el-option v-for="item in buyerList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" v-hasPerm="['pms:CT:shippingReceipt:search']">
              {{ $t("common.search") }}
            </el-button>
            <el-button @click="handleResetQuery">
              {{ $t("common.reset") }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-card shadow="never" class="table-container">
        <el-table v-loading="receiveTransportLoading" :data="receiveTransportList">
            <template #empty>
                <Empty/>
            </template>
          <el-table-column :label="$t('shippingReceipt.label.receiveTransportCode')" prop="receiveTransportCode" />
          <el-table-column prop="receiveTransportDate" :label="$t('shippingReceipt.label.receiveTransportDate')"
            min-width="90">
            <template #default="scope">
              <span>
                {{ parseDateTime(scope.row.receiveTransportDate, "dateTime") }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('shippingReceipt.label.purchaseCode')" prop="purchaseCode" min-width="120" />

          <el-table-column prop="receiveTransportCount" :label="$t('shippingReceipt.label.receiveTransportCount')" />
          <el-table-column :label="$t('shippingReceipt.label.buyerName')" prop="buyerName" />
          <el-table-column :label="$t('shippingReceipt.label.supplier')" prop="supplierName" />
          <!-- 商品种类  暂无 -->
<!--          <el-table-column :label="$t('shippingReceipt.label.productCount')" prop="productCount" />-->
          <el-table-column :label="$t('shippingReceipt.label.createUserName')" prop="createUserName" />
          <el-table-column :label="$t('common.handle')" fixed="right" width="100">
            <template #default="scope">
              <el-button type="primary" link size="small" v-if="scope.row.confirmStatus === 0"
                @click="handleView(scope.row)" v-hasPerm="['pms:CT:shippingReceipt:detail']">
                {{ $t("shippingReceipt.button.viewBtn") }}
              </el-button>
              <el-button type="primary" link size="small" v-if="scope.row.confirmStatus === 1"
                @click="handleConfirm(scope.row)" v-hasPerm="['pms:CT:shippingReceipt:detail']">
                {{ $t("shippingReceipt.button.confirmBtn") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-if="receiveTransportTotal > 0" v-model:total="receiveTransportTotal"
          v-model:page="queryParams.page" v-model:limit="queryParams.limit" @pagination="handleQuery" />
      </el-card>
    </div>

    <!-- 采购单 -->
    <div class="shipping-Receipt mt2">
      <div class="search-container">
        <el-form ref="purchaseQueryFormRef" :model="purchaseQueryParams" :inline="true" label-width="auto"
          label-position="left">
          <el-form-item prop="orderCode" :label="$t('shippingReceipt.label.purchaseCode')">
            <el-input v-model="purchaseQueryParams.orderCode" :placeholder="$t('common.placeholder.inputTips')"
              clearable @keyup.enter="handleQueryPurchase" />
          </el-form-item>
          <el-form-item prop="supplierId" :label="$t('shippingReceipt.label.supplier')">
            <el-select v-model="purchaseQueryParams.supplierId" clearable
              :placeholder="$t('common.placeholder.selectTips')" class="!w-[256px]">
              <el-option v-for="item in supplierList" :key="item.supplierId" :label="item.supplierName"
                :value="item.supplierId" />
            </el-select>
          </el-form-item>
          <el-form-item prop="purchaseUserId" :label="$t('shippingReceipt.label.buyerName')">
            <el-select v-model="purchaseQueryParams.purchaseUserId" clearable
              :placeholder="$t('common.placeholder.selectTips')" class="!w-[256px]">
              <el-option v-for="item in buyerList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQueryPurchase">
              {{ $t("common.search") }}
            </el-button>
            <el-button @click="handleResetQueryPurchase">
              {{ $t("common.reset") }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-card shadow="never" class="table-container">
        <el-table v-loading="purchaseLoading" :data="purchaseList" highlight-current-row :key="new Date()">
            <template #empty>
                <Empty/>
            </template>
          <el-table-column :label="$t('purchaseOrder.label.orderCode')" prop="orderCode" width="180" />
          <el-table-column :label="$t('purchaseOrder.label.orderType')" prop="orderType">
            <template #default="scope">
              <span v-if="scope.row.orderType == 1">{{ t('purchaseOrder.orderTypeList.suppliersDirectSupply') }}</span>
              <span v-if="scope.row.orderType == 2">{{ t('purchaseOrder.orderTypeList.marketDirectSupply') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('purchaseOrder.label.purchaseUserName')" prop="purchaseUserName">
<!--            <template #default="scope">
              <EncryptPhone :nameType="true" :name="scope.row.purchaseUserName" />
            </template>-->
          </el-table-column>
          <el-table-column :label="$t('purchaseOrder.label.supplierName')" prop="supplierName" />
          <el-table-column :label="$t('purchaseOrder.label.remark')" prop="remark" />
          <el-table-column :label="$t('purchaseOrder.label.productNumber')" prop="totalPurchaseCount" align="center" />
          <el-table-column :label="$t('shippingReceipt.label.receivedQuantity')" prop="totalReceivedCount"
            align="center" />
          <el-table-column :label="$t('shippingReceipt.label.quantityToBeReceived')" prop="totalRemainingCount"
            align="center" />
          <el-table-column :label="$t('purchaseOrder.label.orderPurchaseStatus')">
            <template #default="scope">
              <div class="product-code">
                <div class="purchase">
                  <div class="purchase-status purchase-status-color1" v-if="scope.row.orderPurchaseStatus == 1">
                    {{ t('purchaseOrder.orderPurchaseStatusList.wattingPurchase') }}</div>
                  <div class="purchase-status purchase-status-color2" v-if="scope.row.orderPurchaseStatus == 2">
                    {{ t('purchaseOrder.orderPurchaseStatusList.partialPurchase') }}</div>
                  <div class="purchase-status purchase-status-color3" v-if="scope.row.orderPurchaseStatus == 3">
                    {{ t('purchaseOrder.orderPurchaseStatusList.partialPurchase') }}</div>
                  <div class="purchase-status purchase-status-color0" v-if="scope.row.orderPurchaseStatus == 0">
                    {{ t('purchaseOrder.orderPurchaseStatusList.closePurchase') }}</div>
                    <div class="purchase-status purchase-status-color4" v-if="scope.row.orderPurchaseStatus == 4">
                      {{ t('purchaseOrder.orderPurchaseStatusList.waitReceive') }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-if="purchaseTotal > 0" v-model:total="purchaseTotal" v-model:page="purchaseQueryParams.page"
          v-model:limit="purchaseQueryParams.limit" @pagination="handleQueryPurchase" />
      </el-card>
    </div>
    <template v-if="dialogVisible">
      <ShippingReceiptDetailDrawer
        v-model:visible="dialogVisible"
        ref="receiveTransport"
        :drawerType="drawerType"
        :data="rowData"
        :title="dialog.title"
        @onSubmit="submitHandle"
        @cancel="closeHandle" />
    </template>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n();
defineOptions({
  name: "ShippingReceipt",
  inheritAttrs: false,
});
import ShippingReceiptAPI, {
  ReceiveTransportPageQuery,
} from "@/modules/pms/api/shippingReceipt";
import PurchasePersonnelAPI from "@/modules/pms/api/purchasePersonnel";
import supplierAPI from "@/modules/pms/api/supplier";
import { parseDateTime } from "@/core/utils/index.js";
import Edit from "./components/edit.vue";
import ShippingReceiptDetailDrawer from './components/shippingReceiptDetailDrawer.vue';
import PurchaseOrderAPI, { PurchaseOrderPageVO, PurchaseOrderPageQuery } from "@/modules/pms/api/purchaseOrder";

const queryFormRef = ref(ElForm); //收运
const purchaseQueryFormRef = ref(ElForm); //采购
const receiveTransportLoading = ref(false); //收运
const purchaseLoading = ref(false); //采购
const receiveTransportTotal = ref(0); //收运
const purchaseTotal = ref(0); //采购
// 收运单表格数据
const receiveTransportList = ref([]);
//采购单表格数据
const purchaseList = ref([]);
//供应商列表
const supplierList = ref([]);
//采购员列表
const buyerList = ref([]);
let receiveTransport = ref();
const queryParams = reactive<ReceiveTransportPageQuery>({
  page: 1,
  limit: 20,
}); //收运
const purchaseQueryParams = reactive<ReceiveTransportPageQuery>({
  page: 1,
  limit: 20,
  querySource: 1, // 默认传1
  excludeOrderPurchaseStatusList: [0, 3], // 排除已关闭的采购单
  orderCode: '',
  purchaseUserId: '',
}); //采购

/**  弹窗对象  */
const dialog = reactive({
  visible: false,
  title: "",
});

const drawerType = ref('')
const rowData = ref('')
const dialogVisible = ref(false)

/** 收运单列表查询 */
function handleQuery() {
  receiveTransportLoading.value = true;
  ShippingReceiptAPI.getShippingReceiptPage(queryParams)
    .then((data: any) => {
      receiveTransportList.value = data.records;
      receiveTransportTotal.value = parseInt(data.total);
    })
    .finally(() => {
      receiveTransportLoading.value = false;
    });
}

/** 采购单列表查询 */
function handleQueryPurchase() {
  purchaseLoading.value = true;
  PurchaseOrderAPI.getPurchaseOrderPage(purchaseQueryParams)
    .then((data: any) => {
      purchaseList.value = data.records;
      purchaseTotal.value = parseInt(data.total);
    })
    .finally(() => {
      purchaseLoading.value = false;
    });
}
/** 收运单重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  handleQuery();
}

/** 采购单重置查询 */
function handleResetQueryPurchase() {
  purchaseQueryFormRef.value.resetFields();
  purchaseQueryParams.page = 1;
  handleQueryPurchase();
}
function submitHandle() {
  handleQuery();
  handleQueryPurchase();
}

function closeHandle() {
  dialogVisible.value = false;
  // console.log('关闭了-----------------------')
}

/** 供应商列表查询 */
function getSupplierList() {
  supplierAPI.getSupplierListAll().then((data: any) => {
    supplierList.value = data;
  });
}

/** 采购员列表查询 */
function getBuyerList() {
  PurchasePersonnelAPI.getPurchaerPersonlList().then((data: any) => {
    buyerList.value = data;
  });
}

/** 查看 */
function handleView(row: any) {
  drawerType.value = 'details';
  rowData.value = row;
  dialog.title = t("shippingReceipt.title.detailsTitle");
  dialogVisible.value = true
}

/** 确认 */
function handleConfirm(row: any) {
  drawerType.value = 'confirm'
  rowData.value = row;
  dialog.title = t("shippingReceipt.title.confirmTitle");
  dialogVisible.value = true
}

onMounted(() => {
  handleQuery();
  handleQueryPurchase();
  getSupplierList();
  getBuyerList();
});
</script>
<style scoped lang="scss">
.purchase-status {
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  font-style: normal;
  width: 100px;

  .purchase-status-color1 {
    background: rgba(255, 156, 0, 0.08);
    border: 1px solid rgba(255, 156, 0, 0.2);
    color: #FF9C00;
  }

  .purchase-status-color2 {
    background: rgba(64, 158, 255, 0.08);
    border: 1px solid rgba(64, 158, 255, 0.2);
    color: #409EFF;
  }

  .purchase-status-color3 {
    background: rgba(41, 182, 16, 0.08);
    border: 1px solid rgba(41, 182, 16, 0.2);
    color: #29B610;
  }

  .purchase-status-color0 {
    background: rgba(144, 151, 158, 0.1);
    border: 1px solid rgba(200, 201, 204, 0.2);
    color: #90979E;
  }
}
</style>
