<template>
  <div class="app-container">
    <div class="reconcile" v-loading="loading">
      <!-- <div class="page-header">
        <el-button link @click="handleClose">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          {{ t("purchaseReceiveables.detail.billNumber") }}：{{
            detailData.payableBillCode
          }}
        </el-button>
        <el-tag type="success">
          {{
            detailData.payableBillStatus === 1 ? $t('purchasePayable.label.due') :
              $t('purchasePayable.label.closedAccount')
          }}
        </el-tag>
      </div> -->
      <div class="card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="handleClose"
        />
        <span class="code" @click="handleClose">
          {{ t("purchasePayable.label.billNumber") }}：{{
            detailData.payableBillCode
          }}
        </span>
        <!-- 收款状态：1->待收款，2->已结清 -->
        <span
          class="contract status ml-10px"
          :class="
            detailData.payableBillStatus === 1 ? 'unwokring' : 'executing'
          "
        >
          {{
            detailData.payableBillStatus === 1
              ? $t("purchasePayable.label.due")
              : $t("purchasePayable.label.closedAccount")
          }}
        </span>
      </div>
      <div class="page-content">
        <div class="base-info">
          <div class="card-title mb-20px">
            {{ $t("purchasePayable.label.basicInformation") }}
          </div>
          <div class="base-info-desc">
            <div class="item-list">
              <div class="item">
                <span>{{ $t("purchasePayable.label.billCode") }}:</span>
                {{ detailData.payableBillCode }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.closeTime") }}:</span>
                <span v-if="detailData.closeTime" class="time_style">
                  {{ parseDateTime(detailData.closeTime, "dateTime") }}
                </span>
                <span v-else>-</span>
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.createUserName") }}:</span>
                {{ detailData.createUserName }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.billingStatus") }}:</span>
                {{
                  detailData.payableBillStatus === 1
                    ? $t("purchasePayable.label.due")
                    : $t("purchasePayable.label.closedAccount")
                }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.purchaseCode") }}:</span>
                {{ detailData.purchaseCode }}
              </div>
            </div>
          </div>
        </div>
        <div class="product-info">
          <div class="card-title mb-20px">
            {{ $t("purchasePayable.label.productInfo") }}
          </div>
          <el-table
            :data="detailData.payableBillItemList"
            highlight-current-row
          >
            <el-table-column
              type="index"
              :label="$t('purchasePayable.label.sort')"
              width="60"
            />
            <el-table-column
              :label="$t('purchasePayable.label.productImage')"
              prop="productImage"
              min-width="100"
            >
              <template #default="scope">
                <div class="product-div">
                  <div class="picture">
                    <img :src="scope.row.productImage" alt="" />
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchasePayable.label.productName')"
              prop="productName"
              min-width="100"
            />
            <el-table-column
              :label="$t('purchasePayable.label.productUnit')"
              prop="productUnit"
              min-width="100"
            />
            <el-table-column
              :label="$t('purchasePayable.label.productQuantity')"
              prop="productQuantity"
              min-width="100"
            />
            <el-table-column
              :label="$t('purchasePayable.label.productAmount')"
              prop="productAmount"
            >
              <template #default="scope">
                {{ scope.row.currencyCode === "USD" ? "$" : "￥"
                }}{{ scope.row.productAmount }}
              </template>
            </el-table-column>
          </el-table>
          <div class="mb20px mt20px total-row">
            <span>{{ t("purchasePayable.label.total") }}:</span>
            <span class="ml10px">{{ t("purchasePayable.label.amount") }}:</span>
            <span class="total-amount">
              {{ detailData.currencyCode === "USD" ? "$" : "￥"
              }}{{ detailData.amount }}
            </span>
            <span class="ml10px">
              {{ t("purchasePayable.label.changeAmount") }}:
            </span>
            <span class="total-amount">
              {{ detailData.currencyCode === "USD" ? "$" : "￥"
              }}{{ detailData.changeAmount }}
            </span>
          </div>
        </div>
        <div class="settle-account">
          <div class="card-title mb-20px">
            {{ $t("purchasePayable.label.settleAccount") }}
          </div>
          <el-table
            :data="detailData.payableBillPaymentList"
            highlight-current-row
          >
            <el-table-column
              :label="$t('purchasePayable.label.receiverUserName')"
              prop="receiverUserName"
              min-width="160"
            />
            <el-table-column
              :label="$t('purchasePayable.label.closeType')"
              prop="transactionType"
              min-width="100"
            >
              <template #default="scope">
                {{
                  scope.row.transactionType === 1
                    ? $t("purchasePayable.button.pay")
                    : $t("purchasePayable.button.strikeBalance")
                }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchasePayable.label.payType')"
              prop="paymentMethod"
              min-width="100"
            >
              <template #default="scope">
                {{ getPayTypeName(scope.row.paymentMethod) }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchasePayable.label.paymentAmount')"
              prop="paymentAmount"
              min-width="100"
            >
              <template #default="scope">
                {{ scope.row.currencyCode === "USD" ? "$" : "￥"
                }}{{ scope.row.paymentAmount }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchasePayable.label.exchangeRate')"
              prop="exchangeRate"
              min-width="60"
            />
            <!-- 付款凭证 -->
            <el-table-column
              :label="$t('purchasePayable.label.paymentVoucher')"
              prop="paymentVoucher"
              min-width="180"
              show-overflow-tooltip
            >
              <template #default="scope">
                <UploadMultiple
                  :showTip="false"
                  :showUploadBtn="false"
                  ref="detailPicsRef"
                  v-model="scope.row.paymentVoucher"
                  listType="text"
                  class="modify-multipleUpload"
                  name="detailPic"
                  actionType="preview"
                />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchasePayable.label.paymentTime')"
              prop="paymentTime"
              show-overflow-tooltip
              min-width="160"
            >
              <template #default="scope">
                <span>
                  {{ parseDateTime(scope.row.paymentTime, "dateTime") }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchasePayable.label.uploadVoucherTime')"
              prop="createTime"
              show-overflow-tooltip
              min-width="160"
            >
              <template #default="scope">
                <span>
                  {{ parseDateTime(scope.row.createTime, "dateTime") }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchasePayable.label.remark')"
              prop="paymentRemark"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              :label="$t('purchasePayable.label.operationUserName')"
              prop="operatorUserName"
              min-width="120"
            />
          </el-table>
          <div class="mb20px mt20px total-row">
            <span>{{ t("purchasePayable.label.total") }}:</span>
            <span class="ml10px">{{ t("purchasePayable.label.amount") }}:</span>
            <span class="total-amount">
              {{ detailData.currencyCode === "USD" ? "$" : "￥"
              }}{{ detailData.amount }}
            </span>
            <span class="ml10px">
              {{ t("purchasePayable.label.paidAmount") }}:
            </span>
            <span class="total-amount">
              {{ detailData.currencyCode === "USD" ? "$" : "￥"
              }}{{ detailData.paidAmount }}
            </span>
          </div>
        </div>
        <div class="operation-record">
          <div class="card-title mb-20px">
            {{ $t("purchasePayable.label.operationRecord") }}
          </div>
          <el-table :data="detailData.operationLogList" highlight-current-row>
            <el-table-column
              :label="$t('purchasePayable.label.operationTime')"
              prop="operationTime"
              show-overflow-tooltip
              min-width="120"
            >
              <template #default="scope">
                <span>
                  {{ parseDateTime(scope.row.operationTime, "dateTime") }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchasePayable.label.operationType')"
              prop="operationTypeName"
              min-width="100"
            />
            <el-table-column
              :label="$t('purchasePayable.label.operationUserName')"
              prop="operationName"
              min-width="100"
            />
          </el-table>
        </div>
        <div class="page-footer">
          <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Reconcile",
  inheritAttrs: false,
});

import { useRoute, useRouter } from "vue-router";
import PurchasePayableAPI, {
  PurchasePayableInfo,
} from "@/modules/pms/api/purchasePayable";
import { parseDateTime } from "@/core/utils/index.js";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const { t } = useI18n();
const loading = ref(false);

const detailData = ref<PurchasePayableInfo>({});
const id = route.query.id;

function handleClose() {
  router.go(-1);
}

/** 查询采购单详情 */
function queryPurchaseReconcileDetail() {
  loading.value = true;
  PurchasePayableAPI.detailPayableBill(id)
    .then((data) => {
      detailData.value = data;
      if (
        detailData.value.payableBillPaymentList &&
        detailData.value.payableBillPaymentList.length > 0
      ) {
        detailData.value.payableBillPaymentList =
          detailData.value.payableBillPaymentList.map((item: any) => {
            if (
              item &&
              item.paymentVoucher &&
              typeof item.paymentVoucher === "string"
            ) {
              return {
                ...item,
                paymentVoucher: JSON.parse(item.paymentVoucher),
              };
            }
            return item;
          });
      }

      loading.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}
function getPayTypeName(paymentMethod: any) {
  switch (paymentMethod) {
    case 1:
      return "转账";
      break;
    case 2:
      return "现金";
      break;
    case 3:
      return "线下支付宝";
      break;
    default:
      return "线下微信";
      break;
  }
}

onMounted(() => {
  queryPurchaseReconcileDetail();
});
</script>
<style scoped lang="scss">
.reconcile {
  background: #ffffff;
  border-radius: 4px;
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .card-header {
    padding: 0px 10px 20px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;
    .code {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    .status {
      /* font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal; */
    }
  }

  .page-content {
    padding: 5px;

    .base-info {
      .base-info-desc {
        .item-list {
          display: flex;
          flex-wrap: wrap;
          align-items: center;

          .item {
            width: 25%;
            padding: 10px 0;
            color: #151719;
            font-size: 14px;

            span {
              display: inline-block;
              color: #90979e;
            }
          }
        }
      }
    }

    .total-row {
      margin-top: 20px;
      margin-bottom: 40px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585f;
      line-height: 20px;
      text-align: left;
      font-style: normal;

      .total-amount {
        color: #c00c1d;
      }
    }

    .form-item-container {
      display: flex;
      flex-wrap: wrap;
    }

    .form-item-container .el-form-item {
      width: 25%;
    }

    .product-div {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .picture {
        img {
          width: 80px;
          height: 80px;
        }
      }
    }
  }

  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 9px 20px;
    border-top: 1px solid #e5e7f3;
  }

  :deep(.el-timeline-item__timestamp) {
    font-size: 16px;
    color: #151719;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
  }

  :deep(.el-card) {
    width: 100%;
    background: linear-gradient(180deg, #fafafa 0%, #ffffff 100%);
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-sizing: border-box;
  }

  .fileName_style {
    align-items: center;
  }
}
.time_style {
  color: #151719 !important;
  padding-left: 4px;
}
</style>
