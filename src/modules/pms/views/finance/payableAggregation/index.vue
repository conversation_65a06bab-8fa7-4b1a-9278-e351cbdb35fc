<template>
  <div class="app-container">
    <div class="container-wrapper">
      <el-card
        shadow="never"
        class="table-container"
      >
        <template #header>
          <el-tabs
            :model-value="queryParams.payableAccountType"
            class="menu-list"
            @tab-change="handlePayableAccount"
          >
            <el-tab-pane
              v-for="(item, index) in payableAccountTypeOption"
              :key="index"
              :label="item.label"
              :name="item.value"
            ></el-tab-pane>
          </el-tabs>

          <div class="tabs-search">
            <el-form
              ref="queryFormRef"
              :model="queryParams"
              :inline="true"
              label-width="96px"
            >
              <template v-if="queryParams.payableAccountType === 1">
                <el-form-item
                  prop="supplierName"
                  :label="$t('payableAggregation.label.supplierName')"
                >
                  <el-input
                    v-model="queryParams.supplierName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
              </template>
              <template v-if="queryParams.payableAccountType === 2">
                <el-form-item
                  prop="purchaseUserName"
                  :label="$t('payableAggregation.label.purchaseUserName')"
                >
                  <el-input
                    v-model="queryParams.purchaseUserName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
              </template>
              <el-form-item>
                <el-button
                  v-hasPerm="['pms:finance:payableAggregation:search']"
                  type="primary"
                  @click="handleQuery('search')"
                >
                  {{ $t("common.search") }}
                </el-button>
                <el-button
                  v-hasPerm="['pms:finance:payableAggregation:reset']"
                  @click="handleResetQuery"
                >
                  {{ $t("common.reset") }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </template>

        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="tableData"
          highlight-current-row
          stripe
          :summary-method="getSummaries"
          show-summary
          @sort-change="handleSortChange"
        >
          <template #empty>
            <Empty />
          </template>
          <template
            v-for="(item, index) in tableHeadData()"
            :key="index"
          >
            <el-table-column
              v-if="item.prop"
              :class-name="item.className"
              :label-class-name="item.labelClassName"
              :type="item.type"
              :prop="item.prop"
              :key="item.prop"
              :label="item.label"
              :width="item.width"
              :min-width="item.minWidth"
              :fixed="item.fixed"
              :align="item.align"
              :header-align="item.headerAlign"
              :show-overflow-tooltip="item.showOverflowTooltip"
              :formatter="item.formatter"
              :sortable="item.sortable"
            >
              <template
                #default="scope"
                v-if="item.prop === 'operator'"
              >
                <el-button
                  v-hasPerm="['pms:finance:payableAggregation:detail']"
                  type="primary"
                  link
                  @click="handleDetail(scope.row)"
                >
                  {{ $t("payableAggregation.button.actionView") }}
                </el-button>
              </template>
            </el-table-column>
          </template>
        </el-table>

        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handleQuery"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "PayableAggregation",
  inheritAttrs: false,
});
import { parseDateTime, isEmpty, formatPrice } from "@/core/utils";

import { useUserStore } from "@/core/store";

import API from "@/modules/pms/api/payableAggregation";

import { useNavigation } from "@/core/composables/useNavigation";

const { refreshAndNavigate } = useNavigation();

const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();

const initQueryParams = () => ({
  page: 1,
  limit: 20,
  payableAccountType: 1,
  supplierName: undefined,
  purchaseUserName: undefined,
  sortType: undefined,
  sortField: undefined,
});
const state = reactive({
  loading: false,
  queryFormRef: null,
  queryParams: initQueryParams(),
  total: 0,
  tableData: [],
  payableAccountTypeOption: [
    {
      label: t("payableAggregation.label.payableAccountTypeOption[1]"),
      value: 1,
    },
    {
      label: t("payableAggregation.label.payableAccountTypeOption[2]"),
      value: 2,
    },
  ],
  summaryData: "",
  tableRef: null,
}) as any;

const {
  loading,
  queryFormRef,
  queryParams,
  total,
  tableData,
  payableAccountTypeOption,
  summaryData,
  tableRef,
} = toRefs(state);

/**
 * Tabs切换
 */

function handlePayableAccount(value: any) {
  queryParams.value = {
    ...initQueryParams(),
    payableAccountType: value,
  };
  tableRef.value?.clearSort();
  handleQuery();
}

/**
 * 表头数据
 */

function tableHeadData() {
  let symbol;
  if (
    tableData.value?.length === 0 ||
    tableData.value[0]?.currencyCode === "CNY"
  ) {
    symbol = "￥";
  } else {
    symbol = "$";
  }

  let tableHead: any = [
    {
      prop: "payableAmount",
      label: t("payableAggregation.label.payableAmount") + `(${symbol})`,
      showOverflowTooltip: true,
      minWidth: 180,
      sortable: "custom",
      formatter: formatterAmount,
      align: "right",
    },
    {
      prop: "actualPayAmount",
      label: t("payableAggregation.label.actualPayAmount") + `(${symbol})`,
      showOverflowTooltip: true,
      minWidth: 180,
      sortable: "custom",
      formatter: formatterAmount,
      align: "right",
    },
    {
      prop: "remainingPayAmount",
      label: t("payableAggregation.label.remainingPayAmount") + `(${symbol})`,
      showOverflowTooltip: true,
      minWidth: 180,
      sortable: "custom",
      formatter: formatterAmount,
      align: "right",
    },
    {
      prop: "operator",
      label: t("payableAggregation.label.operator"),
      fixed: "right",
      width: 160,
    },
  ];
  switch (queryParams.value.payableAccountType) {
    case 1:
      tableHead = [
        ...[
          {
            prop: "supplierName",
            label: t("payableAggregation.label.supplierName"),
            showOverflowTooltip: true,
            minWidth: 180,
          },
          {
            prop: "supplierCode",
            label: t("payableAggregation.label.supplierCode"),
            showOverflowTooltip: true,
            minWidth: 180,
          },
        ],
        ...tableHead,
      ];
      break;
    case 2:
      tableHead = [
        ...[
          {
            prop: "purchaseUserName",
            label: t("payableAggregation.label.purchaseUserName"),
            showOverflowTooltip: true,
            minWidth: 180,
          },
        ],
        ...tableHead,
      ];
      break;
    default:
      break;
  }
  return tableHead;
}

/**
 * 搜索
 * @param type
 */
function handleQuery(type?: string) {
  if (type === "search") {
    queryParams.value.sortType = undefined;
    queryParams.value.sortField = undefined;
    tableRef.value?.clearSort();
  }
  loading.value = true;
  let params = {
    ...queryParams.value,
  };

  API.getPageList(params)
    .then((res: any) => {
      const {
        records,
        pages,
        totalPayableAmount,
        totalActualPayAmount,
        totalRemainingPayAmount,
      } = res;
      tableData.value = records || [];
      total.value = parseInt(res.total);
      summaryData.value = {
        totalPayableAmount,
        totalActualPayAmount,
        totalRemainingPayAmount,
      };
    })
    .finally(() => {
      loading.value = false;
    });
}

/**
 * 重置
 */
function handleResetQuery() {
  queryParams.value = {
    ...initQueryParams(),
    payableAccountType: queryParams.value.payableAccountType,
  };
  tableRef.value?.clearSort();
  handleQuery();
}

/**
 * 排序
 */

function handleSortChange({ column, prop, order }: any) {
  // console.log(column, prop, order);
  let sortType;
  if (order === "descending") {
    sortType = 1;
  } else if (order === "ascending") {
    sortType = 0;
  } else {
    sortType = undefined;
  }
  queryParams.value.sortType = sortType;
  queryParams.value.sortField = prop;

  handleQuery();
}

/**
 * 时间戳格式化
 * @param row
 * @param column
 * @param cellValue
 */
function formatterTimestamp(row: any, column: any, cellValue: any) {
  if (isEmpty(cellValue)) {
    return "";
  }
  return parseDateTime(cellValue, "dateTime");
}

/**
 * 金额格式化
 */

function formatterAmount(row: any, column: any, cellValue: any) {
  if (isEmpty(cellValue)) {
    return "";
  }
  let val: any = formatPrice(cellValue);
  if (column.property === "payableAmount") {
    val = h("div", { class: "highlight" }, [val]);
  }

  return val;
}

/**
 * 合计
 * @param param
 */
function getSummaries(param: any) {
  const { columns, data } = param;
  const sums: any = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = h("div", { class: "summary" }, [
        t("payableAggregation.label.summation"),
      ]);
      return;
    } else {
      sums[index] = h("div", { class: "summary" }, []);
    }
  });

  let offset = 0;
  if (queryParams.value.payableAccountType === 2) {
    offset = 1;
  }
  sums[2 - offset] = h("div", { class: "highlight" }, [
    `${formatPrice(summaryData.value.totalPayableAmount)}`,
  ]);
  sums[3 - offset] = h("div", { class: "summary" }, [
    `${formatPrice(summaryData.value.totalActualPayAmount)}`,
  ]);
  sums[4 - offset] = h("div", { class: "summary" }, [
    `${formatPrice(summaryData.value.totalRemainingPayAmount)}`,
  ]);
  return sums;
}

/**
 * 详情
 * @param row
 */
function handleDetail(row: any) {
  refreshAndNavigate({
    path: "/pms/finance/payableAggregationDetails",
    query: {
      id: row.id,
      payableAccountType: queryParams.value.payableAccountType,
    },
  });
}

onActivated(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
.container-wrapper {
  .tabs-search {
    padding-top: 10px;
  }

  :deep(.highlight) {
    color: #c00c1d;
  }

  :deep(.summary) {
    font-weight: 500;
    font-size: 16px;
    color: #52585f;
  }
}
</style>
