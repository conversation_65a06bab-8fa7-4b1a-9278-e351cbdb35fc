<template>
  <div class="app-container">
    <div class="purchaseReturn">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="96px">
            <el-form-item prop="dateRange">
              <el-select
                v-model="queryParams.queryTimeType"
                :placeholder="$t('common.placeholder.selectTips')"
                class="!w-[180px] ml5"
              >
                <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
              <el-date-picker
                :editable="false"
                class="!w-[370px]"
                v-model="queryParams.dateRange"
                type="datetimerange"
                :range-separator="$t('purchaseReturn.label.to')"
                :start-placeholder="$t('purchaseReturn.label.startTime')"
                :end-placeholder="$t('purchaseReturn.label.endTime')"
                :default-time="defaultTime"
                :placeholder="$t('common.placeholder.selectTips')"
              />
              <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handelChangeDateRange(1)">{{$t('purchaseReturn.label.today')}}</span>
              <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handelChangeDateRange(2)">{{$t('purchaseReturn.label.yesterday')}}</span>
              <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)" @click="handelChangeDateRange(3)">{{$t('purchaseReturn.label.weekday')}}</span>
            </el-form-item>
            <el-form-item :label="$t('purchaseReturn.label.orderType')" prop="orderType">
              <el-select
                v-model="queryParams.orderType"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                class="!w-[256px]"
              >
                <el-option v-for="item in orderTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="orderCode" :label="$t('purchaseReturn.label.orderCode')">
              <el-input
                v-model="queryParams.orderCode"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item prop="returnOrderCode" :label="$t('purchaseReturn.label.returnCode')">
              <el-input
                v-model="queryParams.returnOrderCode"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item :label="$t('purchaseReturn.label.supplierName')" prop="supplierId">
              <el-select
                v-model="queryParams.supplierId"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                filterable
                class="!w-[256px]"
              >
                <el-option v-for="item in supplierList" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('purchaseReturn.label.purchaseUserName')" prop="purchaseUserId">
              <el-select
                v-model="queryParams.purchaseUserId"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                filterable
                class="!w-[256px]"
              >
                <el-option v-for="item in purchasePersonnelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('purchaseReturn.label.synStatus')" prop="syncStatus">
              <el-select
                v-model="queryParams.syncStatus"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                filterable
                class="!w-[256px]"
              >
                <el-option v-for="(item,index) in synStatusOption" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-hasPerm="['pms:purchase:purchaseReturn:sync']" type="primary" plain @click="handleSync"  :disabled="multipleSelection.length===0">
                {{$t('purchaseReturn.button.manualSync')}}
              </el-button>
              <el-button v-hasPerm="['pms:purchase:purchaseReturn:search']" type="primary" @click="handleQuery">
                {{$t('common.search')}}
              </el-button>
              <el-button v-hasPerm="['pms:purchase:purchaseReturn:reset']"  @click="handleResetQuery">
                {{$t('common.reset')}}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <el-card class="content-card">
        <div class="action-bar">
          <el-button v-hasPerm="['pms:purchase:purchaseReturn:add']" type="primary" @click="addPurchaseReturn()">{{$t('purchaseReturn.button.add')}}</el-button>
        </div>
        <el-table v-loading="loading" :data="purchaseReturnList" highlight-current-row stripe @selection-change="handleSelectionChange">
          <template #empty>
            <Empty/>
          </template>
          <el-table-column type="selection" fixed="left" width="60" align="center" />
          <el-table-column :label="$t('purchaseReturn.label.returnCode')" prop="returnOrderCode" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.orderCode')" prop="orderCode" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.orderType')" prop="orderType" width="120" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.orderType==1">{{t('purchaseReturn.orderTypeList.suppliersDirectSupply')}}</span>
              <span v-if="scope.row.orderType==2">{{t('purchaseReturn.orderTypeList.marketDirectSupply')}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.purchaseUserName')" prop="purchaseUserName" min-width="120" show-overflow-tooltip/>
          <el-table-column :label="$t('purchaseReturn.label.supplierName')" prop="supplierName" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.totalReturnAmount')" prop="returnAmount" min-width="140" show-overflow-tooltip align="right">
            <template #default="scope">
              <span v-if="scope.row.returnAmount" style="color:#FF4D4F">{{!scope.row.currencyCode || scope.row.currencyCode == 'CNY' ? '￥' : '$'}}{{scope.row.returnAmount}}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.returnCreateTime')" prop="createTime" width="180" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.createTime ? parseDateTime(scope.row.createTime, "dateTime") : '-'}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.approveTime')" prop="approveTime" width="180" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.approveTime ? parseDateTime(scope.row.approveTime, "dateTime") : '-'}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.createUserName')" prop="createUserName" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.approveUserName')" prop="approveUserName" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.approveStatus')" width="120"  show-overflow-tooltip fixed="right">
            <template #default="scope">
              <div class="purchase">
                <div class="purchase-status purchase-status-color1" v-if="scope.row.approveStatus==1">{{t('purchaseReturn.approveStatusOption[1]')}}</div>
                <div class="purchase-status purchase-status-color3" v-if="scope.row.approveStatus==2">{{t('purchaseReturn.approveStatusOption[2]')}}</div>
                <div class="purchase-status purchase-status-color5" v-if="scope.row.approveStatus==3">{{t('purchaseReturn.approveStatusOption[3]')}}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('purchaseReturn.label.synStatus')" width="120"  show-overflow-tooltip fixed="right">
            <template #default="scope">
              <div class="purchase">
                <div class="purchase-status purchase-status-color3" v-if="scope.row.syncStatus==1">{{t('purchaseReturn.synStatusOption.success')}}</div>
                <div class="purchase-status purchase-status-color5" v-if="scope.row.syncStatus==2">{{t('purchaseReturn.synStatusOption.fail')}}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="$t('common.handle')" width="200">
            <template #default="scope">
              <!-- 查看  -->
              <el-button v-hasPerm="['pms:purchase:purchaseReturn:select']" type="primary" link @click="toDetail(scope.row.returnOrderCode)">{{$t('common.select')}}</el-button>
              <!-- 编辑  -->
              <el-button v-hasPerm="['pms:purchase:purchaseReturn:update']" v-if="scope.row.approveStatus == 3" type="primary" link @click="toEdit(scope.row.returnOrderCode)">{{$t('common.edit')}}</el-button>
              <!-- 删除  -->
              <el-button v-hasPerm="['pms:purchase:purchaseReturn:delete']" v-if="scope.row.approveStatus == 3" type="danger" link @click="handleDelete(scope.row.returnOrderCode)">{{$t('common.delete')}}</el-button>
              <!-- 审核 -->
              <el-button v-hasPerm="['pms:purchase:purchaseReturn:approve']" v-if="scope.row.approveStatus == 1 && scope.row.approveUserId == userId" type="primary" link @click="toApprove(scope.row.returnOrderCode)">{{$t('purchaseReturn.label.approve')}}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.limit"
            @pagination="handleQuery"
          />
        </div>
      </el-card>
    </div>

  </div>
</template>

<script setup lang="ts">

  defineOptions({
    name: "PurchaseReturn",
    inheritAttrs: false,
  });

  import supplierAPI from "@/modules/pms/api/supplier";
  import PurchasePersonnelAPI from "@/modules/pms/api/purchasePersonnel";
  import PurchaseOrderAPI, {
    PurchaseOrderPageVO,
    PurchaseOrderPageQuery,
    PurchaseOrderFrom
  } from "@/modules/pms/api/purchaseOrder";
  import PurchaseReturnApi, {queryPageListReq,queryPageListResp} from "@/modules/pms/api/purchaseReturn";
  import {IObject} from "@/core/components/CURD/types";
  import {useRouter} from "vue-router";
  import {convertToTimestamp, changeDateRange, parseDateTime} from "@/core/utils/index.js";
  import moment from 'moment';
  import { useNavigation } from "@/core/composables/useNavigation";
  const { refreshAndNavigate } = useNavigation();
  const router = useRouter();
  const { t } = useI18n();
  const userId = localStorage.getItem("userId");
  const queryFormRef = ref(null);
  const loading = ref(false);
  const total = ref(0);
  const multipleSelection = ref([]);
  const purchasePersonnelList = ref([])
  const supplierList = ref([])
  const defaultTime: [Date, Date] = [
    new Date(2000, 1, 1, 0, 0, 0),
    new Date(2000, 2, 1, 23, 59, 59),
  ];
  const dateTypeList = ref([
    {
      key: 1,
      value: t('purchaseReturn.dateTypeList.returnCreateDate')
    },
    {
      key: 2,
      value:t('purchaseReturn.dateTypeList.auditDate')
    }
  ])
  const orderTypeList = ref([
    {
      key: 2,
      value: t('purchaseReturn.orderTypeList.marketDirectSupply')
    },
    {
      key: 1,
      value:t('purchaseReturn.orderTypeList.suppliersDirectSupply')
    }
  ])
  const synStatusOption  = ref([
    {
      value: 1,
      label: t('purchaseReturn.synStatusOption.success')
    },
    {
      value: 0,
      label:t('purchaseReturn.synStatusOption.fail')
    },
  ])
  const queryParams = reactive<queryPageListReq>({
    queryTimeType:1,
    page: 1,
    limit: 20,
    dateRange: [],
  });

  const purchaseReturnList = ref<queryPageListResp[]>();

  /** 查询采购员列表 */
  function getPerchasePersonnelList() {
    PurchasePersonnelAPI.getPerchasePersonnelList()
      .then((data) => {
        purchasePersonnelList.value = data;
      })
  }

  /** 查询供应商列表 */
  function getSupplierList() {
    supplierAPI.getSupplierListAll()
      .then((data) => {
        supplierList.value = data;
      })
  }

  /** 时间转换 */
  function handelChangeDateRange(val) {
    queryParams.dateRange = changeDateRange(val);
  }

  /** 查询 */
  function handleQuery() {
    loading.value = true;
    let params = {
      ...queryParams
    }
    if(queryParams.dateRange && queryParams.dateRange.length > 0){
      params.startTime = new Date(queryParams.dateRange[0]).getTime()
      params.endTime = new Date(queryParams.dateRange[1]).getTime()
    }
    delete params.dateRange
    PurchaseReturnApi.queryPageList(params)
      .then((data) => {
        purchaseReturnList.value = data.records;
        total.value = parseInt(data.total);
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 重置查询 */
  function handleResetQuery() {
    queryFormRef.value.resetFields();
    queryParams.queryTimeType=1
    queryParams.page = 1;
    queryParams.limit = 20;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any[]) {
    multipleSelection.value = selection;
  }

  /** 新增 */
  function addPurchaseReturn() {
      refreshAndNavigate({
      path: "/pms/purchase/purchaseReturnAddOrEdit",
      query: {returnOrderCode:null,type:'add',title:t('purchaseReturn.button.addPurchaseReturn'),fromPage:1}
    });
  }

  /** 编辑 */
  function toEdit(code?:string){
      refreshAndNavigate({
      path: "/pms/purchase/purchaseReturnAddOrEdit",
      query: {returnOrderCode:code,type:'edit',title:t('purchaseReturn.button.editPurchaseReturn'),fromPage:1}
    });
  }

  /** 审核 */
  function toApprove(code?:string) {
    router.push({
      path: "/pms/purchase/purchaseReturnApprove",
      query: {returnOrderCode:code}
    });
  }

  /** 查看 */
  function toDetail(code?:string){
    router.push({
      path: "/pms/purchase/purchaseReturnDetail",
      query: {returnOrderCode:code}
    });
  }

  /**
   * 手动同步
   */
  function handleSync() {
    const multiple=multipleSelection.value
    if(multiple.length>20){
      return ElMessage.error(t('purchaseReturn.message.maxSelect'))
    }

    let message = ''; // 提示消息
    let validOrderCodes:any = []; // 符合条件的退货编码
    let validIds:any = []; // 符合条件的id

    multiple.forEach(item => {
      const { syncStatus, approveStatus, returnOrderCode, id } = item;
      if ( syncStatus!= 1 && approveStatus === 2) {
        validOrderCodes.push(returnOrderCode);
        validIds.push(id);
      }
    });

    //不符合条件的code
    const unqualifiedCodes =  multiple.map(item => item.returnOrderCode).filter(code => !validOrderCodes.includes(code));
    console.log('不符合条件的orderCode:',unqualifiedCodes)


    if (unqualifiedCodes.length>0){
      message = unqualifiedCodes.map(code => `${code}`).join('、');
      console.log(message)
      return ElMessage.error(t('purchaseReturn.message.inconformitySelect',{message:message}))
    }

    if (validIds.length==0){
      return
    }

    PurchaseReturnApi.sync(validIds).then(res => {
      ElMessage.success(t('purchaseReturn.message.syncSuccess'))
      handleQuery()
    })
  }

  /**
   * 删除
   */
  function handleDelete(code){
    ElMessageBox.confirm(t("purchaseReturn.message.deleteWarning"), t("common.delete"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }).then(() => {
      PurchaseReturnApi.delete({returnOrderCode:code}).then(res=>{
        ElMessage.success(t('purchaseReturn.message.deleteSuccess'))
        handleQuery()
      })
    }, () => {
        ElMessage.info(t("purchaseReturn.message.cancelTip"));
    });
  }

  onActivated(() => {
    getSupplierList();
    getPerchasePersonnelList();
    handleQuery();
  });
</script>

<style lang="scss" scoped>
  .purchaseReturn{
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }

    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .action-bar {
        margin-bottom: 12px;
        flex-shrink: 0;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
</style>


