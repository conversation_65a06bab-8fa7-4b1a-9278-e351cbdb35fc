<template>
  <div class="app-container">
    <div class="purchaseReturnDetail" v-loading="loading">
      <div class="page-title">
        <div class="purchase-title">
          <div @click="handleClose()" class="cursor-pointer mr8px">
            <el-icon>
              <Back />
            </el-icon>
          </div>
          <div>
            {{ t("purchaseReturn.label.returnCode") }}：{{ form.returnOrderCode }}
          </div>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" label-width="96px" label-position="right">
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">{{ $t("purchaseReturn.label.basicInformation") }}</div>
          </div>
          <div class="grad-row">
            <el-row>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.orderCode')" prop="orderCode">{{ form.orderCode || "-" }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.warehouse')" prop="warehouseName">{{ form.warehouseName || "-" }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.purchaseUserName')">{{ form.purchaseUserName || '-' }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.supplierName')">{{ form.supplierName || '-' }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.orderType')">
                  <span v-if="form.orderType == 1">
                    {{ t("purchaseOrder.orderTypeList.suppliersDirectSupply") }}
                  </span>
                  <span v-if="form.orderType == 2">
                    {{ t("purchaseOrder.orderTypeList.marketDirectSupply") }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.returnMethod')">{{form.deliveryName || "-"}}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.createTime')">{{ form.createTime ? parseDateTime(form.createTime, "dateTime") : '-' }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.createUserName')">{{ form.createUserName }}</el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.planDeliveryTime')">{{ form.planDeliveryTime ? parseDateTime(form.planDeliveryTime, "dateTime") : '-' }}</el-form-item>
              </el-col>
              <div class="purchase audit-status">
                <div
                  class="purchase-status purchase-status-color1"
                  v-if="form.approveStatus == 1"
                >
                  {{ t("purchaseReturn.approveStatusOption[1]") }}
                </div>
                <div
                  class="purchase-status purchase-status-color3"
                  v-if="form.approveStatus == 2"
                >
                  {{ t("purchaseReturn.approveStatusOption[2]") }}
                </div>
                <div
                  class="purchase-status purchase-status-color5"
                  v-if="form.approveStatus == 3"
                >
                  {{ t("purchaseReturn.approveStatusOption[3]") }}
                </div>
              </div>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">{{ $t("purchaseReturn.label.productListInformation") }}</div>
          </div>
          <div>
            <el-table :data="form.detailList"  highlight-current-row stripe>
              <el-table-column type="index" :label="$t('common.sort')" width="60" align="center"/>
              <el-table-column :label="$t('purchaseReturn.label.productName')" prop="productName" show-overflow-tooltip/>
              <el-table-column :label="$t('purchaseReturn.label.unitName')" prop="unitName" show-overflow-tooltip/>
              <el-table-column :label="$t('purchaseReturn.label.receiveCount')" prop="receivedCount" show-overflow-tooltip></el-table-column>
              <el-table-column :label="$t('purchaseReturn.label.returnCount')" prop="returnCount" show-overflow-tooltip></el-table-column>
              <el-table-column :label="$t('purchaseReturn.label.returnPrice')" prop="returnPrice" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.currencyCode == 'CNY'">￥{{scope.row.returnPrice}}</span>
                  <span v-else>${{scope.row.returnPrice}}</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('purchaseReturn.label.returnAmount')" prop="returnAmount" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.currencyCode == 'CNY'">￥{{scope.row.returnAmount}}</span>
                  <span v-else>${{scope.row.returnAmount}}</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('purchaseReturn.label.remark')" prop="remark" show-overflow-tooltip></el-table-column>
            </el-table>
            <div  class="table-title">
              <div>
                <span>{{t('purchaseReturn.label.total')}}：</span>
              </div>
              <div>
                <span class="mr16px">{{t('purchaseReturn.label.returnCount')}}：<span  style="font-size: 18px; color: #C00C1D ">{{form.totalReturnCount}}</span></span>
                <span>{{t('purchaseReturn.label.expectedReturnAmount')}}：
                        <span  style="font-size: 18px; color: #C00C1D ">
                          <span v-if="form.currencyCode == 'CNY'">￥</span>
                          <span v-else>$</span>
                          {{form.totalReturnAmount}}
                        </span>
                </span>
              </div>
            </div>
          </div>
          <div class="total-amount">
            <el-row class="flex-center-start">
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.totalReturnAmount')" prop="totalReturnAmount">
                  <span v-if="form.currencyCode == 'CNY'">￥</span>
                  <span v-else>$</span>
                  {{ form.returnAmount }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("purchaseReturn.label.otherInformation") }}
            </div>
          </div>
          <div class="grad-row">
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('purchaseReturn.label.attachmentFiles')">
                  <UploadMultiple
                    :showTip="false"
                    :showUploadBtn="false"
                    ref="detailPicsRef"
                    v-model="form.attachmentFiles"
                    :tips="''"
                    :limit="10"
                    :formRef="formRef"
                    listType="text"
                    :fileType="filtType"
                    class="modify-multipleUpload"
                    name="detailPic"
                    actionType="preview"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('purchaseReturn.label.remark')">
                  <span v-if="form.remark" style="word-break: break-all">{{ form.remark }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-content">{{ $t("purchaseReturn.label.approve") }}</div>
          </div>
          <div class="grad-row">
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('purchaseReturn.label.approveUserName')">{{ form.approveUserName || "-" }}</el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-content">{{ $t("purchaseReturn.label.approveRemark") }}</div>
          </div>
          <div>
            <el-row>
              <el-col :span="24">
                <el-input
                  :rows="3"
                  type="textarea"
                  show-word-limit
                  v-model="form.approveRemark"
                  :placeholder="$t('common.placeholder.inputTips')"
                  maxlength="100"
                  clearable
                />
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">{{ $t("common.reback") }}</el-button>
        <el-button type="danger"  @click="handleSubmit(3)" :loading="submitLoading">{{ $t("purchaseReturn.button.refused") }}</el-button>
        <el-button type="primary" @click="handleSubmit(2)" :loading="submitLoading">{{ $t("purchaseReturn.button.agree") }}</el-button>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
  import { IObject } from "@/core/components/CURD/types";
  import { useRoute, useRouter } from "vue-router";
  import { useTagsViewStore } from "@/core/store";
  import PurchaseReturnApi, { detailForm } from "@/modules/pms/api/purchaseReturn";
  import { parseDateTime } from "@/core/utils/index.js";
  defineOptions({
    name: "PurchaseReturnApprove",
    inheritAttrs: false,
  });
  const route = useRoute();
  const router = useRouter();
  const tagsViewStore = useTagsViewStore();
  const { t } = useI18n();
  const fromRef = ref();
  const submitLoading = ref(false);
  const loading = ref(false);
  const returnOrderCode = route.query.returnOrderCode;
  const form = reactive<detailForm>({
    detailList: [],
  });

  async function handleClose() {
    await tagsViewStore.delView(route);
    router.go(-1);
  }

  /** 查询详情 */
  function queryDetail() {
    loading.value = true;
    let params = {
      returnOrderCode: returnOrderCode,
    };
    PurchaseReturnApi.detail(params).then((data) => {
      Object.assign(form, data);
    }).finally(() => {
      loading.value = false;
    });
  }

  /** 审核 */
  function handleSubmit(val){
    if(val == 3 && (!form.approveRemark || form.approveRemark == '' || form.approveRemark == null || form.approveRemark == undefined)){
      return  ElMessage.error(t('purchaseReturn.message.approveRemark'));
    }
    submitLoading.value = true
    let params = {
      returnOrderCode:returnOrderCode,
      approveStatus:val,
      approveRemark:form.approveRemark
    }
    PurchaseReturnApi.approve(params).then((res)=>{
      ElMessage.success(t('purchaseReturn.message.approveSuccess'));
      handleClose()
    }).finally(()=>{
      submitLoading.value = false
    })
  }

  onMounted(() => {
    queryDetail();
  });
</script>
<style scoped lang="scss">
  .purchaseReturnDetail {
    background: #ffffff;
    border-radius: 4px;
  }
</style>
<style lang="scss">
  .purchaseReturnDetail {
    .file-div {
      .el-form-item__content {
        display: block;
        margin-top: -38px;
      }

      .el-upload-list__item .is-success:hover {
        .el-upload-list__item-status-label {
          width: 0px !important;
          height: 0px !important;
          display: none !important;
        }

        .el-icon {
          width: 0px !important;
          height: 0px !important;
          display: none !important;
        }

        .el-icon--close-tip {
          width: 0px !important;
          height: 0px !important;
          display: none !important;
        }
      }

      .el-upload-list__item-status-label {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
      }

      .el-icon {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
      }

      .el-icon--close-tip {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
      }
    }

    .total-amount {
      margin-top: 20px;
    }

    .page-title {
      .item {
        margin-left: 30px;
      }
    }
    .table-title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 50px;
      background: #F4F6FA;
      box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585F;
      font-style: normal;
      padding: 15px 12px;
    }
  }

  .grad-row {
    position: relative;

    .audit-status {
      position: absolute;
      right: 50px;
      top: 0;
      z-index: 9999;

      .purchase-status {
        width: 100px;
        height: 100px;
        line-height: 100px;
        border-radius: 100px;
        font-size: 20px;
        padding: 0;
        transform: rotate(30deg);
      }
    }
  }
</style>
