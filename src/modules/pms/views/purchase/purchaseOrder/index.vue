<template>
    <div class="app-container">
        <div class="purchaseOrder">
          <el-card class="mb-12px search-card">
            <div class="search-form">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
                    <el-form-item prop="dateRange">
                        <el-select
                                v-model="queryParams.dateType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                class="!w-[180px] ml5"
                        >
                            <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                        <el-date-picker
                                :editable="false"
                                class="!w-[370px]"
                                v-model="queryParams.dateRange"
                                type="daterange"
                                range-separator="~"
                                start-placeholder="开始时间"
                                end-placeholder="截止时间"
                                value-format="YYYY-MM-DD"
                                :placeholder="$t('common.placeholder.selectTips')"
                        />
                        <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="changeDateRange(1)">{{$t('purchaseOrder.label.today')}}</span>
                        <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="changeDateRange(2)">{{$t('purchaseOrder.label.yesterday')}}</span>
                        <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)" @click="changeDateRange(3)">{{$t('purchaseOrder.label.weekday')}}</span>
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.orderType')" prop="orderType">
                        <el-select
                                v-model="queryParams.orderType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in orderTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="orderCode" :label="$t('purchaseOrder.label.orderCode')">
                        <el-input
                                v-model="queryParams.orderCode"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="purchaseTheme" :label="$t('purchaseOrder.label.purchaseTheme')">
                        <el-input
                          v-model="queryParams.purchaseTheme"
                          :placeholder="$t('common.placeholder.inputTips')"
                          clearable
                          class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.orderSource')" prop="orderSource">
                        <el-select
                                v-model="queryParams.orderSource"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in orderSourceList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.supplierName')" prop="supplierId">
                        <el-select
                                v-model="queryParams.supplierId"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                filterable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in supplierList" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.orderPurchaseStatus')" prop="orderPurchaseStatus">
                        <el-select
                                v-model="queryParams.orderPurchaseStatus"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in orderPurchaseStatusList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.purchaseUserName')" prop="purchaseUserId">
                        <el-select
                                v-model="queryParams.purchaseUserId"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                filterable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in purchasePersonnelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.synStatus')" prop="shutdownSynStatus">
                        <el-select
                          v-model="queryParams.shutdownSynStatus"
                          :placeholder="$t('common.placeholder.selectTips')"
                          clearable
                          filterable
                          class="!w-[256px]"
                        >
                            <el-option v-for="(item,index) in synStatusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-hasPerm="['pms:purchase:purchaseOrder:sync']" type="primary" plain @click="handleSync"  :disabled="multipleSelection.length===0">
                            {{$t('purchaseOrder.button.manualSync')}}
                        </el-button>
                        <el-button v-hasPerm="['pms:purchase:purchaseOrder:search']" type="primary" @click="handleQuery">
                            {{$t('common.search')}}
                        </el-button>
                        <el-button v-hasPerm="['pms:purchase:purchaseOrder:reset']"  @click="handleResetQuery">
                            {{$t('common.reset')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
          </el-card>

            <el-card class="content-card">
              <div class="action-bar">
                <el-button v-hasPerm="['pms:purchase:purchaseOrder:add']" type="primary" @click="addPurchaseOrder(null,'add')">
                  {{$t('purchaseOrder.button.addPurchaseOrder')}}
                </el-button>
                <!--                            <el-button v-hasPerm="['pms:purchase:purchaseOrder:bachSend']" type="primary" plain @click="sendPurchaseOrder(null)" :disabled="multipleSelection.length===0">-->
                <!--                                {{$t('purchaseOrder.button.sendPurchaseOrder')}}-->
                <!--                            </el-button>-->
                <el-button v-hasPerm="['pms:purchase:purchaseOrder:exportSequence']" @click="exportSequencePurchaseOrder()">
                  {{$t('common.exportSequence')}}
                </el-button>
                <el-button style="float: right;" v-hasPerm="['pms:purchase:purchaseOrder:export']" @click="exportPurchaseOrder()">
                  {{$t('purchaseOrder.button.export')}}
                </el-button>
              </div>

                <el-table
                        v-loading="loading"
                        :data="purchaseOrderList"
                        highlight-current-row
                        stripe
                        @selection-change="handleSelectionChange"
                >
                    <template #empty>
                        <Empty/>
                    </template>
                    <el-table-column type="selection" width="60" align="center" fixed="left" />
                    <el-table-column :label="$t('purchaseOrder.label.purchaseTheme')" prop="purchaseTheme" width="150">
                      <template #default="scope">
                        <div style="word-break: break-all;">{{scope.row.purchaseTheme ? scope.row.purchaseTheme : '-'}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.orderCode')" prop="orderCode" width="180"/>
                    <el-table-column :label="$t('purchaseOrder.label.orderType')" prop="orderType" width="100" show-overflow-tooltip>
                        <template #default="scope">
                            <span v-if="scope.row.orderType==1">{{t('purchaseOrder.orderTypeList.suppliersDirectSupply')}}</span>
                            <span v-if="scope.row.orderType==2">{{t('purchaseOrder.orderTypeList.marketDirectSupply')}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.orderSource')" prop="orderSource" width="130" show-overflow-tooltip>
                        <template #default="scope">
                            <span v-if="scope.row.orderSource==1">{{t('purchaseOrder.orderSourceList.manuallyAddPurchaseOrder')}}</span>
                            <span v-if="scope.row.orderSource==2">{{t('purchaseOrder.orderSourceList.purchaseTask')}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.purchaseUserName')" prop="purchaseUserName" min-width="120" show-overflow-tooltip/>
                    <el-table-column :label="$t('purchaseOrder.label.supplierName')" prop="supplierName" min-width="150" show-overflow-tooltip></el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.productNumber')" prop="totalPurchaseCount" show-overflow-tooltip align="right"></el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.planDeliveryDate')" prop="planDeliveryDate" width="130">
                        <template #default="scope">
                            <span>{{ scope.row.planDeliveryDate ? parseDateTime(scope.row.planDeliveryDate, "date") : '-' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.createUserName')" prop="createUserName" min-width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.remark')" prop="remark" min-width="150" show-overflow-tooltip></el-table-column>
                    <el-table-column label="创建时间" prop="createTime" width="180">
                      <template #default="scope">
                        <span>{{ scope.row.createTime ? parseDateTime(scope.row.createTime, "dateTime") : '-' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.approveStatus')" width="100" fixed="right">
                        <template #default="scope">
                            <div class="purchase">
                                <div class="purchase-status purchase-status-color1" v-if="scope.row.approveStatus==1">{{t('purchaseOrder.approveStatusOption[1]')}}</div>
                                <div class="purchase-status purchase-status-color3" v-if="scope.row.approveStatus==2">{{t('purchaseOrder.approveStatusOption[2]')}}</div>
                                <div class="purchase-status purchase-status-color5" v-if="scope.row.approveStatus==3">{{t('purchaseOrder.approveStatusOption[3]')}}</div>
                            </div>

                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.orderPurchaseStatus')" width="100" fixed="right">
                        <template #default="scope">
                                <div class="purchase">
                                    <div class="purchase-status purchase-status-color1" v-if="scope.row.orderPurchaseStatus==1">{{t('purchaseOrder.orderPurchaseStatusList.wattingPurchase')}}</div>
                                    <div class="purchase-status purchase-status-color2" v-if="scope.row.orderPurchaseStatus==2">{{t('purchaseOrder.orderPurchaseStatusList.partialPurchase')}}</div>
                                    <div class="purchase-status purchase-status-color3" v-if="scope.row.orderPurchaseStatus==3">{{t('purchaseOrder.orderPurchaseStatusList.allPurchase')}}</div>
                                    <div class="purchase-status purchase-status-color0" v-if="scope.row.orderPurchaseStatus==0">{{t('purchaseOrder.orderPurchaseStatusList.closePurchase')}}</div>
                                    <div class="purchase-status purchase-status-color4" v-if="scope.row.orderPurchaseStatus==4">{{t('purchaseOrder.orderPurchaseStatusList.waitReceive')}}</div>
                                </div>
                            <el-tooltip
                                    v-if="scope.row.orderPurchaseStatus===0 && scope.row.shutdownReason"
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.shutdownReason"
                                    placement="right"
                            >
                                <div class="close-reason cursor-pointer">{{$t('purchaseOrder.label.closeReason')}}：{{scope.row.shutdownReason}}</div>
                            </el-tooltip>
                        </template>
                    </el-table-column>
<!--                    <el-table-column :label="$t('purchaseOrder.label.sendStatus')" show-overflow-tooltip>-->
<!--                        <template #default="scope">-->
<!--                                <div v-if="scope.row.sendStatus==1" class="circle-div">-->
<!--                                    <div class="circle circle-color1"></div>-->
<!--                                    <div>{{t('purchaseOrder.sendStatusList.send')}}</div>-->
<!--                                </div>-->
<!--                                <div v-if="scope.row.sendStatus==0" class="circle-div">-->
<!--                                    <div class="circle circle-color0"></div>-->
<!--                                    <div>{{t('purchaseOrder.sendStatusList.noSend')}}</div>-->
<!--                                </div>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column :label="$t('purchaseOrder.label.synStatus')" width="100" fixed="right">
                        <template #default="scope">
                            <div class="purchase">
                                <div class="purchase-status purchase-status-color3" v-if="scope.row.shutdownSynStatus==1">{{t('purchaseOrder.synStatusOption.success')}}</div>
                                <div class="purchase-status purchase-status-color5" v-else-if="scope.row.shutdownSynStatus==2">{{t('purchaseOrder.synStatusOption.fail')}}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="240">
                        <template #default="scope">
                            <!-- 发送采购单  -->
<!--                            <el-button-->
<!--                                    v-hasPerm="['pms:purchase:purchaseOrder:send']"-->
<!--                                    v-if=" scope.row.orderType==1 && scope.row.orderPurchaseStatus == 1 && scope.row.approveStatus == 2 && scope.row.sendStatus == 0"-->
<!--                                    type="primary"-->
<!--                                    link-->
<!--                                    @click="sendPurchaseOrder(scope.row)"-->
<!--                            >-->
<!--                                {{$t('purchaseOrder.button.sendPurchaseOrder')}}-->
<!--                            </el-button>-->
                            <!-- 查看  -->
                            <el-button
                                    v-hasPerm="['pms:purchase:purchaseOrder:select']"
                                    type="primary"
                                    link
                                    @click="selectPurchaseOrder(scope.row.id)"
                            >
                                {{$t('common.select')}}
                            </el-button>
                            <!-- 编辑  -->
                            <el-button
                                    v-hasPerm="['pms:purchase:purchaseOrder:update']"
                                    v-if="(scope.row.orderPurchaseStatus==1 && scope.row.approveStatus!==2) || (scope.row.orderPurchaseStatus==1 && scope.row.approveStatus!==2)"
                                    type="primary"
                                    link
                                    @click="addPurchaseOrder(scope.row.id,'edit')"
                            >
                                {{$t('common.edit')}}
                            </el-button>

<!--                            <el-button-->
<!--                                    type="primary"-->
<!--                                    link-->
<!--                                    @click="addPurchaseOrder(scope.row.id,'copy')"-->
<!--                            >-->
<!--                                {{$t('purchaseOrder.button.copyPurchaseOrder')}}-->
<!--                            </el-button>-->

                            <!-- 打印  -->
                            <el-button
                                    v-hasPerm="['pms:purchase:purchaseOrder:print']"
                                    type="primary"
                                    link
                                    @click="handerPrint(scope.row.id,scope.row.orderCode)"
                            >
                                {{$t('common.print')}}
                            </el-button>

                            <!-- 关闭  -->
                            <el-button
                                    v-hasPerm="['pms:purchase:purchaseOrder:close']"
                                    v-if="scope.row.orderPurchaseStatus!==0 && scope.row.orderPurchaseStatus!==3"
                                    type="danger"
                                    link
                                    @click="closePurchaseOrder(scope.row)"
                            >
                                {{$t('purchaseOrder.button.closePurchaseOrder')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

              <div class="pagination-container">
                <pagination
                        v-if="total > 0"
                        v-model:total="total"
                        v-model:page="queryParams.page"
                        v-model:limit="queryParams.limit"
                        @pagination="handleQuery"
                />
              </div>
            </el-card>
        </div>
        <Close
                ref="closeRef"
                v-model:visible="dialog.visible"
                :title="dialog.title"
                @onSubmit="handleQuery"
        />
        <Print ref="printRef"/>
        <ExportSequence
            ref="exportSequenceRef"
            v-model:dialog-visible="dialogVisible"
            :path="`pms:purchaseOrder:export`">
        </ExportSequence>

        <!-- 需要打印的区域 -->
       <!-- <div id="printPurchaseOrder" style="display:none;">
            <div class="box-title">{{$t('purchaseOrder.label.purchaseOrderTitle')}}</div>
            <div>
                <el-row class="grad-row-print">
                    <el-col :span="8">
                        <el-form-item :label="$t('purchaseOrder.label.orderCode')+'：'">
                            {{form.orderCode}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('purchaseOrder.label.supplierName')+'：'">
                            {{form.supplierName}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('purchaseOrder.label.purchaseUserName')+'：'">
                            {{form.purchaseUserName}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="grad-row-print">
                    <el-col :span="8" v-if="form.orderPurchaseStatus!==1">
                        <el-form-item :label="$t('purchaseOrder.label.createTime')+'：'">
                            {{parseDateTime(form.createTime, "dateTime")}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="form.orderPurchaseStatus!==1">
                        <el-form-item :label="$t('purchaseOrder.label.orderType')+'：'">
                            <span v-if="form.orderType==1">{{t('purchaseOrder.orderTypeList.suppliersDirectSupply')}}</span>
                            <span v-if="form.orderType==2">{{t('purchaseOrder.orderTypeList.marketDirectSupply')}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="form.orderPurchaseStatus==1">
                        <el-form-item :label="$t('purchaseOrder.label.remark')+'：'">
                            {{form.remark}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item :label="$t('purchaseOrder.label.planDeliveryDate')+'：'">
                            {{parseDateTime(form.planDeliveryDate, "date")}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="grad-row-print">
                    <el-col :span="24" v-if="form.orderPurchaseStatus!==1">
                        <el-form-item :label="$t('purchaseOrder.label.remark')+'：'">
                            {{form.remark}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="grad-row-print">
                    <el-col :span="24">
                        <el-form-item :label="$t('purchaseOrder.label.productType')+'：'">
                            {{form.purchaseOrderDetailVOList.length}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-table :data="form.purchaseOrderDetailVOList" class="tabs">
                    <el-table-column type="index" label="序号" width="50" />
                    <el-table-column :label="$t('purchaseOrder.label.productName')" prop="productName"></el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.unitName')" prop="unitName"></el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.planPurchaseCount')" v-if="form.orderPurchaseStatus==1" prop="planPurchaseCount">
                        <template #default="scope">
                            <span v-if="form.orderPurchaseStatus==2 || form.orderPurchaseStatus==3">{{scope.row.planPurchaseCount}}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.purchasePrice')+'(￥/$)'" v-if="form.orderPurchaseStatus==1" prop="planPurchasePrice">
                        <template #default="scope">
                            <span v-if="form.orderPurchaseStatus==2 || form.orderPurchaseStatus==3">{{scope.row.planPurchasePrice}}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.planPurchaseAmount')+'(￥/$)'" v-if="form.orderPurchaseStatus==1" prop="planPurchaseAmount">
                        <template #default="scope">
                            <span v-if="form.orderPurchaseStatus==2 || form.orderPurchaseStatus==3">{{scope.row.planPurchaseAmount}}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.receivedCount')" v-if="form.orderPurchaseStatus!==1" prop="receivedCount">
                        <template #default="scope">
                            <span v-if="form.orderPurchaseStatus==2 || form.orderPurchaseStatus==3">{{scope.row.receivedCount}}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.receivedAmount')+'(￥/$)'" v-if="form.orderPurchaseStatus!==1" prop="receivedAmount">
                        <template #default="scope">
                            <span v-if="form.orderPurchaseStatus==2 || form.orderPurchaseStatus==3">{{scope.row.receivedAmount}}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="grad-row-print-money">
                    <span class="mr10px">{{$t('purchaseOrder.label.total')}}</span>
                    <span v-if="form.orderPurchaseStatus==1">
                       <span class="mr5px"> {{$t('purchaseOrder.label.purchaseAmount')+'(￥/$)：'}}</span>
                       <span> {{form.totalPurchaseAmount}}</span>
                    </span>
                    <span v-if="form.orderPurchaseStatus!==1" class="mr10px">
                         <span class="mr5px"> {{$t('purchaseOrder.label.receivingNumberCopy')+'(￥/$)：'}}</span>
                         <span> {{ form.totalReceivedCount}}</span>
                    </span>
                    <span v-if="form.orderPurchaseStatus!==1">
                         <span class="mr5px"> {{$t('purchaseOrder.label.receivingMoneyCopy')+'(￥/$)：'}}</span>
                         <span> {{form.totalReceivedAmount}}</span>
                    </span>
                </div>
            </div>
        </div>-->
    </div>
</template>

<script setup lang="ts">

    defineOptions({
        name: "PurchaseOrder",
        inheritAttrs: false,
    });

    import supplierAPI from "@/modules/pms/api/supplier";
    import PurchasePersonnelAPI from "@/modules/pms/api/purchasePersonnel";
    import PurchaseOrderAPI, {
        PurchaseOrderPageVO,
        PurchaseOrderPageQuery,
        PurchaseOrderFrom
    } from "@/modules/pms/api/purchaseOrder";
    import {IObject} from "@/core/components/CURD/types";
    import {useRouter} from "vue-router";
    import {convertToTimestamp, parseDateTime} from "@/core/utils/index.js";
    import moment from 'moment';
    import Close from "./components/close.vue";
    import Print from "./components/print.vue";
    import { useNavigation } from "@/core/composables/useNavigation";
    const { refreshAndNavigate } = useNavigation();
    const router = useRouter();
    const { t } = useI18n();
    const queryFormRef = ref(null);
    const dialogVisible = ref(false);
    const exportSequenceRef= ref()
    const loading = ref(false);
    const total = ref(0);
    const multipleSelection = ref([]);
    const purchasePersonnelList = ref([])
    const supplierList = ref([])
    const dateTypeList = ref([
        {
            key: 1,
            value: t('purchaseOrder.dateTypeList.createDate')
        },
        {
            key: 2,
            value:t('purchaseOrder.dateTypeList.planDeliveryDate')
        }
    ])
    const orderTypeList = ref([
        {
            key: 2,
            value: t('purchaseOrder.orderTypeList.marketDirectSupply')
        },
        {
            key: 1,
            value:t('purchaseOrder.orderTypeList.suppliersDirectSupply')
        }
    ])
    const orderSourceList = ref([
        {
            key: 2,
            value: t('purchaseOrder.orderSourceList.purchaseTask')
        },
        {
            key: 1,
            value:t('purchaseOrder.orderSourceList.manuallyAddPurchaseOrder')
        }
    ])
    const orderPurchaseStatusList = ref([
        {
            key: 1,
            value: t('purchaseOrder.orderPurchaseStatusList.wattingPurchase')
        },
        {
            key: 2,
            value:t('purchaseOrder.orderPurchaseStatusList.partialPurchase')
        },
        {
            key: 3,
            value:t('purchaseOrder.orderPurchaseStatusList.allPurchase')
        },
        {
            key: 0,
            value:t('purchaseOrder.orderPurchaseStatusList.closePurchase')
        },
        {
            key: 4,
            value:t('purchaseOrder.orderPurchaseStatusList.waitReceive')
        }
    ])
    const sendStatusList = ref([
        {
            key: 1,
            value: t('purchaseOrder.sendStatusList.send')
        },
        {
            key: 0,
            value:t('purchaseOrder.sendStatusList.noSend')
        },
    ])
    const synStatusOption  = ref([
        {
            value: 1,
            label: t('purchaseOrder.synStatusOption.success')
        },
        {
            value: 2,
            label:t('purchaseOrder.synStatusOption.fail')
        },
    ])


    const queryParams = reactive<PurchaseOrderPageQuery>({
        dateType:1,
        page: 1,
        limit: 20,
    });

    const purchaseOrderList = ref<PurchaseOrderPageVO[]>();

    const  closeRef= ref()
    const  printRef= ref()

    const dialog = reactive({
        title: "关闭",
        visible: false,
    });
    const form = reactive<PurchaseOrderFrom>({
        purchaseOrderDetailVOList:[]
    });


    /** 查询采购员列表 */
    function getPerchasePersonnelList() {
        PurchasePersonnelAPI.getPerchasePersonnelList()
            .then((data) => {
                purchasePersonnelList.value = data;
            })
    }

    /** 查询供应商列表 */
    function getSupplierList() {
        supplierAPI.getSupplierListAll()
            .then((data) => {
                supplierList.value = data;
            })
    }

    /** 时间转换 */
    function changeDateRange(val) {
        if(val===1) {
            // var date = moment(new Date()).format('YYYY-MM-DD')
            var date1 = moment().subtract('days', 0).format('YYYY-MM-DD')
            queryParams.dateRange = [date1,date1]
        }else if(val===2){
            // var date = moment(new Date().getTime() - 3600 * 24 * 1000).format('YYYY-MM-DD')
            var date1 = moment().subtract('days', 1).format('YYYY-MM-DD')
            queryParams.dateRange = [date1,date1]
        }else if(val===3){
            // var endDate = moment(new Date().getTime() - 3600 * 24 * 1000 * 6).format('YYYY-MM-DD')
            var endDate1 = moment(new Date()).format('YYYY-MM-DD')
            var startDate = moment().subtract('days', 6).format('YYYY-MM-DD')
            queryParams.dateRange = [startDate,endDate1]
        }
    }

    /** 查询 */
    function handleQuery() {
        loading.value = true;
        let params = {
            ...queryParams
        }
        if(queryParams.dateType==1 && queryParams.dateRange && queryParams.dateRange.length>0){
            params.startCreateTime=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.endCreateTime=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
        }
        if(queryParams.dateType==2 && queryParams.dateRange && queryParams.dateRange.length>0){
            params.startPlanDeliveryDate=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.endPlanDeliveryDate=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
        }
        delete params.dateType
        delete params.dateRange
        PurchaseOrderAPI.getPurchaseOrderPage(params)
            .then((data) => {
                purchaseOrderList.value = data.records;
                total.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 重置查询 */
    function handleResetQuery() {
        queryFormRef.value.resetFields();
        queryParams.dateType=1
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }

    /** 行复选框选中记录选中ID集合 */
    function handleSelectionChange(selection: any[]) {
        multipleSelection.value = selection;
    }

    /** 发送采购单*/
    function sendPurchaseOrder(row?: IObject,status?: number) {
        let data = {}
        let num =0
        if(row){
            num=1;
            data = {
                ids: [row.id],
            }
        }else{
            let flag =  multipleSelection.value.some(item =>item.orderPurchaseStatus!==1 || item.sendStatus!==0)
            if(flag){
                return  ElMessage.error(t('purchaseOrder.message.sendPurchaseOrderStatusTips'))
            }
            num=multipleSelection.value.length;
            let ids = multipleSelection.value.map((item) => item.id);
            data = {
                ids: ids,
            }
        }
        ElMessageBox.confirm(t('purchaseOrder.message.sendPurchaseOrderTips1')+ num +t('purchaseOrder.message.sendPurchaseOrderTips2'), t('purchaseOrder.title.sendPurchaseOrder'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                PurchaseOrderAPI.sendPurchaseOrder(data).then(res => {
                    ElMessage.success(t('purchaseOrder.message.sendPurchaseOrderSucess'))
                    handleQuery()
                })
            },
            () => {
                ElMessage.info(t('purchaseOrder.message.sendPurchaseOrderConcel'));
            }
        );
    }

    /** 导出采购单*/
    function exportPurchaseOrder() {
        ElMessageBox.confirm(t('common.exportTips'), t('common.export'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                loading.value = true;
                let params = {
                    ...queryParams
                }
                if(queryParams.dateType==1 && queryParams.dateRange && queryParams.dateRange.length>0){
                    params.startCreateTime=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
                    params.endCreateTime=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
                }
                if(queryParams.dateType==2 && queryParams.dateRange && queryParams.dateRange.length>0){
                    params.startPlanDeliveryDate=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
                    params.endPlanDeliveryDate=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
                }
                delete params.dateType
                delete params.dateRange
                delete params.page
                delete params.limit
                PurchaseOrderAPI.exportPurchaseOrder(params)
                    .then((data) => {
                        exportSequencePurchaseOrder()
                    })
                    .finally(() => {
                        loading.value = false;
                    });
            },
            () => {
                ElMessage.info(t('common.exportConcel'));
            }
        );
    }

    /** 导出序列*/
    function exportSequencePurchaseOrder(){
        exportSequenceRef.value.exportSequenceListPage()
        dialogVisible.value = true;
    }

    /** 关闭采购单 */
    function closePurchaseOrder(row?: IObject) {
        closeRef.value.setFormData({id:row.id,orderCode:row.orderCode});
        dialog.visible = true;
    }

    /** 新增/编辑采购单*/
    function addPurchaseOrder(id?:string,type?:string){
        refreshAndNavigate({
            path: "/pms/purchase/addPurchaseOrder",
            query: {id:id,type:type,title:type=='edit'?t("purchaseOrder.button.editPurchaseOrder"):t("purchaseOrder.button.addPurchaseOrder")}
        });
    }

    /** 查看采购单*/
    function selectPurchaseOrder(id?:string){
        router.push({
            path: "/pms/purchase/purchaseOrderDetail",
            query: {id:id}
        });
    }


   /* /!**打印*!/
    function handerPrint(id?: string,orderCode?:string){
        let params = {
            id: id,
            orderCode: orderCode
        }
        PurchaseOrderAPI.queryPurchaseOrderDetail(params)
            .then((data) => {
                Object.assign(form, data)
                if (nextTick) {
                    nextTick(() => {
                        setTimeout(function () {
                            const printContent = document.getElementById('printPurchaseOrder').innerHTML;
                            const iframe = document.createElement('iframe');
                            iframe.style.display = 'none';
                            document.body.appendChild(iframe);
                            const win = iframe.contentWindow;
                            // 将需要打印的HTML内容写入iframe的document中
                            win.document.write(printContent);
                            win.print();
                            document.body.removeChild(iframe);
                        }, 500);
                    })
                }
            })
    }*/

    /**打印*/
    function handerPrint(id?: string,orderCode?:string){
        printRef.value.setFormData({id: id,orderCode:orderCode,type:1})
    }

    /**
     * 手动同步
     */
    function handleSync() {

        const multiple=multipleSelection.value
        if(multiple.length>20){
            return ElMessage.error(t('purchaseOrder.message.maxSelect'))
        }

        let message = ''; // 提示消息
        let validOrderCodes:any = []; // 符符合条件的orderCode
        let validIds:any = []; // 符合条件的id

        multiple.forEach(item => {
            const { shutdownSynStatus, approveStatus, orderPurchaseStatus,orderCode, id } = item;
            if (shutdownSynStatus!==1 && approveStatus === 2 && orderPurchaseStatus==0) { //待采购，审核通过且同步不成功的
                validOrderCodes.push(orderCode);
                validIds.push(id);
            }
        });
        console.log('所有的orderCode:', multiple.map(item => item.orderCode));
        console.log('符合条件的orderCode:', validOrderCodes);
        console.log('符合条件的id:', validIds);


        //不符合条件的code
        const unqualifiedCodes =  multiple.map(item => item.orderCode).filter(code => !validOrderCodes.includes(code));
        console.log('不符合条件的orderCode:',unqualifiedCodes)


        if (unqualifiedCodes.length>0){
            message = unqualifiedCodes.map(code => `${code}`).join('、');
            console.log(message)
            return ElMessage.error(t('purchaseOrder.message.inconformitySelect',{message:message}))
        }

        if (validIds.length==0){
            return
        }

        PurchaseOrderAPI.syncShutdowm({ids:validIds}).then(res => {
            ElMessage.success(t('purchaseOrder.message.syncSucess'))
            handleQuery()
        })
    }


    onActivated(() => {
        getSupplierList();
        getPerchasePersonnelList();
        handleQuery();
    });
</script>

<style lang="scss" scoped>
    .purchaseOrder{
      height: 100%;
      display: flex;
      flex-direction: column;

      .search-card {
        flex-shrink: 0;
      }

      .content-card {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        :deep(.el-card__body) {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .action-bar {
          margin-bottom: 12px;
          flex-shrink: 0;
        }

        .el-table {
          flex: 1;
          overflow: auto;
        }

        .pagination-container {
          margin-top: 20px;
          display: flex;
          justify-content: center;
        }
      }
        .gred{
            color: #90979E;
        }
        .close-reason{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #90979E;
        }
        .circle-div{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .circle{
                width: 6px;
                height: 6px;
                border-radius: 3px;
                margin-right: 7px;
            }
            .circle-color1{
                background: #29B610;
            }
            .circle-color0{
                background: #D7DBDF;
            }
        }
    }
</style>
<style lang="scss">
   /* !* 打印样式 *!
    @media print {
          .tabs{
               margin-top: 20px;
               margin-bottom: 20px;
            }
          .box-title {
              text-align: center!important;
              margin-top: 24px;
              margin-bottom: 24px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 600 !important;
              font-size: 18px!important;
              color: #151719;
              font-style: normal;
          }

          .grad-row-print {
              .el-form-item__label {
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 500;
                  font-size: 12px;
                  color: #90979E;
                  font-style: normal;
              }

              .el-form-item__content {
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 500;
                  font-size: 12px;
                  color: #151719;
                  font-style: normal;
              }
          }

          .grad-row-print-money {
              display: flex;
              justify-content: flex-end;
              align-items: center;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              font-size: 12px;
              color: #52585F;
              font-style: normal;
          }

          .el-table {
              width: 100% !important;
              border: 1px solid #E5E7F3;
          }

          .el-table__header {
              width: 100% !important;
          }

          .el-table__body {
              width: 100% !important;
          }

          .el-table th, .el-table td {
              font-family: PingFangSC, PingFang SC;
              font-style: normal;
          }

          .el-table th {
              font-weight: 500;
              font-size: 12px;
              color: #52585F;
          }

          .el-table td {
              font-weight: 400;
              font-size: 12px;
              color: #52585F;
          }
    }*/
</style>


