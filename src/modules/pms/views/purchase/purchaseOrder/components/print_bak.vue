<template>
    <div id="printPurchaseOrder" style="display:none;">
        <div class="box-title">{{$t('purchaseOrder.label.purchaseOrderTitle')}}</div>
        <div>
            <div class="grad-row-print">
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseOrder.label.orderCode')}}：</span>
                    <span class="el-form-item__content">{{form.orderCode}}</span>
                </div>
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseOrder.label.supplierName')}}：</span>
                    <span class="el-form-item__content" v-if="form.supplierName"> {{form.supplierName}}</span>
                    <span class="el-form-item__content" v-else>-</span>
                </div>
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseOrder.label.purchaseUserName')}}：</span>
                    <span class="el-form-item__content" v-if="form.purchaseUserName"> {{form.purchaseUserName}}</span>
                    <span class="el-form-item__content" v-else>-</span>
                </div>
            </div>
            <div class="grad-row-print">
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseOrder.label.createTime')}}：</span>
                    <span class="el-form-item__content" v-if="form.createTime">{{parseDateTime(form.createTime, "dateTime")}}</span>
                    <span class="el-form-item__content" v-else>-</span>
                </div>
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseOrder.label.orderType')}}：</span>
                    <span v-if="form.orderType==1" class="el-form-item__content">{{$t('purchaseOrder.orderTypeList.suppliersDirectSupply')}}</span>
                    <span v-if="form.orderType==2" class="el-form-item__content">{{$t('purchaseOrder.orderTypeList.marketDirectSupply')}}</span>
                </div>
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseOrder.label.planDeliveryDate')}}：</span>
                    <span class="el-form-item__content" v-if="form.planDeliveryDate">{{parseDateTime(form.planDeliveryDate, "date")}}</span>
                    <span class="el-form-item__content" v-else>-</span>
                </div>
            </div>
            <div class="grad-row-print">
                <div class="el-form-item--div100">
                    <span class="el-form-item__label">{{$t('purchaseOrder.label.remark')}}：</span>
                    <span class="el-form-item__content" v-if="form.remark">{{form.remark}}</span>
                    <span class="el-form-item__content" v-else>-</span>
                </div>
            </div>
            <div class="grad-row-print">
                <div class="el-form-item--div100">
                    <span class="el-form-item__label">{{$t('purchaseOrder.label.productType')}}：</span>
                    <span class="el-form-item__content">{{form.purchaseOrderDetailVOList.length}}</span>
                </div>
            </div>
            <table  class="tabs">
                <thead>
                <tr>
                    <th class="index">{{$t('common.sort')}}</th>
                    <th>{{$t('purchaseOrder.label.productName')}}</th>
                    <th>{{$t('purchaseOrder.label.unitName')}}</th>
                    <th v-if="form.orderPurchaseStatus==1 || form.orderPurchaseStatus==4">{{$t('purchaseOrder.label.planPurchaseCount')}}</th>
                    <th v-if="form.orderPurchaseStatus==1 || form.orderPurchaseStatus==4">{{$t('purchaseOrder.label.purchasePrice')}}</th>
                    <th v-if="form.orderPurchaseStatus==1 || form.orderPurchaseStatus==4">{{$t('purchaseOrder.label.planPurchaseAmount')}}</th>
                    <th v-if="form.orderPurchaseStatus!==1 && form.orderPurchaseStatus!==4">{{$t('purchaseOrder.label.receivedCount')}}</th>
                    <th v-if="form.orderPurchaseStatus!==1 && form.orderPurchaseStatus!==4">{{$t('purchaseOrder.label.receivedAmount')}}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in form.purchaseOrderDetailVOList" :key="index">
                    <td class="index">{{index+1}}</td>
                    <td>{{ item.productName }}</td>
                    <td>{{ item.unitName }}</td>
                    <td v-if="form.orderPurchaseStatus==1 || form.orderPurchaseStatus==4">
                        <span v-if="item.planPurchaseCount">{{item.planPurchaseCount}}</span>
                        <span v-else>-</span>
                    </td>
                    <td v-if="form.orderPurchaseStatus==1 || form.orderPurchaseStatus==4">
                        <span v-if="item.planPurchasePrice">
                             <span v-if="form.orderType==2 || (form.orderType==1 && item.currencyCode == 'CNY')">￥</span>
                             <span v-else>$</span>
                            {{item.planPurchasePrice}}
                        </span>
                        <span v-else>-</span>
                    </td>
                    <td v-if="form.orderPurchaseStatus==1 || form.orderPurchaseStatus==4">
                        <span v-if="item.planPurchaseAmount">
                             <span v-if="form.orderType==2 || (form.orderType==1 && item.currencyCode == 'CNY')">￥</span>
                             <span v-else>$</span>
                            {{item.planPurchaseAmount}}
                        </span>
                        <span v-else>-</span>
                    </td>
                    <td v-if="form.orderPurchaseStatus!==1 && form.orderPurchaseStatus!==4">
                        <span v-if="(form.orderPurchaseStatus==2 || form.orderPurchaseStatus==3) && item.receivedCount">{{item.receivedCount}}</span>
                        <span v-else>-</span>
                    </td>
                    <td v-if="form.orderPurchaseStatus!==1 && form.orderPurchaseStatus!==4">
                        <span v-if="(form.orderPurchaseStatus==2 || form.orderPurchaseStatus==3) && item.receivedAmount">
                             <span v-if="form.orderType==2 || (form.orderType==1 && item.currencyCode == 'CNY')">￥</span>
                             <span v-else>$</span>
                            {{item.receivedAmount}}
                        </span>
                        <span v-else>-</span>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="grad-row-print-money">
                <span class="mr10px">{{$t('purchaseOrder.label.total')}}</span>
                <span v-if="form.orderPurchaseStatus==1 || form.orderPurchaseStatus==4">
                       <span class="mr5px"> {{$t('purchaseOrder.label.purchaseAmount')+'：'}}</span>
                       <span>
                            <span v-if="form.orderType==2 || (form.orderType==1 &&form.purchaseOrderDetailVOList[0].currencyCode == 'CNY')">￥</span>
                             <span v-else>$</span>
                           {{form.totalPurchaseAmount}}
                       </span>
                    </span>
                <span v-if="form.orderPurchaseStatus!==1 && form.orderPurchaseStatus!==4" class="mr10px">
                         <span class="mr5px"> {{$t('purchaseOrder.label.receivingNumberCopy')+'：'}}</span>
                         <span>
                              <span v-if="form.orderType==2 || (form.orderType==1 &&form.purchaseOrderDetailVOList[0].currencyCode == 'CNY')">￥</span>
                              <span v-else>$</span>
                             {{ form.totalReceivedCount}}
                         </span>
                    </span>
                <span v-if="form.orderPurchaseStatus!==1 && form.orderPurchaseStatus!==4">
                         <span class="mr5px"> {{$t('purchaseOrder.label.receivingMoneyCopy')+'：'}}</span>
                         <span>
                             <span v-if="form.orderType==2 || (form.orderType==1 &&form.purchaseOrderDetailVOList[0].currencyCode == 'CNY')">￥</span>
                             <span v-else>$</span>
                             {{form.totalReceivedAmount}}
                         </span>
                    </span>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import PurchaseOrderAPI, {PurchaseOrderFrom} from "@/modules/pms/api/purchaseOrder";
import {parseDateTime} from "@/core/utils/index.js";
const  type= ref(1)
const  id= ref('')
const  orderCode= ref('')
const form = reactive<PurchaseOrderFrom>({
    purchaseOrderDetailVOList:[]
});

function setFormData(data) {
    id.value = data.id;
    orderCode.value = data.orderCode;
    type.value = data.type;
    if(type.value==2){
        Object.assign(form,data.form)
    }
    handerPrint()
}

/**打印*/
function handerPrint(){
    if(type.value==1){
        let params = {
            id: id.value,
            orderCode: orderCode.value
        }
        PurchaseOrderAPI.queryPurchaseOrderDetail(params)
          .then((data) => {
              Object.assign(form, data)
              setDatas()
          })
    }else{
        setDatas()
    }

}
function setDatas(){
    if (nextTick) {
        nextTick(() => {
            setTimeout(function () {
                const printContent1 = document.getElementById('printPurchaseOrder').innerHTML;
                const printContent = `
                        <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <style lang="scss">
                                @media print {
                                    @page {
                                      @bottom-center {
                                        content: counter(pages) "/" counter(page);
                                        font-size: 10px;
                                      }
                                    }
                                    body {
                                        .tabs{
                                           margin-top: 20px;
                                           margin-bottom: 20px;
                                           border-top: 1px solid #E5E7F3 !important;
                                           border-left: 1px solid #E5E7F3 !important;
                                           border-right: 1px solid #E5E7F3 !important;
                                        }
                                        .mr10px{
                                            margin-right: 10px;
                                        }
                                        .mr5px{
                                            margin-right:5px;
                                        }
                                         .box-title {
                                              text-align: center!important;
                                              margin-top: 24px;
                                              margin-bottom: 24px;
                                              font-family: PingFangSC, PingFang SC;
                                              font-weight: 600 !important;
                                              font-size: 18px!important;
                                              color: #151719;
                                              font-style: normal;
                                          }
                                         .grad-row-print {
                                              display: flex;
                                              /*justify-content: space-around;*/
                                              justify-content: flex-start;
                                              .el-form-item--div{
                                                    width: 33%;
                                              }
                                                .el-form-item--div100{
                                                    width: 100%;
                                              }
                                              .el-form-item__label {
                                                  font-family: PingFangSC, PingFang SC;
                                                  font-weight: 400;
                                                  font-size: 12px;
                                                  color: #90979E;
                                                  font-style: normal;
                                              }
                                              .el-form-item__content {
                                                  font-family: PingFangSC, PingFang SC;
                                                  font-weight: 500;
                                                  font-size: 12px;
                                                  color: #151719;
                                                  font-style: normal;
                                                  word-break:break-word;
                                              }
                                          }
                                          .grad-row-print-money {
                                              display: flex;
                                              justify-content: flex-end;
                                              align-items: center;
                                              font-family: PingFangSC, PingFang SC;
                                              font-weight: 500;
                                              font-size: 12px;
                                              color: #52585F;
                                              font-style: normal;
                                          }

                                          table {
                                                border-collapse: collapse;
                                                width: 100%;
                                                table-layout: fixed;
                                                -webkit-print-color-adjust: exact;
                                          }
                                          table, th, td {
                                            border: 1px solid #E5E7F3;
                                          }
                                          th,td{
                                            width: cacl((100% - 50px)/4);
                                            border-left: none;
                                            border-right: none;
                                            font-family: PingFangSC, PingFang SC;
                                            font-style: normal;
                                            text-align: left;
                                            padding: 14px 12px;
                                            color: #52585F;
                                            word-break:break-word;
                                            .index{
                                                width: 50px;
                                            }
                                          }
                                          th {
                                              background: #F4F6FA;
                                              font-weight: 600;
                                              font-size: 12px;
                                              background: #F4F6FA;
                                          }
                                           td {
                                              font-weight: 400;
                                              font-size: 12px;
                                          }
                                    }
                               }
                            </style>
                        </head>
                        <body>
                          ${printContent1}
                        </body>
                        </html>
                    `;
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
                const win = iframe.contentWindow;
                // 将需要打印的HTML内容写入iframe的document中
                win.document.write(printContent);
                win.print();
                document.body.removeChild(iframe);
            }, 500);
        })
    }
}

defineExpose({
    setFormData,
});
</script>

<style scoped lang="scss">
</style>
<style lang="scss">
</style>
