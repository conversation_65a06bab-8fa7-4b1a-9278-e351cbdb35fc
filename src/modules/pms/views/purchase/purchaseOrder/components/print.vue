<template>
  <NewPrint ref="printRef">
    <div class="box-title">
      {{ form.tenantName }}{{ $t("purchaseOrder.label.purchaseOrderTitle") }}
    </div>
    <div>
      <div class="grad-row-print">
        <div
          class="el-form-item--div"
          v-if="form.orderType == 1"
        >
          <span class="el-form-item__label">
            {{ $t("purchaseOrder.label.supplierName") }}：
          </span>
          <span class="el-form-item__content">
            {{ form.supplierName }}
          </span>
        </div>
        <div class="el-form-item--div">
          <span class="el-form-item__label">
            {{ $t("purchaseOrder.label.purchaseUserName") }}：
          </span>
          <span class="el-form-item__content">
            {{ form.purchaseUserName }}
          </span>
        </div>

        <div class="el-form-item--div">
          <span class="el-form-item__label">
            {{ $t("purchaseOrder.label.paymentType") }}：
          </span>
          <span
            v-if="form.paymentType == 1"
            class="el-form-item__content"
          >
            {{ $t("purchaseOrder.paymentTypeOption[1]") }}
          </span>
          <span
            v-if="form.paymentType == 2"
            class="el-form-item__content"
          >
            {{ $t("purchaseOrder.paymentTypeOption[2]") }}
          </span>
        </div>
        <div class="el-form-item--div">
          <span class="el-form-item__label">
            {{ $t("purchaseOrder.label.orderCode") }}：
          </span>
          <span class="el-form-item__content">
            {{ form.orderCode }}
          </span>
        </div>
        <div class="el-form-item--div">
          <span class="el-form-item__label">
            {{ $t("purchaseOrder.label.receiveStorehouse") }}：
          </span>
          <span class="el-form-item__content">
            {{ form.warehouseName }}
          </span>
        </div>
      </div>
      <table class="print-table">
        <thead>
          <tr>
            <!--              <th class="index">{{ $t("common.sort") }}</th>-->
            <th>{{ $t("purchaseOrder.label.productNameCopy") }}</th>
            <th>{{ $t("purchaseOrder.label.unit") }}</th>
            <th>
              {{ $t("purchaseOrder.label.count") }}
            </th>
            <th>
              {{ $t("purchaseOrder.label.price") }}
            </th>
            <th>
              {{ $t("purchaseOrder.label.amount") }}
            </th>
            <th>
              {{ $t("purchaseOrder.label.remark") }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in form.purchaseOrderDetailVOList"
            :key="index"
          >
            <!--              <td class="index">{{ index + 1 }}</td>-->
            <td>{{ item.productName }}</td>
            <td>{{ item.unitName }}</td>
            <td>
              {{ item.planPurchaseCount }}
            </td>
            <td>
              <span v-if="!item.currencyCode || item.currencyCode == 'CNY'">
                ￥
              </span>
              <span v-else>$</span>
              {{ item.planPurchasePrice }}
            </td>
            <td>
              <span v-if="!item.currencyCode || item.currencyCode == 'CNY'">
                ￥
              </span>
              <span v-else>$</span>
              {{ item.planPurchaseAmount }}
            </td>
            <td>
              {{ item.remark }}
            </td>
          </tr>
          <tr>
            <td colspan="2">
              {{ $t("purchaseOrder.label.totalAmount") + "：" }}
              <span v-if="!form.currencyCode || form.currencyCode == 'CNY'">
                ￥
              </span>
              <span v-else>$</span>
              {{ form.totalDiscountedAmount }}
            </td>
            <td colspan="3">
              {{ $t("purchaseOrder.label.totalCount") + "：" }}
              {{ form.totalPurchaseCount }}
            </td>
            <td>
              {{ $t("purchaseOrder.label.discount") + "：" }}
              <template v-if="form.discountValue && form.discountType !== 3">
                <span v-if="!form.currencyCode || form.currencyCode == 'CNY'">
                  ￥
                </span>
                <span v-else>$</span>
              </template>

              {{ form.discountValue }}
              <template v-if="form.discountType === 3">%</template>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="grad-row-print">
      <div class="el-form-item--div">
        <span class="el-form-item__label">
          {{ $t("purchaseOrder.label.createUserName") }}：
        </span>
        <span class="el-form-item__content">
          {{ form.createUserName }}
        </span>
      </div>

      <div class="el-form-item--div">
        <span class="el-form-item__label">
          {{ $t("purchaseOrder.label.approveUserName") }}：
        </span>
        <span class="el-form-item__content">
          {{ form.approveUserName }}
        </span>
      </div>
    </div>
  </NewPrint>
</template>
<script setup lang="ts">
import PurchaseOrderAPI, {
  PurchaseOrderFrom,
} from "@/modules/pms/api/purchaseOrder";
import { parseDateTime } from "@/core/utils/index.js";

const type = ref(1);
const id = ref("");
const orderCode = ref("");
const printRef = ref();

import NewPrint from "@/core/components/NewPrint/index.vue";

const form = reactive<PurchaseOrderFrom>({
  purchaseOrderDetailVOList: [],
});

function setFormData(data: any) {
  id.value = data.id;
  orderCode.value = data.orderCode;
  type.value = data.type;
  if (type.value == 2) {
    Object.assign(form, data.form);
  }
  handerPrint();
}

//
/**打印*/
function handerPrint() {
  if (type.value == 1) {
    let params = {
      id: id.value,
      orderCode: orderCode.value,
    };
    PurchaseOrderAPI.queryPurchaseOrderDetail(params).then((data) => {
      Object.assign(form, data);
      printRef.value?.onPrint();
    });
  } else {
    printRef.value?.onPrint();
  }
}

defineExpose({
  setFormData,
});
</script>

<style scoped lang="scss">
.box-title {
  color: #000000;
  text-align: center !important;
  margin-bottom: 10px;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.grad-row-print {
  display: flex;
  flex-wrap: wrap;

  .el-form-item--div {
    flex-basis: calc(33% - 10px);
    display: flex;
  }

  .el-form-item__label {
    color: #000000;
    font-weight: 500;
    font-size: 12px;
    font-style: normal;
    width: 100px;
  }

  .el-form-item__content {
    color: #000000;
    font-weight: 500;
    font-size: 12px;
    word-break: break-word;
  }
}

.print-table {
  margin-top: 10px;
  border: 1px solid #000000 !important;
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
  -webkit-print-color-adjust: exact;
}

table,
th,
td {
  color: #000000;
  border: 1px solid #000000;
}

th,
td {
  padding: 6px;
  word-break: break-word;
  font-size: 12px;
  font-weight: 500;
}
</style>
