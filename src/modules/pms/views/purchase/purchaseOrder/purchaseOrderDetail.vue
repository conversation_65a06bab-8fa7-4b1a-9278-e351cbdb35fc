<template>
  <div class="app-container">
    <div
      class="purchaseOrderDetail"
      v-loading="loading"
    >
      <div class="page-title">
        <div class="purchase-title">
          <div
            @click="handleClose()"
            class="cursor-pointer mr8px"
          >
            <el-icon>
              <Back />
            </el-icon>
          </div>
          <div>
            {{ t("purchaseOrder.label.orderCode") }}：{{ form.orderCode }}
          </div>
        </div>
        <div class="purchase">
          <span
            class="purchase-status purchase-status-color1"
            v-if="form.orderPurchaseStatus == 1"
          >
            {{ t("purchaseOrder.orderPurchaseStatusList.wattingPurchase") }}
          </span>
          <span
            class="purchase-status purchase-status-color2"
            v-if="form.orderPurchaseStatus == 2"
          >
            {{ t("purchaseOrder.orderPurchaseStatusList.partialPurchase") }}
          </span>
          <span
            class="purchase-status purchase-status-color3"
            v-if="form.orderPurchaseStatus == 3"
          >
            {{ t("purchaseOrder.orderPurchaseStatusList.allPurchase") }}
          </span>
          <span
            class="purchase-status purchase-status-color0"
            v-if="form.orderPurchaseStatus == 0"
          >
            {{ t("purchaseOrder.orderPurchaseStatusList.closePurchase") }}
          </span>
          <span
            class="purchase-status purchase-status-color4"
            v-if="form.orderPurchaseStatus == 4"
          >
            {{ t("purchaseOrder.orderPurchaseStatusList.waitReceive") }}
          </span>
        </div>
        <template v-if="form.approveStatus === 3">
          <div class="item">
            {{ $t("purchaseOrder.label.approveReject") }}：{{
              form.approveRemark || "-"
            }}
          </div>
        </template>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          label-width="96px"
          label-position="right"
        >
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("purchaseOrder.label.basicInformation") }}
            </div>
          </div>
          <div class="grad-row">
            <el-row>
              <el-col :span="8">
                <el-form-item
                  :label="$t('purchaseOrder.label.purchaseTheme')"
                  prop="purchaseTheme"
                >
                  {{ form.purchaseTheme || "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('purchaseOrder.label.orderType')">
                  <span v-if="form.orderType == 1">
                    {{ t("purchaseOrder.orderTypeList.suppliersDirectSupply") }}
                  </span>
                  <span v-if="form.orderType == 2">
                    {{ t("purchaseOrder.orderTypeList.marketDirectSupply") }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  :label="$t('purchaseOrder.label.purchaseUserName')"
                >
                  {{ form.purchaseUserName }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  :label="$t('purchaseOrder.label.planDeliveryDate')"
                >
                  {{ parseDateTime(form.planDeliveryDate, "date") }}
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item :label="$t('purchaseOrder.label.storehouse')">
                  {{ form.warehouseName }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('purchaseOrder.label.orderSource')">
                  <span v-if="form.orderSource == 1">
                    {{
                      t(
                        "purchaseOrder.orderSourceList.manuallyAddPurchaseOrder"
                      )
                    }}
                  </span>
                  <span v-if="form.orderSource == 2">
                    {{ t("purchaseOrder.orderSourceList.purchaseTask") }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('purchaseOrder.label.createTime')">
                  {{ parseDateTime(form.createTime, "dateTime") }}
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item :label="$t('purchaseOrder.label.createUserName')">
                  {{ form.createUserName }}
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-if="form.orderType == 1"
              >
                <el-form-item :label="$t('purchaseOrder.label.supplierName')">
                  {{ form.supplierName }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('purchaseOrder.label.paymentType')">
                  <span>
                    {{
                      form.paymentType
                        ? t(
                            `purchaseOrder.paymentTypeOption[${form.paymentType}]`
                          )
                        : "-"
                    }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('purchaseOrder.label.contractName')">
                  {{ form.contractName || "-" }}
                </el-form-item>
              </el-col>
              <div class="purchase audit-status">
                <div
                  class="purchase-status purchase-status-color1"
                  v-if="form.approveStatus == 1"
                >
                  {{ t("purchaseOrder.approveStatusOption[1]") }}
                </div>
                <div
                  class="purchase-status purchase-status-color3"
                  v-if="form.approveStatus == 2"
                >
                  {{ t("purchaseOrder.approveStatusOption[2]") }}
                </div>
                <div
                  class="purchase-status purchase-status-color5"
                  v-if="form.approveStatus == 3"
                >
                  {{ t("purchaseOrder.approveStatusOption[3]") }}
                </div>
              </div>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("purchaseOrder.label.productListInformation") }}
            </div>
          </div>
          <div>
            <el-table
              ref="tableSumRef1"
              :data="form.purchaseOrderDetailVOList"
              :summary-method="getSum"
              :span-method="arraySpanMethod"
              show-summary
              class="purchaseOrderDetail-table"
            >
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
                align="center"
              />
              <el-table-column
                :label="$t('purchaseOrder.label.productImg')"
                min-width="150"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div class="product-div">
                    <div class="picture">
                      <img
                        :src="scope.row.imagesUrls"
                        alt=""
                      />
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.productName')"
                prop="productName"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('purchaseOrder.label.unitName')"
                prop="unitName"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('purchaseOrder.label.onHandStock')"
                v-if="
                  form.orderPurchaseStatus == 0 ||
                  form.orderPurchaseStatus == 1
                "
                prop="inventoryCount"
                show-overflow-tooltip
                align="right"
              >
                <template #default="scope">
                  <span
                    v-if="
                      (form.orderPurchaseStatus == 0 || form.orderPurchaseStatus == 1) &&
                      scope.row.inventoryCount !== undefined &&
                      scope.row.inventoryCount !== null
                    "
                  >
                    {{ scope.row.inventoryCount }}
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <!--              <el-table-column-->
              <!--                :label="$t('purchaseOrder.label.planPurchaseCount')"-->
              <!--                v-if="-->
              <!--                  form.orderPurchaseStatus == 1 ||-->
              <!--                  form.orderPurchaseStatus == 0 ||-->
              <!--                  form.orderPurchaseStatus == 4-->
              <!--                "-->
              <!--                prop="planPurchaseCount"-->
              <!--                show-overflow-tooltip-->
              <!--                align="right"-->
              <!--              >-->
              <!--                <template #default="scope">-->
              <!--                  <span-->
              <!--                    v-if="-->
              <!--                      form.orderPurchaseStatus == 1 ||-->
              <!--                      form.orderPurchaseStatus == 4-->
              <!--                    "-->
              <!--                  >-->
              <!--                    {{ scope.row.planPurchaseCount }}-->
              <!--                  </span>-->
              <!--                  <span v-else>-</span>-->
              <!--                </template>-->
              <!--              </el-table-column>-->
              <el-table-column
                :label="$t('purchaseOrder.label.planPurchaseCount')"
                prop="planPurchaseCount"
                show-overflow-tooltip
                align="right"
              >
                <template #default="scope">
                  <span>
                    {{ scope.row.planPurchaseCount || "-" }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.purchasePrice')"
                v-if="
                  form.orderPurchaseStatus == 0 ||
                  form.orderPurchaseStatus == 1
                "
                prop="planPurchasePrice"
                show-overflow-tooltip
                align="right"
              >
                <template #default="scope">
                  <span
                    v-if="
                      form.orderPurchaseStatus == 0 ||
                       form.orderPurchaseStatus == 1
                    "
                  >
                    <span
                      v-if="
                        form.orderType == 2 ||
                        (form.orderType == 1 && scope.row.currencyCode == 'CNY')
                      "
                    >
                      ￥
                    </span>
                    <span v-else-if="!scope.row.currencyCode">￥</span>
                    <span v-else>$</span>
                    {{ scope.row.planPurchasePrice }}
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.planPurchaseAmount')"
                v-if="
                  form.orderPurchaseStatus == 0 ||
                  form.orderPurchaseStatus == 1
                "
                prop="planPurchaseAmount"
                show-overflow-tooltip
                align="right"
              >
                <template #default="scope">
                  <span
                    v-if="
                      form.orderPurchaseStatus == 0 ||
                       form.orderPurchaseStatus == 1
                    "
                  >
                    <span
                      v-if="
                        form.orderType == 2 ||
                        (form.orderType == 1 && scope.row.currencyCode == 'CNY')
                      "
                    >
                      ￥
                    </span>
                    <span v-else-if="!scope.row.currencyCode">￥</span>
                    <span v-else>$</span>
                    {{ scope.row.planPurchaseAmount }}
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.receivedCount')"
                v-if="
                  form.orderPurchaseStatus !== 0 &&
                  form.orderPurchaseStatus !== 1
                "
                prop="receivedCount"
                show-overflow-tooltip
                align="right"
              ></el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.receivedAmount')"
                v-if="
                  form.orderPurchaseStatus !== 0 &&
                  form.orderPurchaseStatus !== 1

                "
                prop="receivedAmount"
                show-overflow-tooltip
                align="right"
              >
                <template #default="scope">
                  <span
                    v-if="
                      form.orderType == 2 ||
                      (form.orderType == 1 && scope.row.currencyCode == 'CNY')
                    "
                  >
                    ￥
                  </span>
                  <span v-else-if="!scope.row.currencyCode">￥</span>
                  <span v-else>$</span>
                  {{ scope.row.receivedAmount }}
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.remark')"
                prop="remark"
                show-overflow-tooltip
              >
                <template #default="scope">

                    {{ scope.row.remark || "-"}}

                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="total-amount">
            <el-row class="flex-center-start">
              <el-col :span="6">
                <el-form-item
                  :label="$t('purchaseOrder.label.totalPurchaseAmount')"
                  prop="totalPurchaseAmount"
                >
                  <span v-if="form.currencyCode == 'CNY'">￥</span>
                  {{ form.totalPurchaseAmount }}
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item
                  :label="$t('purchaseOrder.label.discountType')"
                  prop="discountType"
                >
                  {{
                    $t(
                      `purchaseOrder.label.discountTypeOption[${form.discountType || 1}]`
                    )
                  }}

                  <template
                    v-if="form.discountType === 2 || form.discountType === 3"
                  >
                    <span v-if="form.currencyCode == 'CNY'">￥</span>
                    {{ form.discountValue }}
                  </template>
                  <template v-if="form.discountType === 3">%</template>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  :label="$t('purchaseOrder.label.totalDiscountedAmount')"
                  prop="totalDiscountedAmount"
                >
                  <span v-if="form.currencyCode == 'CNY'">￥</span>
                  {{ form.totalDiscountedAmount || "-" }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("purchaseOrder.label.otherInformation") }}
            </div>
          </div>
          <div>
            <el-row>
              <el-col
                :span="24"
                class="file-div"
              >
                <el-form-item
                  :label="$t('purchaseOrder.label.orderAttachmentFiles')"
                >
                  <upload-multiple
                    :isDelete="false"
                    :showUploadBtn="false"
                    :isShowTip="false"
                    :listType="`text`"
                    :tips="''"
                    :fileSize="10"
                    :fileType="[
                      'rar',
                      'zip',
                      'docx',
                      'xls',
                      'xlsx',
                      'pdf',
                      'jpg',
                      'png',
                      'jpeg',
                    ]"
                    :isPrivate="`public-read`"
                    :modelValue="form.orderAttachmentFiles"
                    @update:model-value="onChangeMultiple"
                    ref="detailPicsRef"
                    :limit="10"
                    :formRef="formUpdateRef"
                    class="modify-multipleUpload"
                    name="detailPic"
                  >
                    <template #default="{ file }">点击上传</template>
                  </upload-multiple>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('purchaseOrder.label.remark')">
                  <span
                    v-if="form.remark"
                    style="word-break: break-all"
                  >
                    {{ form.remark }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-content">
              {{ $t("purchaseOrder.label.approve") }}
            </div>
          </div>
          <div>
            <el-row>
              <el-col :span="24">
                <el-form-item
                  :label="$t('purchaseOrder.label.approveUserName')"
                >
                  {{ form.approveUserName || "-" }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("purchaseOrder.label.deliveryAttachmentFileInformation") }}
            </div>
          </div>
          <div>
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label-width="0px"
                  v-if="
                    form.deliveryAttachmentFileObject &&
                    form.deliveryAttachmentFileObject.length > 0
                  "
                >
                  <upload-multiple
                    :isDelete="false"
                    :isShowTip="false"
                    :isPrivate="`public-read`"
                    :modelValue="form.deliveryAttachmentFileObject"
                    @update:model-value="onChangeMultiple"
                    ref="detailPicsRef"
                    :limit="form.deliveryAttachmentFileObject.length"
                    :formRef="formUpdateRef"
                    class="modify-multipleUpload"
                    name="detailPic"
                  ></upload-multiple>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div
            class="title-lable"
            v-if="
              form.orderPurchaseStatus == 2 || form.orderPurchaseStatus == 3
            "
          >
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("purchaseOrder.label.receivingDocumentInformation") }}
            </div>
          </div>
          <div
            v-if="
              form.orderPurchaseStatus == 2 || form.orderPurchaseStatus == 3
            "
          >
            <el-table
              :data="form.receiveTransportRecordVOList"
              highlight-current-row
              stripe
            >
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
                align="center"
              />
              <el-table-column
                :label="$t('purchaseOrder.label.receivingOrder')"
                prop="receiveTransportCode"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('purchaseOrder.label.receivingTime')"
                prop="createTime"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span>
                    {{ parseDateTime(scope.row.createTime, "dateTime") }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.receivingNumber')"
                prop="totalReceivedCountThisTime"
                show-overflow-tooltip
                align="right"
              />
              <el-table-column
                :label="$t('purchaseOrder.label.receivingMoney')"
                prop="totalReceivedAmountThisTime"
                show-overflow-tooltip
                align="right"
              >
                <template #default="scope">
                  <span
                    v-if="
                      form.orderType == 2 ||
                      (form.orderType == 1 &&
                        form.purchaseOrderDetailVOList[0].currencyCode == 'CNY')
                    "
                  >
                    ￥
                  </span>
                  <span
                    v-else-if="!form.purchaseOrderDetailVOList[0].currencyCode"
                  >
                    ￥
                  </span>
                  <span v-else>$</span>
                  <span>{{ scope.row.totalReceivedAmountThisTime }}</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.deductionAmount')"
                prop="deductionAmount"
                show-overflow-tooltip
                align="right"
              >
                <template #default="scope">
                  <span
                    v-if="
                      form.orderType == 2 ||
                      (form.orderType == 1 &&
                        form.purchaseOrderDetailVOList[0].currencyCode == 'CNY')
                    "
                  >
                    ￥
                  </span>
                  <span
                    v-else-if="!form.purchaseOrderDetailVOList[0].currencyCode"
                  >
                    ￥
                  </span>
                  <span v-else>$</span>
                  <span>{{ scope.row.totalDeductionAmount }}</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.remark')"
                prop="remark"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.operationUser')"
                prop="updateUserName"
                show-overflow-tooltip
              />
              <el-table-column
                fixed="right"
                :label="$t('common.handle')"
                width="100"
              >
                <template #default="scope">
                  <el-button
                    type="primary"
                    link
                    size="small"
                    v-if="scope.row.confirmStatus === 0"
                    @click="handleShippingReceiptDetail(scope.row)"
                  >
                    {{ $t("purchaseOrder.button.viewBtn") }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <template
            v-if="
              (form.orderPurchaseStatus == 2 || form.orderPurchaseStatus == 3) && form.purchaseReturnOrderInfoVO?.approveStatus==2
            "
          >
            <div class="title-lable">
              <div class="title-line"></div>
              <div class="title-content">
                {{ $t("purchaseOrder.label.purchaseReturnOrder") }}
              </div>
            </div>
            <div class="content-panel">
              <span class="label">{{$t('purchaseOrder.label.purchaseReturnOrderNumber')}}:</span>
              <span class="content primary" @click="handlePurchaseReturnOrderNumber">{{form?.purchaseReturnOrderInfoVO?.returnOrderCode || '-'}}</span>
              <span class="label">{{$t('purchaseOrder.label.returnRequestInitiationTime')}}:</span>
              <span class="content">{{ parseDateTime(form?.purchaseReturnOrderInfoVO?.returnTime, "dateTime") || '-' }} </span>
              <span class="label">{{$t('purchaseOrder.label.approveTime')}}:</span>
              <span class="content">{{ parseDateTime(form?.purchaseReturnOrderInfoVO?.approveTime, "dateTime") || '-'}}</span>
              <span class="label">{{$t('purchaseOrder.label.totalAmountReturned')}}:</span>
              <span class="content red"> <span v-if="!form.currencyCode || form.currencyCode == 'CNY'">￥</span>
                  <span v-else>$</span>
                  {{form?.purchaseReturnOrderInfoVO?.totalReturnAmount || '-'}}</span>
            </div>
            <div class="title-lable">
              <div class="title-line"></div>
              <div class="title-content">
                {{ $t("purchaseOrder.label.returnGoodsDispatchNotice") }}
              </div>
            </div>
            <div class="content-panel">
              <span class="label">{{$t('purchaseOrder.label.outboundOrderNumber')}}:</span>
              <span class="content primary" @click="handleOutboundOrderNumber">{{form?.purchaseReturnOrderInfoVO?.outboundNoticeCode || '-'}}</span>
              <span class="label">{{$t('purchaseOrder.label.status')}}:</span>
              <span class="content">{{form?.purchaseReturnOrderInfoVO?.outboundNoticeStatusStr || '-'}}</span>
            </div>
          </template>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("purchaseOrder.label.documentOperationRecordInformation") }}
            </div>
          </div>
          <div>
            <el-table
              :data="form.operationLogVOList"
              highlight-current-row
              stripe
            >
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
                align="center"
              />
              <el-table-column
                :label="$t('purchaseOrder.label.operationTime')"
                prop="operationTime"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span>
                    {{ parseDateTime(scope.row.operationTime, "dateTime") }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('purchaseOrder.label.operationType')"
                prop="operationRemark"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('purchaseOrder.label.operationUser')"
                prop="operationName"
                show-overflow-tooltip
              />
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">
          {{ $t("common.reback") }}
        </el-button>
        <!--                <el-button  v-hasPerm="['pms:purchase:purchaseOrder:update']" type="primary" plain @click="handleEdit"   v-if="(form.orderType==1 && form.orderPurchaseStatus==1 && form.sendStatus==0 && form.orderSource==1) || (form.orderType==2 && form.orderPurchaseStatus==1)">{{ $t("common.edit") }}</el-button>-->
        <el-button
          v-if="(form.orderPurchaseStatus == 2 || form.orderPurchaseStatus == 3) && (!form.purchaseReturnOrderInfoVO?.approveStatus || form.purchaseReturnOrderInfoVO?.approveStatus==3)"
          type="primary"
          plain
          @click="handleReturned"
        >
          {{ $t("purchaseOrder.button.returnedBtn") }}
        </el-button>

        <el-button
          type="primary"
          plain
          @click="handerPrint"
        >
          {{ $t("common.print") }}
        </el-button>
        <!--                <el-button  v-hasPerm="['pms:purchase:purchaseOrder:send']" v-if="form.orderType==1 && form.orderPurchaseStatus==1 && form.sendStatus==0" type="primary" :loading="submitLoading" @click="sendPurchaseOrder">{{ $t("purchaseOrder.button.sendPurchaseOrder") }}</el-button>-->
      </div>
    </div>
    <Print ref="printRef" />
    <!--收运单详情弹窗-->
    <ShippingReceiptDetailDrawer
      v-if="dialog.visible"
      v-model="dialog.visible"
      ref="receiveTransport"
      :title="dialog.title"
      :drawerType="drawerType"
      :data="rowData"
      @cancel="dialog.visible=false" />
  </div>
</template>

<script setup lang="ts">
import { IObject } from "@/core/components/CURD/types";

defineOptions({
  name: "PurchaseOrderDetail",
  inheritAttrs: false,
});

import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import PurchaseOrderAPI, {
  PurchaseOrderFrom,
} from "@/modules/pms/api/purchaseOrder";
import { parseDateTime } from "@/core/utils/index.js";
import Print from "./components/print.vue";
import FileAPI from "@/core/api/file";
// import ShippingReceiptDetailDrawer from "./components/shippingReceiptDetailDrawer.vue";
import ShippingReceiptDetailDrawer from "@/modules/pms/views/collectionTransportation/shippingReceipt/components/shippingReceiptDetailDrawer.vue";
import { useNavigation } from "@/core/composables/useNavigation";
const { refreshAndNavigate } = useNavigation();

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const fromRef = ref();
const submitLoading = ref(false);
const queryFormRef = ref(ElForm);
const roleFormRef = ref(ElForm);
const loading = ref(false);
const id = route.query.id;
const orderCode = route.query.orderCode;
const tableSumRef1 = ref();
const form = reactive<PurchaseOrderFrom>({
  purchaseOrderDetailVOList: [],
});
const printRef = ref();
const drawerType = ref('')
const rowData = ref('')

async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}

function handleEdit() {
  router.push({
    path: "/pms/purchase/addPurchaseOrder",
    query: {
      id: id,
      type: "edit",
      title: t("purchaseOrder.button.editPurchaseOrder"),
    },
  });
}

/** 查询采购单详情 */
function queryPurchaseOrderDetail() {
  loading.value = true;
  let params = {
    id: id,
    orderCode: orderCode,
  };
  PurchaseOrderAPI.queryPurchaseOrderDetail(params)
    .then((data) => {
      Object.assign(form, data);
    })
    .finally(() => {
      loading.value = false;
    });
}

function arraySpanMethod() {
  setTimeout(() => {
    if (tableSumRef1.value) {
      let current = tableSumRef1.value.$el
        .querySelector(".el-table__footer-wrapper")
        .querySelector(".el-table__footer");
      let cell = current.rows[0].cells;
      cell[0].colSpan = "9";
    }
  }, 50);
}

/** 合计 */
function getSum(param) {
  const { columns, data } = param;
  const sums = [];
  let currenty = "";
  if (
    form.orderType == 2 ||
    (form.orderType == 1 &&
      form.purchaseOrderDetailVOList[0].currencyCode == "CNY")
  ) {
    currenty = "￥";
  } else if (!form.purchaseOrderDetailVOList[0]?.currencyCode) {
    currenty = "￥";
  } else {
    currenty = "$";
  }
  columns.forEach((column, index) => {
    /* if (index === 0) {
             sums[index] = '合计:';
             return;
         }*/
    if (form.orderPurchaseStatus == 2 || form.orderPurchaseStatus == 3) {
      sums[0] =
        "合计:\u3000已收货量 " +
        form.totalReceivedCount +
        "\u3000已收货金额  " +
        currenty +
        form.totalReceivedAmount;
    } else {
      if (form.orderPurchaseStatus == 1 || form.orderPurchaseStatus == 4) {
        sums[0] =
          "合计:\u3000计划采购量 " +
          form.totalPurchaseCount +
          "\u3000计划采购金额  " +
          currenty +
          form.totalPurchaseAmount;
      } else {
        sums[0] =
          "合计:\u3000计划采购量 " +
          "-" +
          "\u3000计划采购金额  " +
          currenty +
          "-";
      }
    }
  });
  return sums;
}

/** 发送采购单*/
function sendPurchaseOrder() {
  let num = 1;
  let data = {
    ids: [id],
  };
  ElMessageBox.confirm(
    t("purchaseOrder.message.sendPurchaseOrderTips1") +
      num +
      t("purchaseOrder.message.sendPurchaseOrderTips2"),
    t("purchaseOrder.title.sendPurchaseOrder"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      PurchaseOrderAPI.sendPurchaseOrder(data).then((res) => {
        ElMessage.success(t("purchaseOrder.message.sendPurchaseOrderSucess"));
        handleClose();
      });
    },
    () => {
      ElMessage.info(t("purchaseOrder.message.sendPurchaseOrderConcel"));
    }
  );
}

/**打印*/
function handerPrint() {
  printRef.value.setFormData({
    id: id,
    orderCode: orderCode,
    type: 2,
    form: form,
  });
}

function download(item) {
  FileAPI.downloadFile(item.url, item.name);
}

/** 查看 */
let receiveTransport = ref(ElForm);
const dialog = reactive({
  visible: false,
  title: t("shippingReceipt.title.detailsTitle"),
});

function handleShippingReceiptDetail(row: any) {
  // receiveTransport.value.setEditType({
  //   type: "details",
  //   receiveTransportId: row.id,
  // });
    row.receiveTransportId=row.id
    drawerType.value = 'details';
    rowData.value = row;
  dialog.visible = true;
}

/**
 * 退货
 */
function handleReturned() {
    refreshAndNavigate({
    path: "/pms/purchase/purchaseReturnAddOrEdit",
    query: {
      returnOrderCode: null,
      type: "add",
      title: t("purchaseReturn.button.addPurchaseReturn"),
      fromPage: 2,
      orderCode: form.orderCode,
    },
  });
}

/**
 * 采购退货单号
 */
function handlePurchaseReturnOrderNumber() {
    router.push({
        path: "/pms/purchase/purchaseReturnDetail",
        query: {
            returnOrderCode: form?.purchaseReturnOrderInfoVO?.returnOrderCode,
        },
    });
}
/**
 * 快速出库通知单号
 */

function handleOutboundOrderNumber() {
    router.push({
        path: "/wms/quickOutbound/detailQuickOutbound",
        query: {
            id: form?.purchaseReturnOrderInfoVO?.outboundId,
        },
    });
}

onMounted(() => {
  queryPurchaseOrderDetail();
});
</script>
<style scoped lang="scss">
.purchaseOrderDetail {
  background: #ffffff;
  border-radius: 4px;
}
</style>
<style lang="scss">
.purchaseOrderDetail {
  .file-div {
    .el-form-item__content {
      display: block;
      margin-top: -38px;
    }

    .el-upload-list__item .is-success:hover {
      .el-upload-list__item-status-label {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
      }

      .el-icon {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
      }

      .el-icon--close-tip {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
      }
    }

    .el-upload-list__item-status-label {
      width: 0px !important;
      height: 0px !important;
      display: none !important;
    }

    .el-icon {
      width: 0px !important;
      height: 0px !important;
      display: none !important;
    }

    .el-icon--close-tip {
      width: 0px !important;
      height: 0px !important;
      display: none !important;
    }
  }

  .total-amount {
    margin-top: 20px;
  }

  .page-title {
    .item {
      margin-left: 30px;
    }
  }
  .el-form-item--default{
    margin-bottom: 0px !important;
  }
}

.purchaseOrderDetail-table .el-table__footer {
  .cell {
    display: flex;
    justify-content: flex-end;
  }
}

.grad-row {
  position: relative;

  .audit-status {
    position: absolute;
    right: 50px;
    top: 0;
    z-index: 9999;

    .purchase-status {
      width: 100px;
      height: 100px;
      line-height: 100px;
      border-radius: 100px;
      font-size: 20px;
      padding: 0;
      transform: rotate(30deg);
    }
  }
}

.content-panel {
  padding: 16px;
  background: #f4f6fa;
  border: 1px solid #ebeef5;
  font-size: 14px;
  .label {
    color: #606266;
  }
  .content {
      margin-left: 5px;
    margin-right: 40px;
    &.red {
      color: #c00c1d;
    }
    &.primary {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
}
</style>
