<template>
  <div class="">
    <!-- 基本信息 -->
    <el-card class="">
      <div class="card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="handleBack"
        />
        <span class="status" @click="handleBack">{{ t('purchaseInquiry.copyInquery.title') }}</span>
      </div>
      <div class="mb-20px">
        <div class="card-title">{{ t('purchaseInquiry.copyInquery.basicInfo') }}</div>
      </div>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="t('purchaseInquiry.copyInquery.deliveryPeriod')" prop="deliveryPeriod">
              <el-date-picker
                v-model="formData.deliveryPeriod"
                :disabled="formDisabled && detailFormDisable"
                type="daterange"
                :start-placeholder="t('purchaseInquiry.copyInquery.startDate')"
                :end-placeholder="t('purchaseInquiry.copyInquery.endDate')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleDateChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('purchaseInquiry.copyInquery.inquiryType')" prop="inquiryType">
              <el-select
                v-model="formData.inquiryType"
                :disabled="formDisabled"
                :placeholder="t('purchaseInquiry.copyInquery.pleaseSelect')"
              >
                <el-option
                  v-for="item in inquiryTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('purchaseInquiry.copyInquery.warehouse')" prop="warehouseId">
              <el-select
                v-model="formData.warehouseId"
                :disabled="formDisabled"
                :placeholder="t('purchaseInquiry.copyInquery.pleaseSelect')"
                @change="handleWarehouseChange"
              >
                <el-option
                  v-for="item in warehouseList"
                  :key="item.warehouseId"
                  :label="item.warehouseName"
                  :value="item.warehouseId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="t('purchaseInquiry.copyInquery.name')" prop="title">
              <el-input
                v-model="formData.title"
                :disabled="formDisabled && detailFormDisable"
                :placeholder="t('purchaseInquiry.copyInquery.pleaseInput')"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="16" class="text-right">
            <el-button type="primary" @click="handleSearch">{{ t('purchaseInquiry.copyInquery.confirm') }}</el-button>
            <el-button @click="handleReset">{{ t('purchaseInquiry.copyInquery.reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-divider />
      <sectiton v-show="isFormValid || useCopyProducts">
        <!-- 询价商品 -->
        <div class="mb-5">
          <div class="card-title mb-20px">{{ t('purchaseInquiry.copyInquery.inquiryProducts') }}</div>
          <div class="text-14px text-gray-400 mb-12px">
            {{ t('purchaseInquiry.copyInquery.productsCount') }}
            <span class="products-count">{{ products.length }}</span>
            {{ t('purchaseInquiry.copyInquery.productsUnit') }}
          </div>
        </div>
        <div>
          <div
            v-for="(item, index) in products"
            :key="index"
            class="flex items-center p-5 border-b product-item"
          >
            <el-image
              :src="item.productImg"
              class="w-25 h-25 object-cover mr-5"
            />
            <section class="flex-1">
              <div class="text-16px font-bold mb-2">{{ item.productName }}</div>
              <div
                class="flex flex-wrap gap-32px text-14px text-gray-500 mb-12px"
              >
                <span>{{ t('purchaseInquiry.copyInquery.purchaseUnit') }}: {{ item.unitName }}</span>
                <span>{{ t('purchaseInquiry.copyInquery.productBrand') }}: {{ item.brandName }}</span>
                <span>{{ t('purchaseInquiry.copyInquery.lastPurchasePrice') }}: ¥{{ item.purchasePrice }}</span>
                <span>{{ t('purchaseInquiry.copyInquery.defaultSupplier') }}: {{ item.defaultSupplierName }}</span>
              </div>
              <div class="flex-1 mr-56px">
                <el-form-item :label="t('purchaseInquiry.copyInquery.remark')">
                  <el-input
                    v-model="item.remark"
                    :placeholder="t('purchaseInquiry.copyInquery.pleaseInput')"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </div>
            </section>
            <div class="split-line"></div>
            <div class="ml-56px mr-56px cursor-pointer delete-icon">
              <el-icon @click="removeProduct(index)"><Delete /></el-icon>
            </div>
          </div>
        </div>

        <div class="bottom-btn">
          <el-button
            class="primary-text w-full h-full"
            @click="addProduct"
            :disabled="!isFormValid"
          >
            <el-icon><Plus /></el-icon>
            {{ t('purchaseInquiry.copyInquery.addProduct') }}
          </el-button>
        </div>
        <!-- 底部按钮 -->
        <div class="text-right mt-5">
          <el-button @click="handleBack">{{ t('purchaseInquiry.copyInquery.cancel') }}</el-button>
          <el-button type="primary" @click="nextStep">{{ t('purchaseInquiry.copyInquery.nextStep') }}</el-button>
        </div>
      </sectiton>
    </el-card>
    <AddGoods
      v-if="showAddGoods"
      v-model:visible="showAddGoods"
      :form-data="goodsForm"
      @add-goods="getSelectedGoods"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import type { FormInstance } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import router from "@/core/router";
import AddGoods from "./components/addGoods.vue";
import { useWarehouse } from "@pms/composables/warehouse";
import { useSupplier } from "@pms/composables/supplier";
import { useInquiryForm } from "./composables/useInquiryForm";
import { useInquiryStore } from "@pms/store/inquiry";
import { useDetail } from "./composables/useDetail";
import {parseTime} from "@/core/utils";
import InqueryAPI from "@pms/api/inquery";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const { inqueryDetail, getInqueryDetail } = useDetail();
const { warehouseList } = useWarehouse();
// const { supplierList } = useSupplier();
const useCopyProducts = ref(false); // 是否使用复制商品,使用复制商品，则商品直接展示
const inquiryStore = useInquiryStore();
const formData = ref({
  deliveryPeriod: [], // 计划交货周期日期范围
  startDeliveryDate: "", // 计划交货周期开始日期
  endDeliveryDate: "", // 计划交货周期结束日期
  inquiryType: "", // 询价类型
  warehouseId: "", // 仓库
  warehouseName: "", // 仓库名称
  title: "", // 名称,关键字搜索
  chooseType: 1, // 1-新增询价单选择产品 2-新增采购单选择商品 3-新增采购需求和采购任务选择商品
});

const goodsForm = computed(() => { // 查询商品的参数
  return {
    startDeliveryDate: formData.value.startDeliveryDate,
    endDeliveryDate: formData.value.endDeliveryDate,
    inquiryType: formData.value.inquiryType,
    warehouseId: formData.value.warehouseId,
    warehouseName: formData.value.warehouseName,
    chooseType: 1,
    title: formData.value.title,
  };
});

const detailFormDisable = ref(false) // 询价单复制初始化时，交货周期和名称允许输入
watch(inqueryDetail, (val) => { // 监听inqueryDetail的变化
  if (val) {
    formData.value.warehouseId = val.warehouseId;
    formData.value.warehouseName = val.warehouseName;
    formData.value.title = val.title;
    formData.value.inquiryType = val.inquiryType;
    formData.value.deliveryPeriod = [
      parseTime(new Date(val.startDeliveryDate), '{y}-{m}-{d}'),
      parseTime(new Date(val.endDeliveryDate), '{y}-{m}-{d}'),
    ];

    handleDateChange(formData.value.deliveryPeriod);
    products.value = val.inquiryDetailVOS.map((item) => {
      return {
        ...item,
        completedPricingSupplierListVOList: item.inquiryProductSupplierListVOS
      }
    });
    detailFormDisable.value = false;
    formDisabled.value = true;
    useCopyProducts.value = true;
  }
});
// 在组件中引入 tagsView store
import { useTagsViewStore } from "@/core/store/modules/tagsView";

/*const handleClose = async () => {
  await tagsViewStore.delView(route);
  router.push("/pms/contract/manage");
};*/

// 在组件中定义跳转方法
const tagsViewStore = useTagsViewStore();
const route = useRoute();

const navigateAndCloseCurrentTab = async (path: string) => {
  // 先删除当前页面的标签
  await tagsViewStore.delView(route);
  // 等待标签删除完成后再进行路由跳转
  nextTick(() => {
    router.push(path);
  });
};
// 在 script setup 中添加返回方法
const handleBack = () => {
  navigateAndCloseCurrentTab("/pms/purchase/inquery");
};
const inquiryTypeList = ref([
  {
    label: t('purchaseInquiry.copyInquery.domesticSupply'),
    value: 0,
  },
  {
    label: t('purchaseInquiry.copyInquery.foreignDirectPurchase'),
    value: 1,
  },
]);

const products = ref([]);

const showAddGoods = ref(false);

const formRef = ref<FormInstance>();
const { rules, validateForm } = useInquiryForm();

// 表单是否已通过验证
const formValidated = ref(false);

// 表单验证状态
const isFormValid = computed(() => {
  return (
    formValidated.value &&
    formData.value.deliveryPeriod?.length === 2 &&
    formData.value.inquiryType !== "" &&
    formData.value.warehouseId !== "" &&
    formData.value.title !== ""
  );
});
// 在 script 部分添加表单禁用状态
const formDisabled = ref(false);
// 搜索并验证表单
const handleSearch = async () => {
  const valid = await validateForm(formRef.value);
  if (valid) {
    formValidated.value = true;
    formDisabled.value = true; // 验证通过后禁用表单
    detailFormDisable.value = true;
    useCopyProducts.value = true;
  }
};

// 表单验证状态
// const isFormValid = computed(() => {
//   return formData.value.deliveryPeriod?.length === 2 &&
//          formData.value.inquiryType !== '' &&
//          formData.value.warehouseId !== '' &&
//          formData.value.title !== '';
// });

const removeProduct = (index: number) => {
  products.value.splice(index, 1);
};

// 合并商品数据，避免重复
const mergeProducts = (existingProducts: any[], newGoods: any[]) => {
  // 获取现有商品的ID列表
  const existingProductIds = existingProducts.map(
    (product) => product.productId
  );

  // 过滤出不存在的新商品
  const newProducts = newGoods.filter(
    (good) => !existingProductIds.includes(good.productId)
  );

  // 合并数组
  return [...existingProducts, ...newProducts];
};

// 更新商品列表
const updateProducts = (goods: any[]) => {
  if (!products.value) {
    products.value = goods;
  } else {
    products.value = mergeProducts(products.value, goods);
  }
};

// 获取弹窗中选择的商品
const getSelectedGoods = (goods: any) => {
  // products.value = goods;
  updateProducts(goods);
};

const addProduct = async () => {
  const isValid = await validateForm(formRef.value);
  if (!isValid) {
    return;
  }
  // 这里需要处理已有商品在弹窗中回显的逻辑
  showAddGoods.value = true;
};

const getTime = (date) => {
  return new Date(date).getTime();
};

// 获取供应商报价信息
const getSupplierInqueryInfo = async () => {
  // 转换数据为 InquiryDetailDTO 格式
  const inquiryDetails = inquiryStore.selectedProducts.map((product) => ({
    brandName: product.brandName,
    currency: product.currency,
    defaultSupplierId: product.defaultSupplierId,
    defaultSupplierName: product.defaultSupplierName,
    firstCategoryId: product.firstCategoryId,
    inquiryProductSupplierListDTOList:
    product.completedPricingSupplierListVOList,
    productCategoryFullName: product.productCategoryFullName,
    productCode: product.productCode,
    productId: product.productId,
    productImg: product.productImg,
    productName: product.productName,
    purchasePrice: product.purchasePrice,
    remark: product.remark,
    secondCategoryId: product.secondCategoryId,
    thirdCategoryId: product.thirdCategoryId,
    unitId: product.unitId,
    unitName: product.unitName,
  }));

  const data = await InqueryAPI.getSupplierInqueryInfo({
    startDeliveryDate: getTime(`${inquiryStore.inquiryForm.startDeliveryDate}`),
    endDeliveryDate: getTime(`${inquiryStore.inquiryForm.endDeliveryDate}`),
    warehouseId: inquiryStore.inquiryForm.warehouseId,
    inquiryType: inquiryStore.inquiryForm.inquiryType,
    inquiryDetailDTOS: inquiryDetails,
  });
};

const nextStep = async () => {
  if (products.value.length === 0) {
    ElMessage({
      type: "warning",
      message: t('purchaseInquiry.copyInquery.noProducts'),
    });
    return;
  }
  inquiryStore.setCreateInquiryForm(formData.value);
  inquiryStore.setInquiryForm(goodsForm.value);
  inquiryStore.setSelectedProducts(products.value);
  try {
    await getSupplierInqueryInfo();
    // 实现下一步的逻辑
    navigateAndCloseCurrentTab("/pms/purchase/productMatching");
  }
  catch (error) {}
};

// 添加日期变化处理函数
const handleDateChange = (val: string[]) => {
  if (val) {
    formData.value.startDeliveryDate = val[0] + " 00:00:00";
    formData.value.endDeliveryDate = val[1] + " 23:59:59";
  } else {
    formData.value.startDeliveryDate = "";
    formData.value.endDeliveryDate = "";
  }
};

const handleWarehouseChange = (warehouseId: string | number) => {
  const selectedWarehouse = warehouseList.value.find(
    (item) => item.warehouseId === warehouseId
  );
  if (selectedWarehouse) {
    formData.value.warehouseName = selectedWarehouse.warehouseName;
  }
};

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields();
  formValidated.value = false;
  formDisabled.value = false; // 重置时启用表单
  useCopyProducts.value = false;
  detailFormDisable.value = false;
  formData.value = {
    deliveryPeriod: [], // 计划交货周期日期范围
    startDeliveryDate: "", // 计划交货周期开始日期
    endDeliveryDate: "", // 计划交货周期结束日期
    inquiryType: "", // 询价类型
    warehouseId: "", // 仓库
    warehouseName: "", // 仓库名称
    title: "", // 名称,关键字搜索
    chooseType: 1, // 1-新增询价单选择产品 2-新增采购单选择商品 3-新增采购需求和采购任务选择商品
  };
  products.value = [];
};

const restoreData = () => {
  try {
    // 从 store 获取保存的表单数据
    const savedForm = inquiryStore.createInquiryForm;
    if (savedForm) {
      formData.value = { ...savedForm };
    }

    // 从 store 获取保存的产品数据
    const savedProducts = inquiryStore.selectedProducts;
    if (savedProducts && savedProducts.length > 0) {
      products.value = [...savedProducts];
    }
    formValidated.value = true;
    formDisabled.value = true; // 验证通过后禁用表单
  } catch (error) {
    console.error("恢复数据失败:", error);
    ElMessage.error("恢复数据失败");
  }
};

const isBackFromNext = ref(false);
// 监听路由来源
onMounted(() => {
  // 检查是否从下一页返回
  const fromPath = route.query.from as string;
  isBackFromNext.value = fromPath === "next-page"; // 替换为实际的下一页路由路径

  if (isBackFromNext.value) {
    // 从下一页返回，恢复数据
    restoreData();
  }
});
</script>

<style scoped lang="scss">
:deep(.el-card__body) {
  padding-top: 0px;
}
.products-count {
  color: #613bec;
  margin: 0 4px;
}
.card-header {
  padding: 20px 10px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #e5e7f3;
  border-radius: 4px 4px 0px 0px;
  cursor: pointer;
  .back-btn {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  .status {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
}
.product-item {
  background: #f9f9fb;
  border-radius: 2px;
  border: 1px dash #d7dbdf;
  margin-bottom: 20px;
}
.bottom-btn {
  height: 80px;
  background: #f9f9fb;
  border-radius: 2px;
  border: 1px dashed #d7dbdf;
  margin-bottom: 20px;

  .el-button {
    border: none;
    background: transparent;
    height: 100%;
    &:hover {
      border: 1px solid #762ADB ;
      font-size: 18px;
    }
  }
}
.split-line {
  width: 1px;
  height: 20px;
  background: #cccfd5;
}
.delete-icon {
  &:hover {
    color: #762ADB ;
  }
}
</style>
