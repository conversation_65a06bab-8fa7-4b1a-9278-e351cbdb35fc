
<template>
  <div class="app-container">
    <div class="search-container">
      <el-form
        ref="queryFormRef"
        :model="queryParams"
        :inline="true"
        label-width="84px"
      >
        <el-form-item
          prop="supplierName"
          :label="$t('supplierManagement.label.supplierName')"
        >
          <el-input
            v-model="queryParams.supplierName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            class="!w-[256px]"
          />
        </el-form-item>
        <el-form-item
          :label="$t('supplierManagement.label.status')"
          prop="enableStatus"
        >
          <el-select
            v-model="queryParams.enableStatus"
            clearable
            :placeholder="$t('common.placeholder.selectTips')"
            class="!w-[256px]"
          >
            <el-option
              v-for="item in statusList"
              :key="item.statusId"
              :label="item.statusName"
              :value="item.statusId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="mobile"
          :label="$t('supplierManagement.label.phone')"
        >
          <el-input
            v-model="queryParams.mobile"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            class="!w-[256px]"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleQuery"
            v-hasPerm="['pms:supplier:search']"
          >
            {{ $t("common.search") }}
          </el-button>
          <el-button
            @click="handleResetQuery"
            v-hasPerm="['pms:supplier:reset']"
          >
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          type="primary"
          @click="AddSupplier(undefined, 'add')"
          v-hasPerm="['pms:supplier:add']"
        >
          {{ $t("supplierManagement.button.addSupplier") }}
        </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="tableData"
        highlight-current-row
        stripe
        :key="new Date()"
      >
        <template #empty>
          <Empty />
        </template>
        <el-table-column
          :label="$t('supplierManagement.label.supplierCode')"
          prop="supplierCode"
        />
        <el-table-column
          :label="$t('supplierManagement.label.supplierName')"
          prop="supplierName"
        />
        <el-table-column
          prop="legalPerson"
          :label="$t('supplierManagement.label.curator')"
        >
          <template #default="scope">
            <EncryptPhone :nameType="true" :name="scope.row.legalPerson" />
          </template>
        </el-table-column>
        <el-table-column
          prop="mobile"
          :label="$t('supplierManagement.label.phone')"
          min-width="70"
        >
          <template #default="scope">
            <!-- <EncryptPhone :phone="scope.row.mobile" /> -->
            <span class="encryptBox">
              {{ scope.row.countryAreaCode }}
              <span v-if="scope.row.mobile && scope.row.mobile.length <= 4">
                {{ scope.row.mobile }}
              </span>
              <span v-else>
                {{ scope.row.mobile }}
                <el-icon
                  v-if="scope.row.mobile"
                  @click="
                    scope.row.mobilePhoneShow
                      ? getRealPhone(scope.row.supplierId, scope.$index)
                      : ''
                  "
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['pms:supplier:eye']"
                >
                  <component :is="scope.row.mobilePhoneShow ? 'View' : ''" />
                </el-icon>
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('supplierManagement.label.loginAccount')"
          prop="loginAccountName"
        />
        <el-table-column :label="$t('supplierManagement.label.status')" align="center" width="120">
          <template #default="scope">
            <el-switch
              :active-text="$t('common.statusEmun.enable')"
              :inactive-text="$t('common.statusEmun.disable')"
              inline-prompt
              style="
                --el-switch-on-color: #762ADB ;
                --el-switch-off-color: #cccfd5;
              "
              v-model="scope.row.enableStatus"
              :active-value="1"
              :inactive-value="0"
              @change="handleUpdateStatus(scope.row)"
              v-hasPerm="['pms:supplier:updateStatus']"
            />
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('common.handle')" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              v-hasPerm="['pms:supplier:edit']"
              @click="
                AddSupplier(
                  scope.row.supplierId,
                  'edit',
                  scope.row.enableStatus
                )
              "
            >
              {{ $t("common.edit") }}
            </el-button>
            <el-button
              type="primary"
              link
              v-hasPerm="['pms:supplier:details']"
              @click="AddSupplier(scope.row.supplierId, 'details')"
            >
              {{ $t("common.detailBtn") }}
            </el-button>
            <el-button
              type="danger"
              link
              v-hasPerm="['pms:supplier:delete']"
              @click="handleDelete(scope.row)"
            >
              {{ $t("common.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "SupplierManagement",
  inheritAttrs: false,
});

import supplierAPI, {
  SupplierPageVO,
  PageQuery,
} from "@/modules/pms/api/supplier";
import { title } from "process";

import { useNavigation } from "@/core/composables/useNavigation";
const { refreshAndNavigate } = useNavigation();

const { t } = useI18n();
const router = useRouter();

const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const tableData = ref<SupplierPageVO[]>();

const queryParams = reactive<PageQuery>({
  page: 1,
  limit: 20,
});

const statusList = ref([
  {
    statusId: 0,
    statusName: t("common.statusEmun.disable"),
  },
  {
    statusId: 1,
    statusName: t("common.statusEmun.enable"),
  },
]);

/** 查询 */
function handleQuery() {
  loading.value = true;
  supplierAPI
    .getPageList(queryParams)
    .then((data: any) => {
      tableData.value = data.records;
      tableData.value = data.records.map((item: any, index: any) => {
        item.mobilePhoneShow = true;
        return { ...item };
      });
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

function getRealPhone(id: any, index: any) {
  supplierAPI
    .queryRealPhone({ supplierId: id })
    .then((data: any) => {
      tableData.value[index].mobile = data;
      tableData.value[index].mobilePhoneShow = false;
    })
    .finally(() => {});
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery();
}
/** 新增编辑 */
function AddSupplier(id?: string, type?: string, status?: any) {
  let isEdit: any = "true";
  if (status && status == 1 && type == "edit") {
    isEdit = "false";
  } else {
    isEdit = "true";
  }
  let title;
  if (type == "edit") {
    title = t("supplierManagement.title.editSupplier");
  } else if (type == "details") {
    title = t("supplierManagement.title.supplierDetails");
  } else {
    title = t("supplierManagement.title.addSupplier");
  }
    refreshAndNavigate({
    path: "/pms/supplier/addSupplier",
    query: { title: title, supplierId: id, type: type, isEdit: isEdit },
  });
}

/** 禁用启用 */
function handleUpdateStatus(row: any) {
  let flag = row.enableStatus;
  row.enableStatus = row.enableStatus === 0 ? 1 : 0; //保持switch点击前的状态
  let params = {
    supplierId: row.supplierId,
    enableStatus: flag,
  };
  // 禁用
  if (row.enableStatus == 1) {
    ElMessageBox.confirm(
      t("supplierManagement.message.updateStatusTips"),
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
      }
    )
      .then(() => {
        supplierAPI
          .updateEnableStatus(params)
          .then((res: any) => {
            ElMessage({
              type: "success",
              message: t("supplierManagement.message.disabledSucess"),
            });
            handleQuery();
          })
          .catch(() => {
            handleQuery();
          });
      })
      .catch(() => {
        handleQuery();
        return false;
      });
  } else {
    // 启用
    supplierAPI.updateEnableStatus(params).then((res) => {
      handleQuery();
    });
  }
}

/** 删除角色 */
function handleDelete(row: any) {
  if (row.enableStatus == 1) {
    return ElMessage.error(t("supplierManagement.message.deleteNotTips"));
  }
  ElMessageBox.confirm(
    `${t("supplierManagement.message.deleteTips")}${row.supplierName}?`,
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      let params = {
        supplierId: row.supplierId,
      };
      supplierAPI
        .deleteByIds(params)
        .then(() => {
          ElMessage.success(t("supplierManagement.message.deleteSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("supplierManagement.message.deleteCancel"));
    }
  );
}

onActivated(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.encryptBox {
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  vertical-align: text-top;
}
</style>
