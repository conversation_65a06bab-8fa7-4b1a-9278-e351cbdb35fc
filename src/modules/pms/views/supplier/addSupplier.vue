<template>
  <div class="app-container">
    <div class="addSupplier">
      <div class="page-title">
        <div @click="handleCancel" class="display_block">
          <el-icon><Back /></el-icon>
          <span v-if="type == 'add'">
            {{ $t("supplierManagement.button.addSupplier") }}
          </span>
          <span v-else-if="type == 'edit'">
            {{ $t("supplierManagement.button.editSupplier") }}
          </span>
          <span v-else>
            {{ $t("supplierManagement.button.supplierDetails") }}
          </span>
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="112px"
          label-position="right"
          :disabled="type == 'details'"
        >
          <div class="basicInformation item_content">
            <div class="title">
              {{ $t("supplierManagement.label.basicInformation") }}
            </div>
            <el-row justify="space-between">
              <el-col :span="6">
                <!-- 供应商名称 -->
                <el-form-item
                  :label="$t('supplierManagement.label.supplierName')"
                  prop="supplierName"
                >
                  <el-input
                    v-model="form.supplierName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="100"
                    clearable
                    :disabled="isEdit == 'false'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 供应商类型 -->
                <el-form-item
                  :label="$t('supplierManagement.label.supplierType')"
                  prop="supplierTypeCode"
                >
                  <el-select
                    v-model="form.supplierTypeCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    :disabled="isEdit == 'false'"
                  >
                    <el-option
                      v-for="(item, index) in supplierTypeList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 供应商简称 -->
                <el-form-item
                  :label="$t('supplierManagement.label.supplierAbbreviation')"
                  prop="supplierShortName"
                >
                  <el-input
                    v-model="form.supplierShortName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="20"
                    clearable
                    :disabled="isEdit == 'false'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 采购员 -->
                <el-form-item
                  :label="$t('supplierManagement.label.purchaser')"
                  prop="purchaserId"
                >
                  <el-select
                    v-model="form.purchaserId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    :disabled="isEdit == 'false'"
                  >
                    <el-option
                      v-for="(item, index) in purchaserList"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row justify="space-between">
              <el-col :span="6">
                <!-- 国家 -->
                <el-form-item
                  :label="$t('supplierManagement.label.country')"
                  prop="countryCode"
                >
                  <el-select
                    v-model="form.countryCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="countryChange"
                    clearable
                    :disabled="isEdit == 'false'"
                  >
                    <el-option
                      v-for="(item, index) in countryList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <!-- 地区 -->
                <el-form-item
                  :label="$t('supplierManagement.label.area')"
                  prop="areaInfo"
                >
                  <!-- 省 -->
                  <el-select
                    :disabled="!form.countryCode || isEdit == 'false'"
                    v-model="form.provinceCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleProvinceChange"
                    class="!w-[120px]"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in provinceList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- 市 -->
                  <el-select
                    v-if="showCityInput"
                    :disabled="isEdit == 'false'"
                    v-model="form.cityCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleCityChange"
                    class="!w-[120px]"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in cityList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- 区 -->
                  <el-select
                    v-if="showDistrictInput"
                    :disabled="isEdit == 'false'"
                    v-model="form.districtCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleDistrictChange"
                    class="!w-[120px]"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in districtList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- <el-cascader
                    ref="areaRef"
                    :disabled="!form.countryCode || isEdit == 'false'"
                    :placeholder="$t('common.placeholder.selectTips')"
                    :props="areaProps"
                    v-model="form.    :disabled="!form.countryCode || isEdit == 'false'""
                    :options="cityList"
                    @change="handleCascaderChange"
                    clearable
                  >
                    <template #default="{ node, data }">
                      <div class="defaultData" @click="clickNode">
                        {{ data.shortName }}
                      </div>
                    </template>
                  </el-cascader> -->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!-- 详细地址 -->
                <el-form-item
                  :label="$t('supplierManagement.label.address')"
                  prop="address"
                >
                  <el-input
                    v-model="form.address"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="100"
                    clearable
                    :disabled="isEdit == 'false'"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!-- 可供应商品 -->
                <el-form-item
                  :label="$t('supplierManagement.label.supplyGoods')"
                  prop="categoryList"
                >
                  <el-button
                    type="primary"
                    plain
                    class="select_goods"
                    @click="openGoodsDialog"
                    :disabled="isEdit == 'false'"
                  >
                    <el-icon><Plus /></el-icon>
                    {{ $t("supplierManagement.button.goodTypeBtn") }}
                  </el-button>
                  <div v-if="form.categoryList && form.categoryList.length > 0">
                    <span
                      v-for="(item, index) in form.categoryList"
                      :key="index"
                      class="goods_item"
                    >
                      {{ item.thirdCategoryName }}
                    </span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="contactInformation item_content">
            <div class="title">
              {{ $t("supplierManagement.label.contacts") }}
            </div>
            <el-row>
              <el-col :span="6">
                <!-- 负责人 -->
                <el-form-item
                  :label="$t('supplierManagement.label.curator')"
                  prop="legalPerson"
                >
                  <el-input
                    v-model="form.legalPerson"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-row>
                  <el-col :span="14">
                    <!-- 手机号码 -->
                    <el-form-item
                      :label="$t('supplierManagement.label.mobileNumber')"
                      prop="countryAreaCode"
                      class="custom-select"
                    >
                      <el-select
                        v-model="form.countryAreaCode"
                        :placeholder="$t('supplierManagement.label.areaCode')"
                        clearable
                      >
                        <el-option
                          v-for="(item, index) in areaList"
                          :key="index"
                          :label="item.internationalCode"
                          :value="item.internationalCode"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <el-form-item prop="mobile" label-width="0">
                      <el-input
                        v-model="form.mobile"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                        maxlength="30"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              <div class="flex_style">
                <div>
                  {{ $t("supplierManagement.label.associatedWarehouse") }}
                </div>
                <div>
                  <el-button
                    type="primary"
                    key="primary"
                    text
                    @click="handleWarehouseSelect"
                    :disabled="isEdit == 'false'"
                  >
                    <span class="required_style">
                      {{ $t("supplierManagement.button.warehouseBtn") }}
                    </span>
                  </el-button>
                </div>
              </div>
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 关联仓库列表 -->
                <el-form-item label-width="0" prop="warehouseIds">
                  <el-table
                    :data="form.warehouseList"
                    v-loading="formLoading"
                    stripe
                    v-if="form.warehouseList && form.warehouseList.length > 0"
                  >
                    <el-table-column
                      :label="$t('supplierManagement.label.warehouseName')"
                      prop="warehouseName"
                    />
                    <el-table-column
                      :label="$t('supplierManagement.label.addressW')"
                      prop="warehouseAddress"
                    />
                    <el-table-column
                      prop="contactPerson"
                      :label="$t('supplierManagement.label.contactsPerson')"
                    >
                      <template #default="scope">
                        {{ scope.row.contactPerson }}
                        <!-- <EncryptPhone
                          :nameType="true"
                          :name="scope.row.contactPerson"
                        /> -->
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="mobile"
                      :label="$t('supplierManagement.label.contactInformation')"
                      min-width="70"
                    >
                      <template #default="scope">
                        <!-- <EncryptPhone :phone="scope.row.mobile" /> -->
                        <span class="encryptBox">
                          {{ scope.row.countryAreaCode }}
                          <span
                            v-if="
                              scope.row.mobile && scope.row.mobile.length <= 4
                            "
                          >
                            {{ scope.row.mobile }}
                          </span>
                          <span v-else>
                            {{ scope.row.mobile }}
                            <el-icon
                              v-if="scope.row.mobile"
                              @click="
                                scope.row.mobilePhoneShow
                                  ? getRealPhone(
                                      scope.row.warehouseId,
                                      scope.$index
                                    )
                                  : ''
                              "
                              class="encryptBox-icon"
                              color="#762ADB "
                              size="16"
                            >
                              <component
                                :is="scope.row.mobilePhoneShow ? 'View' : ''"
                              />
                            </el-icon>
                          </span>
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')">
                      <template #default="scope">
                        <el-button
                          type="danger"
                          size="small"
                          link
                          @click="handleDeleteWarehouse(scope.$index)"
                          :disabled="isEdit == 'false'"
                        >
                          {{ $t("common.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              <div class="flex_style">
                <div>
                  {{ $t("supplierManagement.label.contractInformation") }}
                </div>
                <div>
                  <el-button
                    type="primary"
                    key="primary"
                    text
                    @click="handleContractSelect"
                  >
                    {{ $t("supplierManagement.button.contractsBtn") }}
                  </el-button>
                </div>
              </div>
            </div>
            <el-row v-if="form.contractList && form.contractList.length > 0">
              <el-col :span="24">
                <!-- 关联合同列表 -->
                <el-form-item prop="contractList" label-width="0">
                  <el-table
                    :data="form.contractList"
                    stripe
                    v-loading="formLoading"
                  >
                    <el-table-column
                      :label="$t('supplierManagement.label.contractName')"
                      prop="contractName"
                    />
                    <el-table-column
                      :label="$t('supplierManagement.label.signatory')"
                      prop="contractPartner"
                    />
                    <el-table-column
                      :label="$t('supplierManagement.label.contractCode')"
                      prop="contractCode"
                    />
                    <el-table-column
                      :label="$t('supplierManagement.label.contractPeriod')"
                    >
                      <template #default="scope">
                        {{ parseDateTime(scope.row.startDate, "date") }}
                        {{ t("supplierManagement.label.to") }}
                        {{ parseDateTime(scope.row.endDate, "date") }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('supplierManagement.label.createTime')"
                    >
                      <template #default="scope">
                        <span>
                          {{ parseDateTime(scope.row.signDate, "date") }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')">
                      <template #default="scope">
                        <el-button
                          type="danger"
                          size="small"
                          link
                          @click="handleDeleteContract(scope.$index)"
                        >
                          {{ $t("common.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="qualificationInformation item_content">
            <div class="title">
              {{ $t("supplierManagement.label.financeInformation") }}
            </div>
            <el-row>
              <el-col :span="6">
                <!-- 收款方姓名 -->
                <el-form-item
                  :label="$t('supplierManagement.label.payeeName')"
                  prop="payeeName"
                >
                  <el-input
                    type="text"
                    v-model="form.payeeName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 收款方账户 -->
                <el-form-item
                  :label="$t('supplierManagement.label.payeeAccount')"
                  prop="payeeAccount"
                >
                  <el-input
                    type="text"
                    v-model="form.payeeAccount"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 收款开户行 -->
                <el-form-item
                  :label="$t('supplierManagement.label.receivingBank')"
                  prop="payeeBank"
                >
                  <el-input
                    type="text"
                    v-model="form.payeeBank"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 公司名称 -->
                <el-form-item
                  :label="$t('supplierManagement.label.corporateName')"
                  prop="companyName"
                >
                  <el-input
                    type="text"
                    v-model="form.companyName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="200"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <!-- 公司地址 -->
                <el-form-item
                  :label="$t('supplierManagement.label.companyAddress')"
                  prop="companyAddress"
                >
                  <el-input
                    type="text"
                    v-model="form.companyAddress"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="200"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 统一社会信用代码 -->
                <el-form-item
                  :label="$t('supplierManagement.label.creditCode')"
                  prop="creditCode"
                >
                  <el-input
                    type="text"
                    v-model="form.creditCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="35"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 财务联系人 -->
                <el-form-item
                  :label="$t('supplierManagement.label.financialContactPerson')"
                  prop="financialContact"
                >
                  <el-input
                    type="text"
                    v-model="form.financialContact"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 开票类型 -->
                <el-form-item
                  :label="$t('supplierManagement.label.billingType')"
                  prop="ticketTypeCode"
                >
                  <el-select
                    v-model="form.ticketTypeCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in ticketTypeList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <!-- 结算币种 -->
                <el-form-item
                  :label="$t('supplierManagement.label.settlementCurrency')"
                  prop="settlementCurrency"
                >
                  <el-select
                    v-model="form.settlementCurrency"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in currencyList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!-- 营业执照 -->
                <el-form-item
                  :label="$t('supplierManagement.label.businessLicense')"
                  prop="businessLicenseUrl"
                >
                  <upload-multiple
                    :modelValue="form.businessLicenseUrl"
                    @update:model-value="onChangeMultiple"
                    ref="detailPicsRef"
                    :limit="1"
                    :formRef="formUpdateRef"
                    class="modify-multipleUpload"
                    name="detailPic"
                    :isDelete="type == 'details' ? false : true"
                  >
                    <template #default="{ file }">点击上传</template>
                  </upload-multiple>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!-- 附件上传 -->
                <el-form-item
                  :label="$t('supplierManagement.label.attachmentUpload')"
                  prop="attachment"
                >
                  <upload-multiple
                    :listType="`text`"
                    :tips="''"
                    :fileSize="10"
                    :fileType="[
                      'rar',
                      'zip',
                      'docx',
                      'xls',
                      'xlsx',
                      'pdf',
                      'jpg',
                      'png',
                      'jpeg',
                    ]"
                    :modelValue="form.attachment"
                    @update:model-value="onFileChangeMultiple"
                    ref="detailPicsRef"
                    :limit="1"
                    :formRef="formFileRef"
                    class="modify-multipleUpload"
                    name="detailPic"
                  >
                    <template #default="{ file }">点击上传</template>
                  </upload-multiple>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="tradingAndLogistics item_content">
            <div class="title">
              {{ $t("supplierManagement.label.settlementCycle") }}
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 出账周期 -->
                <el-form-item
                  :label="$t('supplierManagement.label.paymentCycle')"
                  prop="enableSettlement"
                >
                  <el-switch
                    :active-text="$t('supplierManagement.label.open')"
                    :inactive-text="$t('supplierManagement.label.close')"
                    inline-prompt
                    style="
                      --el-switch-on-color: #762ADB ;
                      --el-switch-off-color: #cccfd5;
                    "
                    v-model="form.enableSettlement"
                    :active-value="1"
                    :inactive-value="0"
                    @change="settlementChange"
                  />
                  <div
                    v-if="form.enableSettlement == 1"
                    flex
                    class="settlement_style"
                  >
                    <span class="tip1">每</span>
                    <el-input
                      type="number"
                      min="1"
                      oninput="this.value = this.value.replace(/[^0-9]/g, '');if (this.value < 1) this.value = '';"
                      v-model="form.settlementPeriod"
                      class="h-[32px] settlementPeriod"
                    />
                    <el-select
                      v-model="form.settlementPeriodType"
                      :placeholder="$t('common.placeholder.selectTips')"
                      class="h-[32px] settlementPeriodType"
                      @change="settlementPeriodTypeChange"
                    >
                      <el-option
                        v-for="(item, index) in settlementPeriodTypeList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                    <!-- 如果选周 -->
                    <el-select
                      v-model="form.settlementDay"
                      :placeholder="$t('common.placeholder.selectTips')"
                      v-if="form.settlementPeriodType == 2"
                      class="h-[32px]"
                    >
                      <el-option
                        v-for="(item, index) in weekDays"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                    <!-- 如果选月 -->
                    <el-select
                      v-model="form.settlementDay"
                      :placeholder="$t('common.placeholder.selectTips')"
                      v-if="form.settlementPeriodType == 1"
                      class="h-[32px]"
                    >
                      <el-option
                        v-for="day in generateDays()"
                        :key="day"
                        :label="day"
                        :value="day"
                      />
                    </el-select>
                    <span class="tip2">生成应付账单</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="bankInformation">
            <div class="title">
              {{ $t("supplierManagement.label.accountInformation") }}
            </div>
            <el-row justify="space-between">
              <el-col :span="10">
                <!-- 登录账号 -->
                <el-form-item
                  :label="$t('supplierManagement.label.loginAccount')"
                  prop="loginAccountName"
                >
                  <el-input
                    type="text"
                    v-model="form.loginAccountName"
                    :placeholder="$t('supplierManagement.message.accountTip')"
                    disabled
                    class="!w-[260px]"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <!-- 登录密码 -->
                <el-form-item
                  :label="$t('supplierManagement.label.password')"
                  prop="loginAccountPassword"
                >
                  <el-input
                    type="text"
                    v-model="form.loginAccountPassword"
                    :placeholder="$t('common.placeholder.inputTips')"
                    show-password
                    disabled
                    class="!w-[260px]"
                  />
                  <el-form>
                    <el-button
                      @click="updatePassword"
                      type="primary"
                      style="margin-left: 16px"
                      v-if="type != 'add'"
                    >
                      {{ $t("supplierManagement.button.updatePassword") }}
                    </el-button>
                  </el-form>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>

      <div class="page-footer">
        <div class="operation">
          <el-button type="info" @click="handleCancel" v-if="type == 'details'">
            {{ $t("supplierManagement.button.close") }}
          </el-button>
          <el-button type="info" @click="handleCancel" v-if="type != 'details'">
            {{ $t("supplierManagement.button.cancel") }}
          </el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit"
            v-if="type != 'details'"
          >
            {{ $t("supplierManagement.button.submit") }}
          </el-button>
        </div>
      </div>
    </div>

    <AddGoods
      ref="addGoodsRef"
      v-model:visible="goodsDialog.visible"
      :title="goodsDialog.title"
      @on-submit="onSubmitGoods"
    />

    <AddWarehouse
      ref="addWarehouseRef"
      v-model:visible="warehouseDialog.visible"
      :title="warehouseDialog.title"
      @on-submit="onSubmitWarehouse"
    />

    <AddContract
      ref="addContractRef"
      v-model="contractDialog.visible"
      :title="contractDialog.title"
      @on-submit="onSubmitContract"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "AddSupplier",
  inheritAttrs: false,
});

import supplierAPI, { SupplierForm } from "@/modules/pms/api/supplier";
import CommonAPI from "@/modules/wms/api/common";
import ShippingReceiptAPI from "@/modules/pms/api/shippingReceipt";
import UserAPI from "@/core/api/accountManagement";
import SelAreaCascader from "@/modules/pms/components/SelAreaCascader.vue";
import AddGoods from "./components/addGoods.vue";
import AddWarehouse from "./components/addWarehouse.vue";
import AddContract from "./components/addContract.vue";
import type { CascaderProps } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { parseDateTime } from "@/core/utils/index.js";
import { el, fa } from "element-plus/es/locale";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const type = route.query.type;
const supplierId = route.query.supplierId;
const isEdit = route.query.isEdit;
const addGoodsRef = ref();
const addWarehouseRef = ref();
const addContractRef = ref();
const areaRef = ref();
const formUpdateRef = ref();
const formRef = ref(ElForm);
const formFileRef = ref();
const areaList = ref([]);
const loading = ref(false);
const formLoading = ref(false);
const countryList = ref([]);
// const cityList = ref([]);
const purchaserList = ref([]);
const provinceList = ref([]);
const cityList = ref([]);
const districtList = ref([]);
const showCityInput = ref(false);
const showDistrictInput = ref(false);

const goodsDialog = reactive({
  title: "",
  visible: false,
});

const warehouseDialog = reactive({
  title: "",
  visible: false,
});

const contractDialog = reactive({
  title: "",
  visible: false,
});
const fileType = ["jpg", "png", "jpeg", "doc", "docx", "pdf", "xls", "xlsx"];

// 角色表单
const form = reactive<SupplierForm>({
  id: "",
  userId: "",
  supplierName: "",
  supplierTypeCode: "",
  countryName: "",
  countryCode: "",
  cityCode: "",
  cityName: "",
  provinceCode: "",
  provinceName: "",
  districtCode: "",
  districtName: "",
  address: "",
  supplierShortName: "",
  purchaserId: "",
  legalPerson: "",
  countryAreaCode: "",
  mobile: "",
  payeeName: "",
  payeeAccount: "",
  payeeBank: "",
  companyName: "",
  companyAddress: "",
  creditCode: "",
  financialContact: "",
  ticketTypeCode: "",
  settlementCurrency: "",
  businessLicenseUrl: [],
  attachment: [],
  enableSettlement: 0,
  loginAccountName: "",
  loginAccountPassword: "******",
  areaInfo: [],
  categoryList: [],
  warehouseList: [],
  contractIds: [],
  contractList: [],
  warehouseIds: [],
  settlementPeriodType: 2,
  settlementPeriod: undefined,
  settlementDay: undefined,
});
const supplierTypeList = ref([
  {
    code: 1,
    name: t("supplierManagement.label.generalTaxpayer"),
  },
  {
    code: 2,
    name: t("supplierManagement.label.smallScaleTaxpayer"),
  },
  {
    code: 3,
    name: t("supplierManagement.label.individualBusiness"),
  },
]);
const weekDays = ref([
  { value: 1, label: "一" },
  { value: 2, label: "二" },
  { value: 3, label: "三" },
  { value: 4, label: "四" },
  { value: 5, label: "五" },
  { value: 6, label: "六" },
  { value: 7, label: "日" },
]);

const settlementPeriodTypeList = ref([
  {
    value: 2,
    label: t("supplierManagement.label.week"),
  },
  {
    value: 1,
    label: t("supplierManagement.label.month"),
  },
]);

const rules = reactive({
  supplierName: [
    {
      required: true,
      message: t("supplierManagement.rules.supplierNameTip"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  supplierTypeCode: [
    {
      required: true,
      message: t("supplierManagement.rules.supplierTypeTip"),
      trigger: "change",
    },
  ],
  countryCode: [
    {
      required: true,
      message: t("supplierManagement.rules.countryTip"),
      trigger: "change",
    },
  ],
  areaInfo: [
    {
      required: true,
      message: t("supplierManagement.rules.areaTip"),
      trigger: "blur",
    },
  ],
  address: [
    {
      required: true,
      message: t("supplierManagement.rules.addressTip"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  supplierShortName: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  categoryList: [
    {
      required: true,
      message: t("supplierManagement.rules.supplyGoodsTip"),
      trigger: ["change", "blur"],
    },
  ],
  purchaserId: [
    {
      required: true,
      message: t("supplierManagement.rules.purchaserTip"),
      trigger: "change",
    },
  ],
  legalPerson: [
    {
      required: true,
      message: t("supplierManagement.rules.curatorTip"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  countryAreaCode: [
    {
      required: true,
      message: t("supplierManagement.rules.areaTips"),
      trigger: "change",
    },
  ],
  mobile: [
    {
      required: true,
      message: t("supplierManagement.rules.phoneTip"),
      trigger: "blur",
    },
    // {
    //   min: 4,
    //   message: t("supplierManagement.rules.mobileLengthTips"),
    //   trigger: "blur",
    // },
  ],
  payeeName: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  payeeAccount: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  warehouseIds: [
    {
      required: true,
      message: t("supplierManagement.rules.associatedWarehouseTip"),
      trigger: ["blur", "change"],
    },
  ],
  contractList: [
    {
      required: true,
      message: t("supplierManagement.rules.relatedContractsTip"),
      trigger: "change",
    },
  ],
  payeeBank: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  companyName: [
    {
      required: false,
      message: t("supplierManagement.rules.corporateNameTip"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  // companyAddress: [
  //   {
  //     required: true,
  //     message: t("supplierManagement.rules.companyAddressTip"),
  //     trigger: "blur",
  //   },
  // ],
  creditCode: [
    {
      required: false,
      message: t("supplierManagement.rules.creditCodeTip"),
      trigger: "blur",
    },
    {
      pattern: /^[a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.creditCodeFomart"),
      trigger: "blur",
    },
  ],
  financialContact: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("supplierManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  // settlementCurrency: [
  //   {
  //     required: true,
  //     message: t("supplierManagement.rules.settlementCurrencyTip"),
  //     trigger: "change",
  //   },
  // ],
  // businessLicenseUrl: [
  //   {
  //     required: true,
  //     message: t("supplierManagement.rules.businessLicenseTip"),
  //     trigger: "change",
  //   },
  // ],
  // attachment: [
  //   {
  //     required: true,
  //     message: t("supplierManagement.rules.attachmentUploadTip"),
  //     trigger: ["blur", "change"],
  //   },
  // ],
  // loginAccountName: [
  //   {
  //     required: true,
  //     message: t("supplierManagement.rules.loginAccountTip"),
  //     trigger: "blur",
  //   },
  // ],
  // loginAccountPassword: [
  //   {
  //     required: true,
  //     message: t("supplierManagement.rules.passwordTip"),
  //     trigger: "blur",
  //   },
  // ],
});

const ticketTypeList = ref([
  {
    code: "1",
    name: t("supplierManagement.label.specialTicket"),
  },
  {
    code: "2",
    name: t("supplierManagement.label.generalTicket"),
  },
]);

const currencyList = ref([
  {
    code: "CNY",
    name: t("supplierManagement.label.RMB"),
  },
  {
    code: "USD",
    name: t("supplierManagement.label.dollar"),
  },
]);

function generateDays() {
  let days = [];
  for (let i = 1; i <= 31; i++) {
    days.push(`${i}`);
  }
  return days;
}
function onChangeMultiple(val: any) {
  form.businessLicenseUrl = val;
}

function onFileChangeMultiple(val: any) {
  form.attachment = val ? val : "";
}

/** 获取国家列表 */
function queryAllCountry() {
  const queryParams = {
    pid: "0",
  };
  CommonAPI.getAreaList(queryParams)
    .then((data: any) => {
      countryList.value = data;
    })
    .finally(() => {});
}

function countryChange(val: any) {
  // cityList.value = [];
  // form.areaInfo = [];
  if (type == "add") {
    form.provinceCode = "";
    provinceList.value = [];
    form.cityCode = "";
    cityList.value = [];
    form.districtCode = "";
    districtList.value = [];
    showCityInput.value = false;
    showDistrictInput.value = false;
    form.areaInfo = [];
    form.provinceName = "";
    form.cityName = "";
    form.districtName = "";
    if (form.countryCode) {
      let data: any = countryList.value.find(
        (item: any) => item.id === form.countryCode
      );
      form.countryName = data.shortName ? data.shortName : "";
      let params = {
        pid: val,
      };
      CommonAPI.getAreaList(params)
        .then((data: any) => {
          provinceList.value = data;
          if (form.provinceCode) {
            // form.areaInfo[0] = form.provinceCode;
          }
          // checkAddress();
        })
        .finally(() => {});
    }
  }
}

function handleProvinceChange(val: any) {
  if (type == "add") {
    form.cityCode = "";
    cityList.value = [];
    form.districtCode = "";
    districtList.value = [];
    showCityInput.value = false;
    showDistrictInput.value = false;
    form.areaInfo = [];
    let data: any = provinceList.value.find(
      (item: any) => item.id === form.provinceCode
    );
    form.provinceName = data.shortName ? data.shortName : "";
    let params = {
      pid: val,
    };
    CommonAPI.getAreaList(params)
      .then((data: any) => {
        if (data.length > 0) {
          cityList.value = data;
          showCityInput.value = true;
          form.areaInfo[1] = form.cityCode;
        } else {
          showCityInput.value = false;
        }
        // checkAddress();
      })
      .finally(() => {});
  }
}

function handleCityChange(val: any) {
  if (type == "add") {
    form.districtCode = "";
    districtList.value = [];
    showDistrictInput.value = false;
    form.areaInfo[1] = "";
    let data: any = cityList.value.find(
      (item: any) => item.id === form.cityCode
    );
    form.cityName = data.shortName ? data.shortName : "";
    let params = {
      pid: val,
    };
    CommonAPI.getAreaList(params)
      .then((data: any) => {
        if (data.length > 0) {
          showDistrictInput.value = true;
          form.areaInfo[2] = form.districtCode;
          districtList.value = data;
        } else {
          showDistrictInput.value = false;
        }
        // checkAddress();
      })
      .finally(() => {});
  }
}

function handleDistrictChange(val: any) {
  let data: any = districtList.value.find(
    (item: any) => item.id === form.districtCode
  );
  form.districtName = data.shortName ? data.shortName : "";
}

function checkAddress() {
  if (form.provinceCode && showCityInput.value == false) {
    form.areaInfo[0] = form.provinceCode;
  } else if (
    form.provinceCode &&
    form.cityCode &&
    showDistrictInput.value == false
  ) {
    form.areaInfo[0] = form.provinceCode;
    form.areaInfo[1] = form.cityCode;
  } else if (form.provinceCode && form.cityCode && form.districtCode) {
    form.areaInfo[0] = form.provinceCode;
    form.areaInfo[1] = form.cityCode;
    form.areaInfo[2] = form.districtCode;
  } else {
    form.areaInfo = [];
  }
}

// function handleCascaderChange() {
//   let values = areaRef.value.getCheckedNodes();
//   if (values && values.length) {
//     const pathLabels = values[0].pathLabels;
//     const pathValues = values[0].pathValues;
//     pathLabels.forEach((value: any, index: any) => {
//       if (index === 0) {
//         form.provinceCode = pathValues[index];
//         form.provinceName = value;
//       } else if (index === 1) {
//         form.cityCode = pathValues[index];
//         form.cityName = value;
//       } else if (index === 2) {
//         form.districtCode = pathValues[index];
//         form.districtName = value;
//       }
//     });
//   }
// }

// function clickNode(e: any) {
//   // 模拟点击对应的radio
//   e.target.parentElement.parentElement.firstElementChild.click();
// }
// const areaProps: CascaderProps = {
//   label: "shortName",
//   value: "id",
//   lazy: true,
//   checkStrictly: false,

//   lazyLoad(node, resolve) {
//     const { level } = node;
//     if (form.countryCode && form.countryCode.length > 0) {
//       const queryParams: any = {
//         pid: form.countryCode,
//       };
//       // queryParams.pid = countryInfo.value;
//       if (level > 0) {
//         queryParams.pid = node.value;
//       }
//       CommonAPI.getAreaList(queryParams)
//         .then((data: any) => {
//           if (data.length) {
//             data.map((item: any) => {
//               if (level >= 2) {
//                 item.leaf = true;
//               }
//             });
//           } else {
//             if (node.children.length < 1) {
//               // node.data.leaf = true;
//               data = [{ leaf: true, shortName: node.label }];
//             }
//             // data = [
//             //   {
//             //     id: queryParams.pid,
//             //     pid: queryParams.pid,
//             //     shortName: "暂无数据",
//             //     level: level,
//             //     leaf: true,
//             //   },
//             // ];
//             // if (node.label == "暂无数据") {
//             //   data.map((item: any) => {
//             //     console.log("level", level);
//             //     console.log("item.level", item.level);
//             //     if (level >= item.level - 1) {
//             //       item.leaf = true;
//             //     }
//             //   });
//             // }
//           }

//           resolve(data);
//         })
//         .finally(() => {});
//     }
//   },
// };

// 获取区号
function getAreaList() {
  supplierAPI
    .getAllCountry()
    .then((data: any) => {
      areaList.value = data;
    })
    .finally(() => {});
}

function getRealPhone(id: any, index: any) {
  supplierAPI
    .queryWarehouseRealPhone({ id: id })
    .then((data) => {
      form.warehouseList[index].mobile = data;
      form.warehouseList[index].mobilePhoneShow = false;
    })
    .finally(() => {});
}

// 获取采购员下拉列表
function queryPurchaserList() {
  ShippingReceiptAPI.getPurchaserList({})
    .then((data: any) => {
      purchaserList.value = data;
    })
    .finally(() => {});
}

// 选择商品分类
function openGoodsDialog() {
  goodsDialog.title = t("supplierManagement.title.addGoods");
  if (form.categoryList && form.categoryList.length > 0) {
    addGoodsRef.value.setFormData(form.categoryList);
  }
  addGoodsRef.value.getGoodsList();
  goodsDialog.visible = true;
}

// 选择仓库
function handleWarehouseSelect() {
  warehouseDialog.title = t("supplierManagement.title.addWarehouse");
  let ids: any = [];
  if (form.warehouseList && form.warehouseList.length > 0) {
    form.warehouseList.forEach((item: any) => {
      ids.push(item.warehouseId);
      item.mobilePhoneShow = true;
    });
  }
  // form.warehouseIds = ids;
  addWarehouseRef.value.setFormData(ids);
  addWarehouseRef.value.getWarehouseList();
  warehouseDialog.visible = true;
}

// 合同
function onSubmitContract(data: any) {
  form.contractList = form.contractList.concat(data);
  let ids: any = [];
  if (form.contractList && form.contractList.length > 0) {
    form.contractList.forEach((item: any) => {
      ids.push(item.contractId);
    });
  }
  form.contractIds = form.contractIds.concat(ids);
  form.contractIds = form.contractIds.filter(
    (item: any, index: any) => form.contractIds.indexOf(item) === index
  );
}

function onSubmitGoods(data: any) {
  form.categoryList = data;
  let ids: any = [];
  if (form.categoryList && form.categoryList.length > 0) {
    form.categoryList.forEach((item: any) => {
      ids.push(item.value);
    });
  }
  form.thirdCategoryIds = ids;
}

function onSubmitWarehouse(data: any) {
  form.warehouseList = form.warehouseList.concat(data);
  let ids: any = [];
  if (form.warehouseList && form.warehouseList.length > 0) {
    form.warehouseList.forEach((item: any) => {
      ids.push(item.warehouseId);
      item.mobilePhoneShow = true;
    });
  }
  form.warehouseIds = form.warehouseIds.concat(ids);
  form.warehouseIds = form.warehouseIds.filter(
    (item: any, index: any) => form.warehouseIds.indexOf(item) === index
  );
}

// 删除仓库
function handleDeleteWarehouse(index?: number) {
  form.warehouseIds.splice(index, 1);
  form.warehouseList.splice(index, 1);
}

// 选择合同
function handleContractSelect() {
  contractDialog.title = t("supplierManagement.title.addContracts");
  let ids: any = [];
  if (form.contractList && form.contractList.length > 0) {
    form.contractList.forEach((item: any) => {
      ids.push(item.contractId);
    });
  }
  // form.contractIds = ids;
  addContractRef.value.setFormData(ids);
  addContractRef.value.getContractList();
  contractDialog.visible = true;
}

// 删除合同
function handleDeleteContract(index?: number) {
  form.contractIds.splice(index, 1);
  form.contractList.splice(index, 1);
}

// 更改密码
function updatePassword() {
  ElMessageBox.confirm(
    t("accountManagement.message.resetPasswordTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
    let params = {
      userId: form.userId,
    };
    UserAPI.resetPassword(params).then((res: any) => {
      ElMessage.success(t("accountManagement.message.resertPasswordSucess"));
    });
  });
}

const handleCancel = async () => {
  form.categoryList = [];
  await tagsViewStore.delView(route);
  router.push({
    path: "/pms/supplier/supplierManagement",
  });
};

function settlementChange() {
  form.settlementPeriod = undefined;
  form.settlementPeriodType = 2;
  form.settlementDay = undefined;
}

function settlementPeriodTypeChange() {
  form.settlementDay = undefined;
}

// 提交
function handleSubmit() {
  console.log(form);
  checkAddress();
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    loading.value = true;
    const dataCopy = JSON.parse(JSON.stringify(form));
    if (dataCopy && Array.isArray(dataCopy.attachment)) {
      dataCopy.attachment = JSON.stringify(dataCopy.attachment);
    }
    if (dataCopy && Array.isArray(dataCopy.businessLicenseUrl)) {
      dataCopy.businessLicenseUrl = JSON.stringify(dataCopy.businessLicenseUrl);
    }
    // form.businessLicenseUrl = JSON.stringify(form.businessLicenseUrl);
    // form.attachment = JSON.stringify(form.attachment);
    let params = {
      ...dataCopy,
    };
    delete params.contractList;
    delete params.warehouseList;
    delete params.categoryList;
    delete params.areaInfo;
    console.info(params);
    if (type == "add") {
      delete params.id;
      supplierAPI
        .add(params)
        .then((data: any) => {
          ElMessage.success(t("supplierManagement.message.addSucess"));
          loading.value = false;
          handleCancel();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      supplierAPI
        .update(params)
        .then((data: any) => {
          ElMessage.success(t("supplierManagement.message.editSucess"));
          loading.value = false;
          handleCancel();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function querySupplierDetail() {
  formLoading.value = true;
  let params = {
    supplierId: supplierId,
  };
  supplierAPI
    .querySupplierDetail(params)
    .then((data: any) => {
      Object.assign(form, data);
      countryChange(form.countryCode);
      let contractIds: any = [];
      if (form.contractList && form.contractList.length > 0) {
        form.contractList.forEach((item: any) => {
          contractIds.push(item.contractId);
        });
      }
      form.contractIds = contractIds;

      let warehouseIds: any = [];
      if (form.warehouseList && form.warehouseList.length > 0) {
        form.warehouseList.forEach((item: any) => {
          warehouseIds.push(item.warehouseId);
          item.mobilePhoneShow = true;
        });
      }
      form.warehouseIds = warehouseIds;

      let thirdCategoryIds: any = [];
      if (form.categoryList && form.categoryList.length > 0) {
        form.categoryList.forEach((item: any) => {
          thirdCategoryIds.push(item.thirdCategoryId);
        });
      }
      form.thirdCategoryIds = thirdCategoryIds;
      form.areaInfo[0] = form.provinceCode ? form.provinceCode : "";
      form.areaInfo[1] = form.cityCode ? form.cityCode : "";
      form.areaInfo[2] = form.districtCode ? form.districtCode : "";
      if (form.provinceCode) {
        let obj: any = {
          id: form.provinceCode ? form.provinceCode : "",
          shortName: form.provinceName ? form.provinceName : "",
        };
        provinceList.value.push(obj);
      }
      if (form.cityCode) {
        showCityInput.value = true;
        let obj: any = {
          id: form.cityCode ? form.cityCode : "",
          shortName: form.cityName ? form.cityName : "",
        };
        cityList.value.push(obj);
      }
      if (form.districtCode) {
        showDistrictInput.value = true;
        let obj: any = {
          id: form.districtCode ? form.districtCode : "",
          shortName: form.districtName ? form.districtName : "",
        };
        districtList.value.push(obj);
      }

      if (typeof form.attachment === "string") {
        form.attachment = JSON.parse(form.attachment); // 数据库中存储的imagesUrl是一个序列化后的对象数组字符串
      }
      if (typeof form.businessLicenseUrl === "string") {
        form.businessLicenseUrl = JSON.parse(form.businessLicenseUrl); // 数据库中存储的imagesUrl是一个序列化后的对象数组字符串
      }
    })
    .finally(() => {
      formLoading.value = false;
    });
}

onMounted(() => {
  queryAllCountry();
  getAreaList();
  queryPurchaserList();
  if (type == "edit" || type == "details") {
    querySupplierDetail();
  }
});
</script>
<style scoped lang="scss">
.addSupplier {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 13px 21px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 20px 9px 20px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .select_goods:hover {
      color: var(--el-color-primary);
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
    .goods_item {
      background: #f8f9fc;
      border-radius: 2px;
      border: 1px solid #d7dbdf;
      padding: 6px 12px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #52585f;
      margin-left: 8px;
      box-sizing: border-box;
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 9px 20px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .settlement_style {
    .tip1 {
      padding: 0px 8px 0px 24px;
    }
    .tip2 {
      padding-left: 8px;
      width: 400px;
    }
    .settlementPeriod {
      width: 200px;
    }
    .settlementPeriodType {
      width: 200px;
      :deep(.el-select__wrapper) {
        background: #f8f9fc !important;
      }
    }
  }
  .encryptBox {
    // display: inline-flex;
    // justify-content: space-between;
    // align-items: center;
    word-wrap: break-word;
    word-break: break-all;
  }

  .encryptBox-icon {
    margin-left: 4px;
    cursor: pointer;
    // align-self: flex-start;
    vertical-align: text-top;
  }
  .required_style::before {
    color: var(--el-color-danger);
    content: "*";
    margin-right: 4px;
  }
}
</style>
<style scoped>
/* ::v-deep .custom-select .el-select {
  background: #f8f9fc !important;
  border-radius: 2px 0px 0px 2px !important;
} */
::v-deep .custom-select .el-select__wrapper {
  background: #f8f9fc !important;
  /* border-radius: 2px 0px 0px 2px !important;
  width: 120px !important; */
}
</style>
