<template>
  <div class="review-detail">
    <el-card>
      <div class="card-header mb-20px">
        <el-row>
          <el-col :span="2">
            <div class="purchase">
              <span class="purchase-status purchase-status-color1" v-if="contractDetail.approveStatus==1">{{$t('contract.label.pendingReview')}}</span>
              <span class="purchase-status purchase-status-color5" v-if="contractDetail.approveStatus==3">{{$t('contract.label.rejected')}}</span>
              <span class="purchase-status purchase-status-color3" v-if="contractDetail.approveStatus==2">{{$t('contract.label.agreed')}}</span>
            </div>
          </el-col>
          <el-col :span="4" v-if="contractDetail.approveStatus==2 || contractDetail.approveStatus==3">
            <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.auditPerson") }}
                </span>
              <span class="form-text">{{ contractDetail.approveUserName }}</span>
            </div>
          </el-col>
          <el-col :span="6" v-if="contractDetail.approveStatus==2 || contractDetail.approveStatus==3">
            <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.auditTime") }}
                </span>
              <span class="form-text">{{ parseTime(contractDetail.approveTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
            </div>
          </el-col>
          <el-col :span="12" v-if="contractDetail.approveStatus==2 || contractDetail.approveStatus==3">
            <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.auditRemark") }}
                </span>
              <span class="form-text">{{ contractDetail.approveRemark?contractDetail.approveRemark:'-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="section">
        <div class="card-title mb-16px">
          {{ t("contract.label.basicInfo") }}
        </div>
        <div class="info-container">
          <el-row :gutter="20" class="info-row">
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.contractName") }}
                </span>
                <span class="form-text">{{ contractDetail.contractName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.signType") }}
                </span>
                <span class="form-text">
                  {{
                    [
                      t("contract.label.typeSign"),
                      t("contract.label.typeRenew"),
                    ][contractDetail.signType]
                  }}
                </span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.contractCode") }}
                </span>
                <span class="form-text">{{ contractDetail.contractCode }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.contractOwner") }}
                </span>
                <span class="form-text">
                  {{ contractDetail.contractOwner }}
                </span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="info-row">
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.contractPartner") }}
                </span>
                <span class="form-text">
                  {{ contractDetail.contractPartner }}
                </span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.contractAmount") }}
                </span>
                <span class="form-text">
                  ¥{{ contractDetail?.contractAmount || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.contractDeposit") }}
                </span>
                <span class="form-text">
                  ¥{{ contractDetail?.contractDeposit || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.salesPerson") }}
                </span>
                <span class="form-text">
                  {{ contractDetail.salespersonUserName }}
                </span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="info-row">
            <el-col :span="12">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.dateRange") }}
                </span>
                <span class="form-text">
                  {{ parseTimeHandle(contractDetail.startDate)
                  }}{{ t("contract.label.rangeSeparator")
                  }}{{ parseTimeHandle(contractDetail.endDate) }}
                </span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.signDate") }}
                </span>
                <span class="form-text">
                  {{ parseTimeHandle(contractDetail.signDate) }}
                </span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.enableProductSwitch") }}
                </span>
                <span class="form-text">
                  {{ contractDetail.enableProductSwitch?$t('contract.label.open'):$t('contract.label.close') }}
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="info-row">
            <el-col :span="24">
              <div class="info-item">
                <span class="form-label">
                  {{ t("contract.label.contractNote") }}
                </span>
                <span class="form-text">{{ contractDetail.contractNote }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="custom-border"></div>
      <div class="section">
        <div class="card-title mb-16px mt-20px">
          {{ t("contract.label.attachmentInfo") }}
        </div>
        <el-row :gutter="20" class="info-row">
          <el-col :span="24">
            <div class="info-item" style="display: flex; align-items: center">
              <span class="form-label">
                {{ t("contract.label.attachment") }}
              </span>
              <UploadMultiple
                :showTip="false"
                :showUploadBtn="false"
                ref="detailPicsRef"
                v-model="contractDetail.attachment"
                :tips="''"
                :limit="10"
                :formRef="formRef"
                listType="text"
                :fileType="filtType"
                class="modify-multipleUpload"
                name="detailPic"
                actionType="preview"
              />
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="custom-border"></div>
      <div v-if="contractDetail.enableProductSwitch">
        <div class="section">
          <div class="card-title mb-16px mt-20px">
            {{ t("contract.label.enableProductSwitch") }}
          </div>
          <el-row :gutter="20" class="info-row">
            <el-col :span="24">
              <div class="info-item" style="display: flex">
                <span class="form-label">
                  {{ t("contract.label.productDetail") }}
                </span>
                <el-table :data="contractDetail.contractProudctList">
                  <el-table-column type="index" :label="$t('contract.label.sort')" width="60"></el-table-column>
                  <el-table-column prop="productName" :label="$t('contract.label.productName')"></el-table-column>
                  <el-table-column prop="productUnitName" :label="$t('contract.label.productUnit')"></el-table-column>
                  <!--<el-table-column prop="unitAmount" :label="$t('contract.label.price')">
                    <template #default="scope">
                      <span v-if="scope.row.saleCurrency == 'CNY'">￥{{scope.row.unitAmount}}</span>
                      <span v-else>${{scope.row.unitAmount}}</span>
                    </template>
                  </el-table-column>-->
                </el-table>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="custom-border"></div>
      </div>
      <div class="section text-right" style="margin-top: 13px;padding-right: 20px;">
        <el-button @click="handleClose">{{ t("common.reback") }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { getContractDetail } from "@pms/api/contract";
  import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
  import { parseTime } from "@/core/utils";
  import { useTagsViewStore } from "@/core/store/modules/tagsView";
  import { useI18n } from "vue-i18n"; // 导入国际化

  // 使用国际化
  const { t } = useI18n();

  interface ContractDetail {
    contractName: string; // 合同名称
    contractCode: string; // 合同编码
    contractOwner: string; // 签订我方
    contractPartner: string; // 签订对方
    contractAmount: number; // 合同金额
    contractDeposit: number; // 合同保证金
    startDate: string; // 合同开始日期
    endDate: string; // 合同结束日期
    signDate: string; // 签订日期
    contractNote: string; // 合同说明
    contractStatus: number; // 合同状态
    contractType: number; // 合同类型
    signType: number; // 签订类型
    attachment: string; // 附件
    salespersonUserName: string;
    enableProductSwitch: number;
  }

  const route = useRoute();
  const contractDetail = ref<ContractDetail>({
    contractName: "",
    contractCode: "",
    contractOwner: "",
    contractPartner: "",
    contractAmount: 0,
    contractDeposit: 0,
    startDate: "",
    endDate: "",
    signDate: "",
    contractNote: "",
    contractStatus: 0,
    contractType: 0,
    signType: 0,
    attachment: "",
    salespersonUserName: '',
    enableProductSwitch:0,
    contractProudctList:[],
  });
  const filtType = [
    "png",
    "jpg",
    "jpeg",
    "pdf",
    "xlsx",
    "xls",
    "docx",
    "zip",
    "rar",
  ];
  const fetchContractDetail = async () => {
    try {
      const { contractId } = route.query;
      const data = await getContractDetail({ contractId });
      data.attachment = data.attachment ? JSON.parse(data.attachment) : []; // 合同附件只有一份
      contractDetail.value = data;
    } catch (error) {
      console.error("获取合同详情失败:", error);
    }
  };

  const previewFile = reactive({
    name: "",
    url: "",
  });

  const previewSingleHandle = async (
    bucket: string,
    fileName: string,
    originalFileName: string
  ) => {
    try {
      const res = await previewSingle(bucket, fileName, originalFileName);
      console.log("previewFile------", res);
      previewFile.name = res.name;
      previewFile.url = res.url;
    } catch (error) {
      ElMessage.error(t("contract.message.previewFailed"));
    }
  };

  const router = useRouter();
  const tagsViewStore = useTagsViewStore();
  const handleClose = async () => {
    await tagsViewStore.delView(route);
    router.push("/pms/contract/contractReview");
  };

  const parseTimeHandle = (time: string) => {
    return parseTime(time, "{y}-{m}-{d}");
  };
  onMounted(() => {
    fetchContractDetail();
  });
</script>

<style scoped lang="scss">
  .form-label {
    margin-right: 8px;
  }
  .review-detail {
    .back-button {
      margin-bottom: 20px;
      cursor: pointer;

      span {
        color: #333;
      }
    }

    .section {
      margin-bottom: 13px;
      .mb-16px{
        margin-bottom: 16px !important;
      }
      .mt-20px{
        margin-top: 20px;
      }
    }

    .card-header {
      padding: 0px 10px 13px;
      box-sizing: border-box;
      background: #ffffff;
      box-shadow: inset 0px -1px 0px 0px #e5e7f3;
      border-radius: 4px 4px 0px 0px;
      cursor: pointer;
      .code {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #151719;
        line-height: 24px;
        text-align: left;
        font-style: normal;
      }
      .back-btn {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
      .status {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #151719;
        line-height: 24px;
        text-align: left;
        font-style: normal;
      }
    }
    .info-container {
      .info-row {
        margin-bottom: 0px;
        &:last-child {
          margin-bottom: 0;
        }
      }

      .info-item {
        display: flex;
        /*align-items: center;*/
        gap: 12px;

        .form-label {
          min-width: 80px;
          text-align: right;
          color: #606266;
        }

        .form-text {
          flex: 1;
          color: #303133;
          word-break: break-all;
        }
      }
    }

    .attachments {
      .attachment-item {
        padding: 8px 0;
        color: #333;

        &::before {
          content: "*";
          color: #ff4d4f;
          margin-right: 4px;
        }
      }
    }
  }
</style>
<style lang="scss">
  .review-detail{
    .el-card__body{
      padding: 13px 20px 0px 20px !important;
    }
  }
</style>
