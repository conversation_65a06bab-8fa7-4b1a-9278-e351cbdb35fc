<template>
  <div class="add-product-div">
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" size="850px" @close="close" class="add-product">
      <div>
        <el-form ref="productFromRef" label-width="80px" :model="productFrom" :inline="true">
          <el-form-item prop="productName" :label="$t('contract.label.product')">
            <el-input
              v-model="productFrom.productName"
              :placeholder="$t('contract.placeholder.productName')"
              clearable
              class="!w-[256px]"
            />
          </el-form-item>
          <el-form-item prop="productCategory" :label="$t('contract.label.productCategory')">
            <el-cascader v-model="productFrom.productCategory"
                         :options="categoryList"
                         :props="propsCategory"
                         @change="handleChange"
                         ref="cascaderRef"
                         filterable
                         class="!w-[256px]"
                         :placeholder="$t('common.placeholder.selectTips')"
                         clearable />
          </el-form-item>
          <el-form-item prop="productCode" :label="$t('contract.label.productCode')">
            <el-input
              v-model="productFrom.productCode"
              :placeholder="$t('contract.placeholder.productCode')"
              clearable
              class="!w-[256px]"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="queryProductAll">
              {{$t('common.search')}}
            </el-button>
            <el-button  @click="reset">
              {{$t('common.reset')}}
            </el-button>
          </el-form-item>
        </el-form>
        <el-table
          v-loading="loading"
          :data="productTable"
          highlight-current-row
          stripe
          @selection-change="handleSelectionSupplierChange"
        >
          <el-table-column type="selection" width="60" align="center"/>
          <el-table-column :label="$t('contract.label.product')" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <div class="product-div">
                <!--<div class="picture">
                  <img :src="scope.row.mainImageUrl" alt="">
                </div>-->
                <div class="product">
                  <div class="product-code">
                    <span class="product-key">{{$t('contract.label.productCode')}}：</span>
                    <span class="product-value">{{scope.row.productCode}}</span>
                  </div>
                  <div class="product-name">{{scope.row.productName}}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('contract.label.productCategory')" prop="fullCategoryName" min-width="150" show-overflow-tooltip/>
          <el-table-column :label="$t('contract.label.productUnit')" prop="productUnitName" show-overflow-tooltip/>
          <el-table-column :label="$t('contract.label.price')" prop="saleAmount" show-overflow-tooltip/>

        </el-table>
        <div class="pagination-container">
          <pagination
            v-if="productTotal > 0"
            v-model:total="productTotal"
            v-model:page="productFrom.page"
            v-model:limit="productFrom.limit"
            @pagination="queryProductAll"
          />
        </div>
        <div>{{ $t("contract.message.selectNumTips")}}<span class="select-num">{{multipleSelectionSupplier.length}}</span></div>
      </div>
      <template #footer>
            <span class="dialog-footer">
              <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
              <el-button type="primary" :loading="submitLoading" @click="submitForm" :disabled="multipleSelectionSupplier.length==0">{{ $t("common.confirm") }}</el-button>
            </span>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
  import PurchaseAPI, { PurchasePageQuery,PurchasePageVO} from "@/modules/goods/api/purchase";
  import type { CascaderProps } from 'element-plus';
  import productCategoryAPI from "@/modules/pms/api/productCategory";
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
  });
  const emit = defineEmits(["update:visible","onSubmit"]);
  const { t } = useI18n();

  const  supplierType= ref()
  const  ids = ref([])
  const  submitLoading= ref(false)
  const loading = ref(false);
  const productTotal = ref(0);
  const productFromRef = ref();
  const multipleSelectionSupplier = ref([]);
  const productTable = ref<PurchasePageVO[]>()
  const cascaderRef = ref();
  const categoryList = ref([])
  const propsCategory: CascaderProps = {
    checkStrictly : true,
    value: 'id',
    label: 'categoryName',
    children: 'children',
  }
  let productFrom = reactive<PurchasePageQuery>({
    status:1,
    page: 1,
    limit: 20,
  });

  function close() {
    emit("update:visible", false);
    productFrom.productName = '';
    productFrom.productCode = '';
    productFrom.productCategory = [];
    productFrom.firstCategoryId = '';
    productFrom.secondCategoryId = '';
    productFrom.thirdCategoryId = '';
    productFrom.page = 1;
    productFrom.limit = 20;
  }

  function reset() {
    productFrom.productName = '';
    productFrom.productCode = '';
    productFrom.productCategory = [];
    productFrom.firstCategoryId = '';
    productFrom.secondCategoryId = '';
    productFrom.thirdCategoryId = '';
    productFrom.page = 1;
    productFrom.limit = 20;
    queryProductAll()
  }

  function handleChange(){
    if(cascaderRef.value.getCheckedNodes()){
      let valueArr = cascaderRef.value.getCheckedNodes()[0].pathValues
      productFrom.firstCategoryId = valueArr[0];
      productFrom.secondCategoryId = valueArr[1];
      productFrom.thirdCategoryId = valueArr[2];
      productFrom.productCategory = valueArr;
    }
  }

  function handleSelectionSupplierChange(val) {
    multipleSelectionSupplier.value = val;
  }

  function submitForm() {
    submitLoading.value = true;
    const  collection = multipleSelectionSupplier.value
    close();
    submitLoading.value = false;
    emit("onSubmit",collection);
  }

  function queryProductAll(){
    loading.value = true;
    submitLoading.value=true
    let data = {
      ...productFrom
    }
    delete data.productCategory
    PurchaseAPI.getPurchaerSelectPage(data)
      .then((data) => {
        productTable.value = data.records;
        productTotal.value = parseInt(data.total);
      })
      .finally(() => {
        loading.value = false;
        submitLoading.value=false
      });
  }

  function setFormData() {
    productFrom.productName = '';
    productFrom.productCategory = [];
    productFrom.firstCategoryId = '';
    productFrom.secondCategoryId = '';
    productFrom.thirdCategoryId = '';
    queryProductAll()
  }


  /** 查询商品分类列表 */
  function queryManagerCategoryList(id?:any) {
    productCategoryAPI.queryCategoryTreeList({}).then((data: any) => {
      categoryList.value = data;
    })
  }

  defineExpose({
    setFormData,
    queryManagerCategoryList,
  });
</script>

<style scoped lang="scss">
  .add-product{
    .supplier-div{
      width: calc(100% - 170px);
    }
    .product-div{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .picture{
        margin-right: 16px;
        img{
          width: 80px;
          height: 80px;
        }
      }
      .product{
        font-family: PingFangSC, PingFang SC;
        font-style: normal;
        .product-code{
          font-weight: 400;
          font-size: 14px;
          color: #90979E;
        }
        .product-name{
          font-weight: 500;
          font-size: 14px;
          color: #52585F;
        }
      }
    }
    .select-num{
      margin-left: 8px;
      font-size: 18px;
      color:var(--el-color-primary)
    }

  }
</style>
<style lang="scss">
  .add-product-div{
    .el-drawer__body{
      overflow: hidden;
      .el-table{
        height: calc(100% - 175px);
        overflow: auto;
      }
    }
    .el-drawer__body>*{
      height: 100%;
    }
  }
</style>
