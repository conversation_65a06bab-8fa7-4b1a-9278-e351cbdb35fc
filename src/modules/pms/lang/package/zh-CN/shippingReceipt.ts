export default {
  shippingReceipt: {
    label: {
      receiveTransportCode: "收运单号",
      purchaseCode: "采购单号",
      supplier: "供应商",
      buyerName: "采购员",
      receiveTransportDate: "收运时间",
      receiveTransportCount: "收运数量",
      productType: "商品种类",
      createUserName: "收运人",
      remark: "备注",
      purchaseType: "采购类型",
      productCount: "商品数量",
      receivedQuantity: "已收数量",
      quantityToBeReceived: "待收数量",
      purchaseStatus: "采购状态",
      source: "单据来源",
      plannedDeliveryDate: "计划交货日期",
      createTime: "创建时间",
      unitName: "采购单位",
      plannedQuantity: "计划采购量",
      receivedGoodsCount: "已收货量",
      receivedGoodsAmount: "已收货金额",
      receivedCount: "本次收货量",
      unitPriceReceivedThisTime: "本次收货单价",
      receivedAmount: "本次收货金额",
      creator: "制单人",
      jin: "斤",
      total: "合计",
      includedTax: "本次收货金额(含税)",
      productName: "商品名称",
      productUnit:'单位',
      price:'单价',
      receiverCount:  "收运量",
      receiverCoast: "收货金额(元)",
      productSpec: "规格",
      quantity:  "数量",
      proportion: "占比",
      deductionAmount: "扣款金额",
      deductionDesc: "扣款说明",
      attachment: "附件",
      inspectionTableTitle: "产品入库质检信息",
      specification: "规格",
    },
    button: {
      add: "添加收运单",
      viewBtn: "查看",
      operate: "收运",
      allReceivedBtn: "全部收货",
      partialReceiptBtn: "部分收货",
      close: "关闭",
      confirmBtn: "确认",
      confirmTitle: "确认价格",
    },
    title: {
      addTitle: "添加收运单",
      detailsTitle: "收运单详情",
      allReceivedGoods: "全部收货",
      confirmTitle: "确认收运单",
    },
    message: {
      receiveTips: "确定当前采购单全部收货吗？",
      receiveSectionTips:"确定当前采购单部分收货吗？",
      receiveSucess: "收货成功",
      receiveCancel: "取消收货",
      confirmSuccess: "确认成功",
    },
    placeholder: {
      inputTips: "自动计算",
    },
    rules: {
      unitPriceFormat: "请输入大于0的数字，支持小数点前8位后3位",
    }
  },
};
