export default {
  payableAggregation: {
    label: {
      payableAccountTypeOption: {
        1: "供应商直供",
        2: "市场自采",
      },
      supplierName: "供应商",
      purchaseUserName: "采购员",

      supplierCode: "供应商编码",
      payableAmount: "应付金额",
      actualPayAmount: "实付金额",
      remainingPayAmount: "剩余应付金额",

      operator: "操作",
      summation: "合计",

      totalPayableAmount: "应付总额",
      totalAmountPaid: "已付金额",
      totaResiduePayableAmount: "剩余应付",
      paymentTime:"付款时间",

      startDate: "开始日期",
      endDate: "结束日期",
      rangeSeparator: "至",

      paymentUserName: "付款人",
      paymentAmount: "付款金额",
      paymentMethod: "付款方式",
      paymentVoucher: "付款凭证",
      remark:"备注",



      orderTime: "下单时间",

      purchaseOrderCode: "采购单号",
      purchaseTheme:"采购主题",

      plannedPurchaseAmount: "计划采购金额",
      receivingAmount: "收货金额",
      deductionAmount: "扣减金额",
      refundAmount:"退单金额",



     tabsTypeOption: {
        1: "付款记录",
        2: "采购单",
      },

    },
    title: {
      details: "详情",
    },

    button: {
      actionPay: "付款",
      actionDetail: "详情",
      actionView: "查看",
    },
    placeholder: {},
    message: {
      actionSuccess: "操作成功",
    },
    rules: {
      amount:"支持整数9位小数2位且大于0",
    },
    dialog: {},
  },
};
