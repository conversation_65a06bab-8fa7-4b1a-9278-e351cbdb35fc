import CommonAPI from "@/modules/wms/api/common";
import { useI18n } from "vue-i18n";

export const useConvertProductUnit = () => {
  const { t } = useI18n();
  // 商品一级单位转二级单位
  async function convertProductUnit(data: any) {
    const res = await CommonAPI.convertProductUnit(data);
    return res?.convertedValue;
  }

  /** 
  一级单位增减 isDiscreteUnit: 1开启，0关闭
  输入若商品的【一级单位增减isDiscreteUnit】为【是】      
    输入数量时，转换量自动转换      
    输入转换量时，数量不管，用户手动填写
  若商品的【一级单位增减】为【否】       
    输入数量时，转换量内无值时自动转换，有值时不转换       
    输入转换量时，数量内无值时自动转换，且数量向上取整，有值时不转换 
  */
  //  qtyKey 一级单位key，weightKey 二级单位key，unitPriceKey 单价key
  async function convertProductUnitStragery(row: any, stragetry: string, qtyKey = "actualInQty", weightKey = "actualInWeight", calcAmout= true) {
    const data = {
      convertUnitTypeEnum: stragetry,
      originalValue: null,
      productId: row.productId || row.id,
      productCode: row.productCode,
    }
    if (stragetry == "FIRST_TO_SECOND") {
      if (row.isDiscreteUnit == 1) {
        if (!isNull(row[qtyKey])) {
          data.originalValue = row[qtyKey];
          const val = await convertProductUnit(data);
          row[weightKey] = parseFloat(val).toFixed(3);
        }
      }
      else if (row.isDiscreteUnit == 0) {
        if (isNull(row[weightKey]) && !isNull(row[qtyKey])) { // 输入数量时，转换量内无值时自动转换，有值时不转换
          data.originalValue = row[qtyKey];
          const val = await convertProductUnit(data);
          row[weightKey] = parseFloat(val).toFixed(3);
        }
      }
    }
    else if (stragetry == "SECOND_TO_FIRST") {
      if (row.isDiscreteUnit == 0) {
        if (isNull(row[qtyKey]) && !isNull(row[weightKey])) { // 输入数量时，转换量内无值时自动转换，有值时不转换
          data.originalValue = row[weightKey];
          const val = await convertProductUnit(data);
          row[qtyKey] = Math.ceil(val);
        }
      }
    }
    if (calcAmout) {
      calculateAmount(row, qtyKey, weightKey, "unitPrice");
    }
  }


  // 判断值部位null，undefined，''
  function isNull(val: any) {
    return val === null || val === undefined || val === '';
  }
  // 单价+量+转换量:计算金额
  async function calculateAmount(row: any, qtyKey = "actualInQty", weightKey = "actualInWeight", unitPriceKey="unitPrice") {
    if (isNull(row[qtyKey]) || isNull(row[weightKey]) || isNull(row[unitPriceKey])) return;
    const data = {
      convertedQty: row[weightKey],
      productId: row.productId,
      qty: row[qtyKey],
      unitPrice: row[unitPriceKey],
      productCode: row.productCode,
    }
    const res = await CommonAPI.calculateAmount(data);
    row.amount = res?.amount;
  }

  return {
    convertProductUnit,
    convertProductUnitStragery,
    calculateAmount,
  }
}