import request from "@/core/utils/request";
import {ReportLossOrderFrom} from "@/modules/wms/api/reportLossOrder";

const PURCHASE_BASE_URL = "/supply-wms/disassemblyOrder";
const PRODUCTSTOCK_BASE_URL = "/supply-wms/productStock";

class ProductDisassemblyAssembleOrderAPI {

    /** 获取拆装单号下拉数据 (报损单创建页面)*/
    static getDisassemblyAssembleOrderList() {
        return request({
            url: `${PURCHASE_BASE_URL}/codeSelect`,
            method: "post",
            data: {},
        });
    }

    /** 根据拆装单号查询报损商品列表(报损单创建页面) */
    static queryLossProductListByOrderId(data:{disassemblyOrderCode?:string,id?:string}) {
        return request<any, PageResult<ProductVO[]>>({
            url: `${PURCHASE_BASE_URL}/queryLossProductListByOrderCode`,
            method: "post",
            data: data,
        });
    }

    /** 获取拆装单分页数据 */
    static getProductDisassemblyAssembleOrderPage(queryParams?: ProductDisassemblyAssembleOrderPageQuery) {
        return request<any, PageResult<ProductDisassemblyAssembleOrderPageVO[]>>({
            url: `${PURCHASE_BASE_URL}/page`,
            method: "post",
            data: queryParams,
        });
    }

    /**删除拆装单 */
    static deleteProductDisassemblyAssembleOrder(data: { id?:string }) {
        return request({
            url: `${PURCHASE_BASE_URL}/delete`,
            method: "post",
            data: data,
        });
    }

    /**暂存拆装单 */
    static saveProductDisassemblyAssembleOrder(data: ProductDisassemblyAssembleOrderFrom) {
        return request({
            url: `${PURCHASE_BASE_URL}/save`,
            method: "post",
            data: data,
        });
    }

    /**提交拆装单 */
    static submitProductDisassemblyAssembleOrder(data: ProductDisassemblyAssembleOrderFrom) {
        return request({
            url: `${PURCHASE_BASE_URL}/submit`,
            method: "post",
            data: data,
        });
    }

    /** 拆装单详情 */
    static getProductDisassemblyAssembleOrderDetail(data:{id?:string}) {
        return request<any, ProductDisassemblyAssembleOrderFrom>({
            url: `${PURCHASE_BASE_URL}/detail`,
            method: "post",
            data: data,
        });
    }
  /** 拆装单详情 */
  static getProductDisassemblyAssembleOrderDetail(data:{id?:string}) {
    return request<any, ProductDisassemblyAssembleOrderFrom>({
      url: `${PURCHASE_BASE_URL}/detail`,
      method: "post",
      data: data,
    });
  }
/** 领单 **/
  static pickOrder(data:{id?:string}) {
    return request<any>({
      url: `${PURCHASE_BASE_URL}/pickOrder`,
      method: "post",
      data: data,
    });
  }
  /** 取消领单 **/
  static releaseOrder(data:{id?:string}) {
    return request<any>({
      url: `${PURCHASE_BASE_URL}/releaseOrder`,
      method: "post",
      data: data,
    });
  }

  /** 商品ysn列表 **/
  static queryProductYsnList(data:any) {
    return request<any>({
      url: `${PURCHASE_BASE_URL}/queryProductYsnList`,
      method: "post",
      data: data,
    });
  }

  // 查询分拣单下商品
  static queryProductOfPickOrders(sortingCode: string){
    return request<any>({
      url: `/supply-wms/sorting/detail/${sortingCode}`,
      method: "get",
    });
  }

  /*拆装模板列表*/
  static queryTemplateList(){
    return request<any>({
      url: `/supply-wms/disassemblyTemplate/queryListExcludeProduct `,
      method: "get",
    });
  }
  /*查询拆装商品列表*/
  static queryDisassemblyProductDetailList(data: any){
    return request<any>({
      url: `${PURCHASE_BASE_URL}/queryCalculatedProductQuantity`,
      method: "post",
      data: data,
    });
  }
  /*查询商品库存信息*/
  static queryProductWarehouseInfo(data: {productCode?: string , warehouseAreaCode?: string}){
    return request({
      url: `${PRODUCTSTOCK_BASE_URL}/queryAreaProductStockQty`,
      method: 'post',
      data: data
    })
  }
}

export default ProductDisassemblyAssembleOrderAPI;

/** 拆装单分页查询参数 */
export interface ProductDisassemblyAssembleOrderPageQuery extends PageQuery {
    /** 拆装单号 */
    disassemblyOrderCode?: string;
    /** 拆装类型  (1->商品组合，2->商品拆分) */
    orderTypeList?: number[];
    /** 状态 (0->草稿，1->拆装完成)*/
    orderStatusList?: number[];
    /** 时间类型(1->创建时间，2->拆装时间)*/
    dateType?: number;
    /** 时间范围 */
    dateRange?: string[];
}

/** 拆装单分页对象 */
export interface ProductDisassemblyAssembleOrderPageVO {
    /** ID */
    id?: string;
    /** 拆装单号 */
    disassemblyOrderCode?: string;
    /** 拆装类型  (1->商品组合，2->商品拆分) */
    orderType?: number;
    /** 目标商品数 */
    targetProductQty?: number;
    /** 源商品数 */
    sourceProductQty?: number;
    /** 报损单号 */
    lossOrderCode?: string;
    /** 备注 */
    remark?: string;
    /** 拆装人 */
    disassemblerName?: string;
    /** 拆装时间 */
    disassemblyTime?: string;
    /** 创建人 */
    createUserName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 状态 (0->草稿，1->拆装完成)*/
    orderStatus?: number;
}

/**根据拆装单号查询报损商品对象(报损单创建页面) */
export interface ProductVO{
    /** ID */
    id?: string;
    /** 商品编码 */
    productCode?: string;
    /** 商品名称 */
    productName?: string;
    /** 商品规格 */
    productSpec?: string;
    /** 商品单位 */
    productUnitName?: string;
    /** 数量 */
    totalStockQty?: number;
    /** 可用库存 */
    availableStockQty?: number;
    /** 源出库库区编码 */
    sourceWarehouseAreaId?: string;
    /** 源出库库区名称 */
    sourceWarehouseAreaName?: string;
    /** 源出库数量 */
    disassemblyInQty?: number;
    /** 目标入库库区编码 */
    targetWarehouseAreaId?: string;
    /** 目标入库库区名称 */
    targetWarehouseAreaName?: string;
    /** 入库数量 */
    disassemblyOutQty?: number;
}

/** 拆装单对象 */
export interface ProductDisassemblyAssembleOrderFrom{
    /** ID */
    id?: string;
    /** 拆装单号 */
    disassemblyOrderCode?: string;
    /** 拆装类型  (1->商品组合，2->商品拆分) */
    orderType?: number;
    /*拆装模板id*/
    disassemblyTemplateId: string;
    /*拆装模板编码*/
    disassemblyTemplateCode: string;
    /*拆装模板名称*/
    disassemblyTemplateName: string;
    /** 备注 */
    remark?: string;
    /** 拆装人 */
    disassemblerName?: string;
    /** 拆装时间 */
    disassemblyTime?: string;
    /** 创建人 */
    createUserName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 状态 (0->草稿，1->拆装完成)*/
    orderStatus?: number;
    /** 拆装单源数据明细 */
    sourceList?: ProductList[];
    /** 拆装单目标数据明细 */
    targetList?: ProductList[];
    /** 入库通知单 */
    receiptNoticeCode?: string;
    sourceOrderCode?: string;
    /** 入库类型（入库通知单-16） */
    sourceOrderType?: number;
    /*拆装模板*/
    disassemblyTemplateId?: string;
    /*计算数量*/
    calculateNum?: number;
}
/** 拆装单对象(产品对象) */
export interface ProductList{
    /** ID */
    id?: string;
    /** 商品编码 */
    productCode?: string;
    /** 商品名称 */
    productName?: string;
    /** 商品规格 */
    productSpec?: string;
    /** 商品单位 */
    productUnitName?: string;
    /** 数量 */
    totalStockQty?: number;
    /** 可用库存 */
    availableStockQty?: number;
    /** 源出库库区编码 */
    sourceWarehouseAreaId?: string;
    /** 源出库库区名称 */
    sourceWarehouseAreaName?: string;
    /** 源出库数量 */
    disassemblyOutQty?: number;
    /** 目标入库库区编码 */
    targetWarehouseAreaId?: string;
    /** 目标入库库区名称 */
    targetWarehouseAreaName?: string;
    /** 入库数量 */
    disassemblyInQty?: number;
    /** 入库通知单 */
    receiptNoticeCode?: string;
    /** 入库类型（入库通知单-16） */
    sourceOrderType?: number;
}
