import request from "@/core/utils/request";

const TRANSFER_ORDER_BASE_URL = "/supply-wms/transferOrder";

class TransferOrderAPI {

  /** 分页查询调拨单列表 */
  static getTransferOrderPage(queryParams?: TransferOrderPageQuery) {
    return request<any, PageResult<TransferOrderListVO[]>>({
      url: `${TRANSFER_ORDER_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }

  /** 新增调拨单 */
  static saveTransferOrder(data: TransferOrderSaveForm) {
    return request<any, string>({
      url: `${TRANSFER_ORDER_BASE_URL}/save`,
      method: "post",
      data: data,
    });
  }

  /** 删除调拨单 */
  static deleteTransferOrder(transferOrderCode: string) {
    return request<any, string>({
      url: `${TRANSFER_ORDER_BASE_URL}/delete/${transferOrderCode}`,
      method: "post",
      params: { transferOrderCode },
    });
  }

  /** 获取调拨单详情 */
  static getTransferOrderDetail(transferOrderCode: string) {
    return request<any, TransferOrderDetailVO>({
      url: `${TRANSFER_ORDER_BASE_URL}/get/${transferOrderCode}`,
      method: "get",
      params: { transferOrderCode },
    });
  }

  /** 完结调拨单 */
  static completeTransferOrder(transferOrderCode: string) {
    return request<any, string>({
      url: `${TRANSFER_ORDER_BASE_URL}/complete/${transferOrderCode}`,
      method: "get",
      params: { transferOrderCode },
    });
  }

  /** 获取仓库列表 */
  static getWarehouseList() {
    return request<any, WarehouseVO[]>({
      url: "/supply-wms/warehouse/list",
      method: "post",
      data: {},
    });
  }

  /** 获取配送方式列表 */
  static getDeliveryMethodList() {
    return request<any, DeliveryMethodVO[]>({
      url: "/supply-biz-common/deliveryMethods/queryPageList",
      method: "post",
      data: {},
    });
  }

  /** 获取商品列表（用于选择商品） */
  static getProductSelectPage(queryParams?: ProductSelectPageQuery) {
    return request<any, PageResult<ProductSelectVO[]>>({
      url: "/supply-biz-common/product/product/selectPage",
      method: "post",
      data: queryParams,
    });
  }

  /** 获取地址信息 */
  static getAreaList(pid: string = "0") {
    return request<any, AreaVO[]>({
      url: "/supply-base/area/queryAreaListByPid",
      method: "get",
      params: { pid },
    });
  }

  /**
 * 获取销售人员|业务员|获取所有用户列表
 */
  static queryAllUser() {
    return request({
      url: `/supply-base/user/all`,
      method: "get",
    });
  }

  // 获取仓库详情
  static getWarehouseDetail(warehouseCode: string) {
    return request<any, WarehouseVO>({
      url: `/supply-wms/warehouse/detail/${warehouseCode}`,
      method: "get"
    });
  }

   //配送方式
   static queryDeliveryMethodList(data?: any){
      return request({
        url: `/supply-biz-common/deliveryMethods/queryPageList`,
        method: "post",
        data: data
      })
    }

  // 查询库存
  static queryStock(data?: any){
    return request({
      url: `/supply-wms//productStock/queryWarehouseProductStockQtyList`,
      method: "post",
      data: data
    })
  }

  // 获取仓库详情
}

export default TransferOrderAPI;

/** 调拨单分页查询参数 */
export interface TransferOrderPageQuery {
  /** 页码 */
  pageNum?: number;
  /** 页大小 */
  pageSize?: number;
  /** 调拨单编号 */
  transferOrderCode?: string;
  /** 调出仓库编码 */
  sourceWarehouseCode?: string;
  /** 调入仓库编码 */
  targetWarehouseCode?: string;
  /** 调拨单状态：0=草稿 1=初始 2=调拨中 3=完结 */
  transferOrderStatus?: number[];
  /** 调拨单主题描述 */
  transferOrderName?: string;
  /** 查询时间类型 1:申请时间 2:出库确认时间 3:入库确认时间 4:创建时间 5:最后修改时间 */
  queryTimeType?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 时间范围 */
  dateRange?: string[];
}

/** 调拨单列表对象 */
export interface TransferOrderListVO {
  /** 调拨单id */
  id?: number;
  /** 仓库编码 */
  warehouseCode?: string;
  /** 调拨单编号 */
  transferOrderCode?: string;
  /** 调拨单主题描述 */
  transferOrderName?: string;
  /** 申请人id */
  applicantUserId?: number;
  /** 申请人名称 */
  applicantUserName?: string;
  /** 申请时间 */
  applicantTime?: string;
  /** 调出仓库id */
  sourceWarehouseId?: number;
  /** 调出仓库编码 */
  sourceWarehouseCode?: string;
  /** 调出仓库名称 */
  sourceWarehouseName?: string;
  /** 调入仓库id */
  targetWarehouseId?: number;
  /** 调入仓库编码 */
  targetWarehouseCode?: string;
  /** 调入仓库名称 */
  targetWarehouseName?: string;
  /** 计划调出时间 */
  planTransferOutTime?: string;
  /** 计划调入时间 */
  planTransferInTime?: string;
  /** 计划配送方式：1=快递 2=自提 */
  planDeliveryType?: number;
  /** 计划总量 */
  planTotalQty?: number;
  /** 计划转换量 */
  planConvertedQty?: number;
  /** 出库通知单号 */
  outNoticeOrderCode?: string;
  /** 出库总量 */
  outTotalQty?: number;
  /** 出库转换量 */
  outConvertedQty?: number;
  /** 入库通知单号 */
  inNoticeOrderCode?: string;
  /** 入库总量 */
  inTotalQty?: number;
  /** 入库转换量 */
  inConvertedQty?: number;
  /** 备注 */
  remark?: string;
  /** 出库确认时间 */
  outConfirmTime?: string;
  /** 入库确认时间 */
  inConfirmTime?: string;
  /** 出库确认信息 */
  outConfirmInfo?: string;
  /** 入库确认信息 */
  inConfirmInfo?: string;
  /** 出库状态：0=待出库 1=部分出库 2=已出库 */
  outStatus?: number;
  /** 入库状态：0=待入库 1=部分入库 2=已入库 */
  inStatus?: number;
  /** 调拨单状态：0=草稿 1=初始 2=调拨中 3=完结 */
  transferOrderStatus?: number;
  /** 是否删除：0=未删除 1=已删除 */
  isDeleted?: number;
  /** 创建用户名 */
  createUserName?: string;
  /** 修改用户名 */
  updateUserName?: string;
  /** 创建时间 */
  createTime?: string;
}

/** 调拨单保存表单 */
export interface TransferOrderSaveForm {
  /** 调拨单编号 */
  transferOrderCode?: string;
  /** 调拨单主题描述 */
  transferOrderName?: string;
  /** 申请人id */
  applicantUserId?: number;
  /** 申请人名称 */
  applicantUserName?: string;
  /** 申请时间 */
  applicantTime?: string;
  /** 调出仓库id */
  sourceWarehouseId?: number;
  /** 调出仓库编码 */
  sourceWarehouseCode?: string;
  /** 调出仓库名称 */
  sourceWarehouseName?: string;
  /** 调出国家id */
  sourceCountryId?: string;
  /** 调出国家名称 */
  sourceCountryName?: string;
  /** 调出省份id */
  sourceProvinceId?: string;
  /** 调出省名称 */
  sourceProvinceName?: string;
  /** 调出城市id */
  sourceCityId?: string;
  /** 调出市名称 */
  sourceCityName?: string;
  /** 调出区县id */
  sourceDistrictId?: string;
  /** 调出区县名称 */
  sourceDistrictName?: string;
  /** 调出详细地址 */
  sourceAddress?: string;
  /** 调入仓库id */
  targetWarehouseId?: number;
  /** 调入仓库编码 */
  targetWarehouseCode?: string;
  /** 调入仓库名称 */
  targetWarehouseName?: string;
  /** 调入国家id */
  targetCountryId?: string;
  /** 调入国家名称 */
  targetCountryName?: string;
  /** 调入省份id */
  targetProvinceId?: string;
  /** 调入省名称 */
  targetProvinceName?: string;
  /** 调入城市id */
  targetCityId?: string;
  /** 调入市名称 */
  targetCityName?: string;
  /** 调入区县id */
  targetDistrictId?: string;
  /** 调入区县名称 */
  targetDistrictName?: string;
  /** 调入详细地址 */
  targetAddress?: string;
  /** 计划调出时间 */
  planTransferOutTime?: string;
  /** 计划调入时间 */
  planTransferInTime?: string;
  /** 计划配送方式：1=快递 2=自提 */
  planDeliveryType?: number;
  /** 备注 */
  remark?: string;
  /** 调拨单商品列表 */
  transferOrderDetailList?: TransferOrderDetailForm[];
  /** 调拨单状态：0=草稿 1=提交 */
  transferOrderStatus?: number;
}

/** 调拨单明细表单 */
export interface TransferOrderDetailForm {
  /** 商品编码 */
  productCode?: string;
  /** 计划调拨量 */
  planTransferQty?: number;
  /** 计划调拨转化量 */
  planTransferConverted?: number;
}

/** 调拨单详情对象 */
export interface TransferOrderDetailVO {
  /** 调拨单id */
  id?: number;
  /** 仓库编码 */
  warehouseCode?: string;
  /** 调拨单编号 */
  transferOrderCode?: string;
  /** 调拨单主题描述 */
  transferOrderName?: string;
  /** 申请人id */
  applicantUserId?: number;
  /** 申请人名称 */
  applicantUserName?: string;
  /** 申请时间 */
  applicantTime?: string;
  /** 调出仓库id */
  sourceWarehouseId?: number;
  /** 调出仓库编码 */
  sourceWarehouseCode?: string;
  /** 调出仓库名称 */
  sourceWarehouseName?: string;
  /** 调出国家id */
  sourceCountryId?: string;
  /** 调出国家名称 */
  sourceCountryName?: string;
  /** 调出省份id */
  sourceProvinceId?: string;
  /** 调出省名称 */
  sourceProvinceName?: string;
  /** 调出城市id */
  sourceCityId?: string;
  /** 调出市名称 */
  sourceCityName?: string;
  /** 调出区县id */
  sourceDistrictId?: string;
  /** 调出区县名称 */
  sourceDistrictName?: string;
  /** 调出详细地址 */
  sourceAddress?: string;
  /** 调入仓库id */
  targetWarehouseId?: number;
  /** 调入仓库编码 */
  targetWarehouseCode?: string;
  /** 调入仓库名称 */
  targetWarehouseName?: string;
  /** 调入国家id */
  targetCountryId?: string;
  /** 调入国家名称 */
  targetCountryName?: string;
  /** 调入省份id */
  targetProvinceId?: string;
  /** 调入省名称 */
  targetProvinceName?: string;
  /** 调入城市id */
  targetCityId?: string;
  /** 调入市名称 */
  targetCityName?: string;
  /** 调入区县id */
  targetDistrictId?: string;
  /** 调入区县名称 */
  targetDistrictName?: string;
  /** 调入详细地址 */
  targetAddress?: string;
  /** 计划调出时间 */
  planTransferOutTime?: string;
  /** 计划调入时间 */
  planTransferInTime?: string;
  /** 计划配送方式：1=快递 2=自提 */
  planDeliveryType?: number;
  /** 计划总量 */
  planTotalQty?: number;
  /** 计划转换量 */
  planConvertedQty?: number;
  /** 出库通知单号 */
  outNoticeOrderCode?: string;
  /** 出库总量 */
  outTotalQty?: number;
  /** 出库转换量 */
  outConvertedQty?: number;
  /** 入库通知单号 */
  inNoticeOrderCode?: string;
  /** 入库总量 */
  inTotalQty?: number;
  /** 入库转换量 */
  inConvertedQty?: number;
  /** 备注 */
  remark?: string;
  /** 出库确认时间 */
  outConfirmTime?: string;
  /** 入库确认时间 */
  inConfirmTime?: string;
  /** 出库确认信息 */
  outConfirmInfo?: string;
  /** 入库确认信息 */
  inConfirmInfo?: string;
  /** 出库状态：0=待出库 1=部分出库 2=已出库 */
  outStatus?: number;
  /** 入库状态：0=待入库 1=部分入库 2=已入库 */
  inStatus?: number;
  /** 调拨单状态：0=草稿 1=初始 2=调拨中 3=完结 */
  transferOrderStatus?: number;
  /** 创建时间 */
  createTime?: string;
  /** 创建用户名 */
  createUserName?: string;
  /** 修改用户名 */
  updateUserName?: string;
  /** 调拨单详情列表 */
  detailList?: TransferOrderDetailItemVO[];
  /** 出库单信息 */
  outboundNoticeInfoVO?: any;
  /** 入库单信息 */
  inboundNoticeInfoVO?: any[];
}

/** 调拨单明细项 */
export interface TransferOrderDetailItemVO {
  /** 调拨单id */
  id?: number;
  /** 调拨单id */
  transferOrderId?: number;
  /** 调拨单编号 */
  transferOrderCode?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 商品规格 */
  productSpec?: string;
  /** 计价模式->0:一级单位;1:二级单位 */
  pricingScheme?: number;
  /** 转换量 */
  convertedQty?: number;
  /** 一级单位id */
  productUnitId?: number;
  /** 一级单位名称 */
  productUnitName?: string;
  /** 二级单位id */
  conversionRelSecondUnitId?: number;
  /** 二级单位名称 */
  conversionRelSecondUnitName?: string;
  /** 商品属性 */
  attributeType?: number;
  /** 商品属性值 */
  attributeTypeName?: string;
  /** 一级分类id */
  firstCategoryId?: number;
  /** 二级分类id */
  secondCategoryId?: number;
  /** 三级分类id */
  thirdCategoryId?: number;
  /** 一级分类名称 */
  firstCategoryName?: string;
  /** 二级分类名称 */
  secondCategoryName?: string;
  /** 三级分类名称 */
  thirdCategoryName?: string;
  /** 计划调拨量 */
  planTransferQty?: number;
  /** 计划调拨转化量 */
  planTransferConverted?: number;
  /** 出库量 */
  outQty?: number;
  /** 出库转化量 */
  outConvertedQty?: number;
  /** 入库量 */
  inQty?: number;
  /** 入库转化量 */
  inConvertedQty?: number;
}

/** 仓库对象 */
export interface WarehouseVO {
  /** 仓库id */
  warehouseId?: number;
  /** 仓库编码 */
  warehouseCode?: string;
  /** 仓库名称 */
  warehouseName?: string;
  /** 国家id */
  countryId?: string;
  /** 国家名称 */
  countryName?: string;
  /** 省份id */
  provinceId?: string;
  /** 省名称 */
  provinceName?: string;
  /** 城市id */
  cityId?: string;
  /** 市名称 */
  cityName?: string;
  /** 区县id */
  districtId?: string;
  /** 区县名称 */
  districtName?: string;
  /** 详细地址 */
  address?: string;
}

/** 配送方式对象 */
export interface DeliveryMethodVO {
  /** id */
  id?: number;
  /** 配送方式名称 */
  deliveryMethodName?: string;
  /** 配送方式编码 */
  deliveryMethodCode?: string;
}

/** 商品选择分页查询参数 */
export interface ProductSelectPageQuery extends PageQuery {
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 商品分类id */
  categoryId?: number;
}

/** 商品选择对象 */
export interface ProductSelectVO {
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 商品规格 */
  productSpec?: string;
  /** 一级单位id */
  productUnitId?: number;
  /** 一级单位名称 */
  productUnitName?: string;
  /** 二级单位id */
  conversionRelSecondUnitId?: number;
  /** 二级单位名称 */
  conversionRelSecondUnitName?: string;
  /** 商品属性 */
  attributeType?: number;
  /** 商品属性值 */
  attributeTypeName?: string;
  /** 一级分类id */
  firstCategoryId?: number;
  /** 二级分类id */
  secondCategoryId?: number;
  /** 三级分类id */
  thirdCategoryId?: number;
  /** 一级分类名称 */
  firstCategoryName?: string;
  /** 二级分类名称 */
  secondCategoryName?: string;
  /** 三级分类名称 */
  thirdCategoryName?: string;
  /** 库存数量 */
  stockQty?: number;
  /** 可用库存数量 */
  availableQty?: number;
}

/** 地址对象 */
export interface AreaVO {
  /** 地区id */
  id?: string;
  /** 地区名称 */
  name?: string;
  /** 父级id */
  pid?: string;
  /** 地区编码 */
  code?: string;
  /** 层级 */
  level?: number;
} 
