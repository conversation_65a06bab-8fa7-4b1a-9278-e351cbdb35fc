import request from "@/core/utils/request";

const WAREHOUSR_BASE_URL = "/supply-wms/warehouse";

class WarehouseAPI {
  /** 获取仓库分页数据 */
  static getWarehousePage(queryParams?: PageQuery) {
    return request<any, PageResult<warehouseInfo[]>>({
      url: `${WAREHOUSR_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }
  /** 获取仓库列表 不分页*/
  static getWarehouseList() {
    return request<any, PageResult<warehouseInfo[]>>({
      url: `${WAREHOUSR_BASE_URL}/list`,
      method: "post",
    });
  }
  /** 添加仓库 */
  static addWarehouse(data: warehouseForm) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }
  /** 编辑仓库 */
  static editWarehouse(data: warehouseForm) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/update`,
      method: "post",
      data: data,
    });
  }
  /** 更改仓库状态 */
  static updateStatus(data: { id?: string; status?: number }) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/updateStatus`,
      method: "post",
      data: data,
    });
  }
  /** 删除仓库 */
  static deleteWarehouse(data: { id?: string }) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  // 获取所有部门列表
  static allDeptList() {
    return request({
      url: "/supply-base/base/dept/tree",
      method: "get",
    });
  }
}

export interface warehouseForm {
  id: string;
  warehouseName: string;
  warehouseCode: string;
  address: string;
  countryId: string;
  countryName: string;
  provinceId: string;
  provinceName: string;
  cityId: string;
  cityName: string;
  districtId: string;
  districtName: string;
  contactPerson: string;
  mobile: string;
  contactLandline: string;
  notes: string;
  status: number;
  fullAddress: string;
  countryAreaCode: string;
  fullAddressClone: string;
  deptId: string;
  deptName: string;
}
export interface WarehousePageQuery extends PageQuery {
  /** 角色名称 */
  roleName?: string;
  /** 状态 */
  status?: number;
}

export default WarehouseAPI;
