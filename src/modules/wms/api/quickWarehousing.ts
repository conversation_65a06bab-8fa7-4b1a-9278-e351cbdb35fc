import request from "@/core/utils/request";

const QUICK_WAREHOUSING_BASE_URL = "/supply-wms/warehouseFastEntryOrder";

class QuickWarehousingAPI {
  /** 快速入库分页查询 */
  static queryPageList(queryParams?: QuickWarehousingPageQuery) {
    return request({
      url: `${QUICK_WAREHOUSING_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  static delete(data: any) {
    return request({
      url: `${QUICK_WAREHOUSING_BASE_URL}/deleteFast`,
      method: "post",
      data: data,
    });
  }
  // 详情页面接口
  static queryDetailDisplay(data: any) {
    return request({
      url: `${QUICK_WAREHOUSING_BASE_URL}/queryDetailDisplay`,
      method: "post",
      data: data,
    });
  }
  /** 快速入库详情查询 */
  static queryDetail(data: any) {
    return request({
      url: `${QUICK_WAREHOUSING_BASE_URL}/queryDetail`,
      method: "post",
      data: data,
    });
  }

  /** 快速入库新增 */
  static add(data: QuickWarehousingFormData) {
    return request({
      url: `${QUICK_WAREHOUSING_BASE_URL}/submitFast`,
      method: "post",
      data: data,
    });
  }

  /** 快速入库编辑 */
  static addDraft(data: QuickWarehousingFormData) {
    return request({
      url: `${QUICK_WAREHOUSING_BASE_URL}/saveDraft`,
      method: "post",
      data: data,
    });
  }

  
  
  /** 商品选择页面分页查询 */
  static queryProductPageList(data: any) {
    return request({
      url: `/supply-wms/product/product/page`,
      method: "post",
      data: data,
    });
  }

  /** 获取仓库列表 */
  static getWarehouseList() {
    return request({
      url: `/supply-wms/warehouse/list`,
      method: "post",
      data: {},
    });
  }

  /** 获取库区列表 */
  static getWarehouseAreaList(params: any) {
    return request({
      url: `/supply-wms/storeArea/queryListByCurrentWarehouse`,
      method: "get",
      params,
    });
  }

  /**
 * 获取销售人员|业务员|获取所有用户列表
 */
  static querySalesPersonUser() {
    return request({
      url: `/supply-base/user/all`,
      method: "get",
    });
  }

  /** 获取供应商下拉数据源(采购商品翻页查询使用) */
  static getSupplierListAll(data: any) {
    return request({
        // url: `/supply-pms/supplier/query/select`,
        url: `/supply-pms/supplier/select`,
        method: "post",
        data,
    });
  }

  //  去入库完结
  static goWarehousingComplete(data: any) {
    return request({
      url: `${QUICK_WAREHOUSING_BASE_URL}/completeFast`,
      method: "post",
      data: data,
    });
  }

  // 在快速入库订单详情页面通过收运单编码和商品编码查询质检商品信息
  static queryProductQualityInspectionList(data: any) {
    return request({
      url: `${QUICK_WAREHOUSING_BASE_URL}/queryProductQualityInspectionList`,
      method: "post",
      data: data,
    });
  }

  // 查询商品最新入库单价
  static queryProductLatestUnitPrice(data: any) {
    return request({
      url: `/supply-wms/productStockBatch/queryNewestInPrice`,
      method: "post",
      params: data,
    });
  }

  // 通过入库单编码查询入库通知单id
  static queryReceiptNoticeIdByEntryOrderCode(params: any) {
    // receiptNoticeCode=EN250630S00002
    return request({
      url: `/supply-wms/warehouseFastEntryOrder/queryReceiptNoticeByEntryOrderCode`,
      method: "get",
      params
    });
  }

  // 打印
  static queryPrintDetail(data: any) {
    return request({
      url: `/supply-wms/warehouseFastEntryOrder/queryPrintDetail`,
      method: "post",
      data: data,
    });
  }
}


/**
 * WarehouseEntryNoticeOrderRequestDTO
 */
export interface QuickWarehousingFormData {
  /**
   * 总入库量（实际总量）
   */
  actualTotalQuantity?: number;
  /**
   * 总入库转换量（实际总数量）
   */
  actualTotalWeight?: number;
  /**
   * 详细地址
   */
  address?: string;
  /**
   * 城市id
   */
  cityId?: string;
  /**
   * 市名称
   */
  cityName?: string;
  /**
   * 联系人
   */
  contactPerson?: string;
  /**
   * 合同编码
   */
  contractCode?: string;
  /**
   * 合同名称
   */
  contractName?: string;
  /**
   * 合同类型
   */
  contractType?: number;
  /**
   * 国家区域代码
   */
  countryAreaCode?: string;
  /**
   * 国家id
   */
  countryId?: string;
  /**
   * 国家名称
   */
  countryName?: string;
  createUser?: number;
  createUserName?: string;
  /**
   * 交易币种：CNY->人民币，USD->美元
   */
  currency?: string;
  /**
   * 当前用户id
   */
  currentUserId?: number;
  /**
   * 当前用户名称
   */
  currentUserName?: string;
  /**
   * 客户编码
   */
  customerCode?: string;
  /**
   * 客户名称
   */
  customerName?: string;
  /**
   * 区县id
   */
  districtId?: string;
  /**
   * 区县名称
   */
  districtName?: string;
  /**
   * 计划总数
   */
  expectedQty?: number;
  /**
   * 计划总重量
   */
  expectedWeight?: number;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 每页条数
   */
  limit?: number;
  /**
   * 手机号
   */
  mobile?: string;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 计划交货时间
   */
  plannedDeliveryTime?: string;
  /**
   * 商品列表
   */
  productList?: QuickWarehousingProductVO[];
  /**
   * 商品个数
   */
  productQty?: number;
  /**
   * 省份id
   */
  provinceId?: string;
  /**
   * 省名称
   */
  provinceName?: string;
  /**
   * 采购/销售员
   */
  purchaseSalesPerson?: string;
  /**
   * 商品质检列表
   */
  qualityInspectionList?: WarehouseReceiptNoticeQualityInspectionDTO[];
  /**
   * 查询结束时间
   */
  queryEndTime?: string;
  /**
   * 查询开始时间
   */
  queryStartTime?: string;
  /**
   * 查询类型:1:计划交货时间、2:单据同步时间
   */
  queryType?: number;
  /**
   * 入库通知单号
   */
  receiptNoticeCode?: string;
  /**
   * 入库类型:1:采购入库、2:退货入库
   */
  receiptType?: number;
  /**
   * 入库类型:1:采购入库、2:退货入库
   * 入库类型集合查询:1:采购入库、2:退货入库
   */
  receiptTypeList?: number[];
  /**
   * 收运单号
   */
  receivingOrderCode?: string;
  /**
   * 收运单id
   */
  receivingOrderId?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 业务员ID
   */
  salesmanId?: string;
  /**
   * 业务员姓名
   */
  salesmanName?: string;
  /**
   * 来源：1:手动创建 2:同步
   */
  source?: number;
  /**
   * 来源单号
   */
  sourceOrderCode?: string;
  /**
   * 来源系统
   */
  sourceSystem?: string;
  /**
   * 状态:0:草稿、1:初始 2：完结 3:取消
   */
  status?: number;
  /**
   * 状态:0:初始、1:已收运
   * 状态集合查询:0:初始、1:已收运
   */
  statusList?: number[];
  /**
   * 供应商编码
   */
  supplierCode?: string;
  /**
   * 供应商名称
   */
  supplierName?: string;
  /**
   * 同步状态:1:成功;2:失败
   */
  syncStatus?: number;
  /**
   * 租户ID
   */
  tenantId?: string;
  /**
   * 主题描述
   */
  themeDesc?: string;
  /**
   * 总入库金额
   */
  totalAmount?: number;
  /**
   * 总成本金额
   */
  totalCostAmount?: number;
  updateUser?: number;
  updateUserName?: string;
  /**
   * 入库车号
   */
  vehicleNo?: string;
  /**
   * 仓库编码
   */
  warehouseCode?: string;
  /**
   * 仓库id
   */
  warehouseId?: number;
  /**
   * 仓库名称
   */
  warehouseName?: string;
  /**
   * 榜单编号
   */
  weighbridgeNo?: string;
  /**
   * 榜单编号附件
   */
  weighbridgeNoAttachment?: string;
  [property: string]: any;
}

export interface QuickWarehousingProductVO {
  /**
   * 本次实际入库数量
   */
  actualInQty?: number;
  /**
   * 本次实际入库重量
   */
  actualInWeight?: number;
  /**
   * 入库金额
   */
  amount?: number;
  /**
   * 商品换算关系第二个值的单位id
   */
  conversionRelSecondUnitId?: number;
  /**
   * 商品换算关系第二个值的单位名称
   */
  conversionRelSecondUnitName?: string;
  /**
   * 成本金额
   */
  costAmount?: number;
  /**
   * 成本单价
   */
  costUnitPrice?: number;
  /**
   * 交易币种：CNY->人民币，USD->美元
   */
  currency?: string;
  /**
   * 计划重量
   */
  expectedWeight?: number;
  /**
   * 一级分类id，外键关联（t_product_category.id）
   */
  firstCategoryId?: number;
  /**
   * 一级分类名称，外键关联（t_product_category.name）
   */
  firstCategoryName?: string;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 已入库数量
   */
  inWarehouseQty?: number;
  /**
   * 已入库重量
   */
  inWarehouseWeight?: number;
  /**
   * 一级单位增减->1:开启；0:关闭
   */
  isDiscreteUnit?: number;
  /**
   * 是否sku->1:是;0:否
   */
  isSku?: number;
  /**
   * 父id
   */
  parentId?: number;
  /**
   * 计价模式->0:一级单位;1:二级单位
   */
  pricingScheme?: number;
  /**
   * 商品编码
   */
  productCode?: string;
  /**
   * 商品数量(期望)
   */
  productExpectQty?: number;
  /**
   * 商品名称
   */
  productName?: string;
  /**
   * 商品包装
   */
  productPackaging?: string;
  /**
   * 规格
   */
  productSpecs?: string;
  /**
   * 商品采购单位id，外键关联（t_product_unit.id）
   */
  productUnitId?: number;
  /**
   * 商品采购单位名称，外键关联（t_product_unit.name）
   */
  productUnitName?: string;
  /**
   * 入库通知单号
   */
  receiptNoticeCode?: string;
  /**
   * 入库通知单id
   */
  receiptNoticeId?: number;
  /**
   * 实收总重量
   */
  receivedWeight?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 二级分类id，外键关联（t_product_category.id）
   */
  secondCategoryId?: number;
  /**
   * 二级分类名称，外键关联（t_product_category.name）
   */
  secondCategoryName?: string;
  /**
   * 租户id
   */
  tenantId?: string;
  /**
   * 三级分类id，外键关联（t_product_category.id）
   */
  thirdCategoryId?: number;
  /**
   * 三级分类名称，外键关联（t_product_category.name）
   */
  thirdCategoryName?: string;
  /**
   * 入库单价
   */
  unitPrice?: number;
  /**
   * 仓库库区编码
   */
  warehouseAreaCode?: string;
  /**
   * 仓库库区名称
   */
  warehouseAreaName?: string;
  /**
   * 仓库编码
   */
  warehouseCode?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
  /**
   * 重量（KG），范围【0,999999】
   */
  weight?: number;
  [property: string]: any;
}

export interface WarehouseReceiptNoticeQualityInspectionDTO {
  /**
   * 附件
   */
  attachment?: string;
  /**
   * 扣款金额
   */
  deductionAmount?: number;
  /**
   * 扣款说明
   */
  deductionRemark?: string;
  /**
   * 商品质检信息列表
   */
  detailList?: ReceiptNoticeQcProductDetail[];
  /**
   * 主键id
   */
  id?: number;
  /**
   * 每页条数
   */
  limit?: number;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 商品编码
   */
  productCode?: string;
  /**
   * 入库通知单id
   */
  receiptNoticeId?: number;
  [property: string]: any;
}

/**
* ReceiptNoticeQcProductDetail对象
*
* ReceiptNoticeQcProductDetail
*/
export interface ReceiptNoticeQcProductDetail {
  /**
   * 占比
   */
  proportion?: number;
  /**
   * 数量
   */
  quantity?: number;
  /**
   * 规格
   */
  specification?: string;
  [property: string]: any;
}


export interface QuickWarehousingEntryDetailVO {
  /**
   * 总入库量（实际总量）
   */
  actualTotalQuantity?: number;
  /**
   * 总入库转换量（实际总数量）
   */
  actualTotalWeight?: number;
  /**
   * 详细地址
   */
  address?: string;
  /**
   * 城市id
   */
  cityId?: string;
  /**
   * 市名称
   */
  cityName?: string;
  /**
   * 联系人
   */
  contactPerson?: string;
  /**
   * 合同编码
   */
  contractCode?: string;
  /**
   * 合同名称
   */
  contractName?: string;
  /**
   * 合同类型
   */
  contractType?: number;
  /**
   * 国家区域代码
   */
  countryAreaCode?: string;
  /**
   * 国家id
   */
  countryId?: string;
  /**
   * 国家名称
   */
  countryName?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建用户id
   */
  createUser?: number;
  /**
   * 创建用户名
   */
  createUserName?: string;
  /**
   * 交易币种：CNY->人民币，USD->美元
   */
  currency?: string;
  /**
   * 客户编码
   */
  customerCode?: string;
  /**
   * 客户名称
   */
  customerName?: string;
  /**
   * 区县id
   */
  districtId?: string;
  /**
   * 区县名称
   */
  districtName?: string;
  /**
   * 入库人信息列表
   */
  entryOperatorList?: EntryOperatorInfo[];
  /**
   * 入库单列表
   */
  entryOrderList?: WarehouseEntryOrdersVO[];
  /**
   * 计划总数
   */
  expectedQty?: number;
  /**
   * 计划总重量
   */
  expectedWeight?: number;
  /**
   * 完整地址
   */
  fullAddress?: string;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 删除状态：0->正常；1->删除
   */
  isDeleted?: number;
  /**
   * 手机号
   */
  mobile?: string;
  /**
   * 组织编码
   */
  orgCode?: string;
  /**
   * 计划交货时间
   */
  plannedDeliveryTime?: string;
  /**
   * 商品列表
   */
  productList?: WarehouseReceiptNoticeProductVO[];
  /**
   * 商品个数
   */
  productQty?: number;
  /**
   * 省份id
   */
  provinceId?: string;
  /**
   * 省名称
   */
  provinceName?: string;
  /**
   * 采购/销售员
   */
  purchaseSalesPerson?: string;
  /**
   * 商品质检列表
   */
  qualityInspectionList?: WarehouseReceiptNoticeQualityInspectionVO[];
  /**
   * 入库通知单号
   */
  receiptNoticeCode?: string;
  /**
   * 入库类型:1:采购入库、2:退货入库
   */
  receiptType?: number;
  /**
   * 收运人
   */
  receiver?: string;
  /**
   * 收运时间
   */
  receivingTime?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 业务员ID
   */
  salesmanId?: string;
  /**
   * 业务员姓名
   */
  salesmanName?: string;
  /**
   * 商品种类数量
   */
  skuQty?: number;
  /**
   * 来源：1:手动创建 2:同步
   */
  source?: number;
  /**
   * 来源单号
   */
  sourceOrderCode?: string;
  /**
   * 来源系统
   */
  sourceSystem?: string;
  /**
   * 状态:0:草稿、1:初始 2：完结 3:取消
   */
  status?: number;
  /**
   * 供应商编码
   */
  supplierCode?: string;
  /**
   * 供应商名称
   */
  supplierName?: string;
  /**
   * 同步状态:1:成功;2:失败
   */
  syncStatus?: number;
  /**
   * 租户ID
   */
  tenantId?: string;
  /**
   * 主题描述
   */
  themeDesc?: string;
  /**
   * 总入库金额
   */
  totalAmount?: number;
  /**
   * 总成本金额
   */
  totalCostAmount?: number;
  /**
   * 入库总数量
   */
  totalInWarehouseQty?: number;
  /**
   * 入库总重量
   */
  totalInWarehouseWeight?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 修改用户id
   */
  updateUser?: number;
  /**
   * 修改用户名
   */
  updateUserName?: string;
  /**
   * 入库车号
   */
  vehicleNo?: string;
  /**
   * 仓库编码
   */
  warehouseCode?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
  /**
   * 榜单编号
   */
  weighbridgeNo?: string;
  /**
   * 榜单编号附件
   */
  weighbridgeNoAttachment?: string;
  [property: string]: any;
}

/**
* EntryOperatorInfo
*/
export interface EntryOperatorInfo {
  /**
   * 入库人
   */
  entryOperator?: string;
  /**
   * 入库时间
   */
  entryTime?: string;
  [property: string]: any;
}

/**
* WarehouseEntryOrdersVO对象
*
* WarehouseEntryOrdersVO
*/
export interface WarehouseEntryOrdersVO {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建用户id
   */
  createUser?: number;
  /**
   * 创建用户名
   */
  createUserName?: string;
  /**
   * 入库人
   */
  entryOperator?: string;
  /**
   * 入库单号
   */
  entryOrderCode?: string;
  /**
   * 入库时间
   */
  entryTime?: string;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 入库批次
   */
  inboundBatchCode?: string;
  /**
   * 删除状态：0->正常；1->删除
   */
  isDeleted?: number;
  /**
   * 订单类型：1:按分拣单入库;2:按商品入库
   */
  orderType?: number;
  /**
   * 组织编码
   */
  orgCode?: string;
  /**
   * 商品集合
   */
  productList?: WarehouseEntryOrdersProductVO[];
  /**
   * 入库商品总量
   */
  productTotalInQty?: number;
  /**
   * 商品总重量
   * 入库商品总重量
   */
  productTotalInWeight?: number;
  /**
   * 领用状态-> 0:未领用 1:已领用
   */
  receivingStatus?: number;
  /**
   * 领用时间
   */
  receivingTime?: string;
  /**
   * 领用人id
   */
  receivingUserId?: number;
  /**
   * 领用人名称
   */
  receivingUserName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 商品种类数量
   */
  skuQty?: number;
  /**
   * 状态:1:已入库
   */
  status?: number;
  /**
   * 租户ID
   */
  tenantId?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 修改用户id
   */
  updateUser?: number;
  /**
   * 仓库编码
   */
  warehouseCode?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
  [property: string]: any;
}

/**
* WarehouseEntryOrdersProductVO对象
*
* WarehouseEntryOrdersProductVO
*/
export interface WarehouseEntryOrdersProductVO {
  /**
   * 入库金额
   */
  amount?: number;
  /**
   * 库区类型：1->常规库；2->残次品库
   */
  areaType?: number;
  /**
   * 可用库存数量
   * 可用库存数量(拆装)
   */
  availableStockQty?: number;
  /**
   * 成本金额
   */
  costAmount?: number;
  /**
   * 成本单价
   */
  costUnitPrice?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建用户id
   */
  createUser?: number;
  /**
   * 交易币种：CNY->人民币，USD->美元
   */
  currency?: string;
  /**
   * 出库数量
   */
  disassemblyOutQty?: number;
  /**
   * 入库单id
   */
  entryOrderId?: number;
  /**
   * 一级分类id，外键关联（t_product_category.id）
   */
  firstCategoryId?: number;
  /**
   * 一级分类名称，外键关联（t_product_category.name）
   */
  firstCategoryName?: string;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 删除状态：0->正常；1->删除
   */
  isDeleted?: number;
  /**
   * 一级单位增减->1:开启；0:关闭
   */
  isDiscreteUnit?: number;
  /**
   * 当前订单中分拣单其他项实际入库商品数量
   */
  orderSortingOtherActualQty?: number;
  /**
   * 当前订单中分拣单实际入库商品数量
   */
  orderSortingTotalActualQty?: number;
  /**
   * 组织编码
   */
  orgCode?: string;
  /**
   * 商品数量(实际收运)
   */
  productActualQty?: number;
  /**
   * 商品重量(实际收运)
   */
  productActualWeight?: number;
  /**
   * 商品编码
   */
  productCode?: string;
  /**
   * 入库比率
   */
  productEntryRatio?: number;
  /**
   * 入库数量(已经库)
   */
  productInventoryQty?: number;
  /**
   * 入库比率(已经库)
   */
  productInventoryRatio?: number;
  /**
   * 入库重量(已入库)
   */
  productInventoryWeight?: number;
  /**
   * 商品名称
   */
  productName?: string;
  /**
   * 分拣后总数量
   */
  productSortingTotalQty?: number;
  /**
   * 分拣后总重量
   */
  productSortingTotalWeight?: number;
  /**
   * 规格
   */
  productSpecs?: string;
  /**
   * 商品采购单位id，外键关联（t_product_unit.id）
   */
  productUnitId?: number;
  /**
   * 商品采购单位名称，外键关联（t_product_unit.name）
   */
  productUnitName?: string;
  /**
   * 入库通知单号
   */
  receiptNoticeCode?: string;
  /**
   * 入库类型:1:采购入库、2:退货入库
   */
  receiptType?: number;
  /**
   * 收运单单号
   */
  receivingOrderCode?: string;
  /**
   * 收运单id
   */
  receivingOrderId?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 二级分类id，外键关联（t_product_category.id）
   */
  secondCategoryId?: number;
  /**
   * 二级分类名称，外键关联（t_product_category.name）
   */
  secondCategoryName?: string;
  /**
   * 分拣单编码
   */
  sortingCode?: string;
  /**
   * 分拣单id
   */
  sortingId?: number;
  /**
   * 源订单商品行id
   */
  sourceOrderProductId?: number;
  /**
   * 状态->0:初始;1:已入库;3:入库中
   */
  status?: number;
  /**
   * 租户id
   */
  tenantId?: string;
  /**
   * 三级分类id，外键关联（t_product_category.id）
   */
  thirdCategoryId?: number;
  /**
   * 三级分类名称，外键关联（t_product_category.name）
   */
  thirdCategoryName?: string;
  /**
   * 入库单价
   */
  unitPrice?: number;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 更新用户id
   */
  updateUser?: number;
  /**
   * 库区编码
   */
  warehouseAreaCode?: string;
  /**
   * 库区名称
   */
  warehouseAreaName?: string;
  /**
   * 仓库编码
   */
  warehouseCode?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
  [property: string]: any;
}

/**
* WarehouseReceiptNoticeProductVO对象
*
* WarehouseReceiptNoticeProductVO
*/
export interface WarehouseReceiptNoticeProductVO {
  /**
   * 入库金额
   */
  amount?: number;
  /**
   * 商品换算关系第二个值的单位id
   */
  conversionRelSecondUnitId?: number;
  /**
   * 商品换算关系第二个值的单位名称
   */
  conversionRelSecondUnitName?: string;
  /**
   * 成本金额
   */
  costAmount?: number;
  /**
   * 成本单价
   */
  costUnitPrice?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建用户id
   */
  createUser?: number;
  /**
   * 交易币种：CNY->人民币，USD->美元
   */
  currency?: string;
  /**
   * 计划重量
   */
  expectedWeight?: number;
  /**
   * 一级分类id，外键关联（t_product_category.id）
   */
  firstCategoryId?: number;
  /**
   * 一级分类名称，外键关联（t_product_category.name）
   */
  firstCategoryName?: string;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 入库数量
   */
  inWarehouseQty?: number;
  /**
   * 入库重量
   */
  inWarehouseWeight?: number;
  /**
   * 删除状态：0->正常；1->删除
   */
  isDeleted?: number;
  /**
   * 是否sku->1:是;0:否
   */
  isSku?: number;
  /**
   * 组织编码
   */
  orgCode?: string;
  /**
   * 父id
   */
  parentId?: number;
  /**
   * 商品数量(实际收运)
   */
  productActualQty?: number;
  /**
   * 商品编码
   */
  productCode?: string;
  /**
   * 商品数量(期望)
   */
  productExpectQty?: number;
  /**
   * 商品名称
   */
  productName?: string;
  /**
   * 商品包装
   */
  productPackaging?: string;
  /**
   * 规格
   */
  productSpecs?: string;
  /**
   * 商品采购单位id，外键关联（t_product_unit.id）
   */
  productUnitId?: number;
  /**
   * 商品采购单位名称，外键关联（t_product_unit.name）
   */
  productUnitName?: string;
  /**
   * 入库通知单号
   */
  receiptNoticeCode?: string;
  /**
   * 入库通知单id
   */
  receiptNoticeId?: number;
  /**
   * 实收总重量
   */
  receivedWeight?: number;
  /**
   * 剩余入库数量
   */
  remainingInQty?: number;
  /**
   * 剩余入库重量
   */
  remainingInWeight?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 二级分类id，外键关联（t_product_category.id）
   */
  secondCategoryId?: number;
  /**
   * 二级分类名称，外键关联（t_product_category.name）
   */
  secondCategoryName?: string;
  /**
   * 三级分类id，外键关联（t_product_category.id）
   */
  thirdCategoryId?: number;
  /**
   * 三级分类名称，外键关联（t_product_category.name）
   */
  thirdCategoryName?: string;
  /**
   * 入库单价
   */
  unitPrice?: number;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 更新用户id
   */
  updateUser?: number;
  /**
   * 仓库编码
   */
  warehouseCode?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
  /**
   * 重量（KG），范围【0,999999】
   */
  weight?: number;
  [property: string]: any;
}

/**
* WarehouseReceiptNoticeQualityInspectionVO对象
*
* WarehouseReceiptNoticeQualityInspectionVO
*/
export interface WarehouseReceiptNoticeQualityInspectionVO {
  /**
   * 附件
   */
  attachment?: string;
  /**
   * 扣款金额
   */
  deductionAmount?: number;
  /**
   * 扣款说明
   */
  deductionRemark?: string;
  /**
   * 商品质检信息列表
   */
  detailList?: ReceiptNoticeQcProductDetail[];
  /**
   * 主键id
   */
  id?: number;
  /**
   * 商品编码
   */
  productCode?: string;
  /**
   * 入库通知单id
   */
  receiptNoticeId?: number;
  [property: string]: any;
}

/**
* ReceiptNoticeQcProductDetail对象
*
* ReceiptNoticeQcProductDetail
*/
export interface ReceiptNoticeQcProductDetail {
  /**
   * 占比
   */
  proportion?: number;
  /**
   * 数量
   */
  quantity?: number;
  /**
   * 规格
   */
  specification?: string;
  [property: string]: any;
}





export interface QuickWarehousingPageQuery extends PageQuery {
 /**
   * 查询结束时间
   */
 queryEndTime?: string;
 /**
  * 查询开始时间
  */
 queryStartTime?: string;
 /**
  * 查询类型:1:计划交货时间、2:单据同步时间
  */
 queryType?: number;
 /**
  * 入库通知单号
  */
 receiptNoticeCode?: string;
 /**
  * 入库类型集合查询:1:采购入库、2:退货入库
  */
 receiptTypeList?: number[];
 /**
  * 来源：1:手动创建 2:同步
  */
 sourceList?: number[];
 /**
  * 来源单号
  */
 sourceOrderCode?: string;
 /**
  * 状态集合查询:0:初始、1:已收运 2:草稿
  */
 statusList?: number[];
 /**
  * 主题描述
  */
 themeDesc?: string;
}

export default QuickWarehousingAPI; 