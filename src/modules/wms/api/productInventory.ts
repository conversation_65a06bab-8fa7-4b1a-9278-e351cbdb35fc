import request from "@/core/utils/request";
const PRODUCEINVENTORY_BASE_URL = "/supply-wms/productStock"
const PRODUCTSTOCKLOG_BASE_URL = "/supply-wms/productStockLog"
class productInventoryApi {
  /** 获取商品库存分页数据 */
  static getProductInventoryPage(queryParams?: ProductInventoryPageQuery){
    return request<any, PageResult<ProductInventoryPageVO[]>>({
      url: `${PRODUCEINVENTORY_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }
  /** 获取商品库存操作日志分页数据 */
  static getProductStockLogPage(queryLogParams?: ProductStockLogPageQuery){
    return request<any,PageResult<ProductStockLogPageV0>>({
      url: `${PRODUCTSTOCKLOG_BASE_URL}/page`,
      method: "post",
      data: queryLogParams,
    })
  }
  /** 导出 */
  static exportPage(data?:any){
    return request({
      url: `${PRODUCEINVENTORY_BASE_URL}/export`,
      method: "post",
      data: data,
    })
  }
}
export default productInventoryApi
/** 商品库存分页查询参数 */
export interface ProductInventoryPageQuery extends PageQuery {
  /** 商品 */
  product?: string;
  /** 商品分类 */
  productCategory?: string;
  /** 商品分类一级 */
  firstCategoryIds?: string[];
  /** 商品分类二级 */
  secondCategoryIds?: string[];
  /** 商品分类三级 */
  thirdCategoryIds?: string[];
  /** 库区 */
  warehouseAreaIds?: string[]
}
/** 商品库存分页对象 */
export interface ProductInventoryPageVO {
  /** ID */
  id?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 库区 */
  warehouseAreaName?: string;
  /** 库区id */
  warehouseAreaId?: number
  /** 总库存 */
  totalStockQty?: number
  /** 可用库存 */
  availableInventory?: number
  /** 锁定库存 */
  lockedStockQty?: number
  /** 单位 */
  productUnitName?: string
  /** 规格 */
  productSpec?: string
  /** 商品分类 */
  fullCategoryName?: string
}
/** 商品库存操作日志分页查询参数 */
export interface ProductStockLogPageQuery extends PageQuery{
  /** 商品编码 */
  productCode?: string
  /** 库区id */
  warehouseAreaId?: number
}
/** 商品库存操作日志分页对象 */
export interface ProductStockLogPageV0 {
  /** 操作环节 */
  operationType?: number
  /** 对应单据号 */
  sourceOrderCode?: string
  /** 操作库存 */
  operationQty?: number
  /** 变动方式 */
  changeType?: number
  /** 原始总库存 */
  originalTotalStockQty?: number
  /** 变动后总库存 */
  totalStockQty?: number
  /** 变动后可用库存 */
  availableStockQty?: number
  /** 锁定库存 */
  lockedStockQty?: number
  /** 库区 */
  warehouseAreaName?: string
  /** 操作人 */
  operatorName?: string
  /** 操作时间 */
  operationTime?: string
}

