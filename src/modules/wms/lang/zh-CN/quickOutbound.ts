export default {
  quickOutbound: {
    label: {
      outbound:'出库单',
      outboundNoticeCode:'出库通知单号',
      themeDescription:'主题描述',
      outboundType:'出库类型',
      status:'状态',
      sourceOrderCode:'来源单号',
      isPresaleOrder:'是否预售单',
      plannedDistributionMethod:'计划配送方式',
      plannedQuantity:'计划量',
      totalQuantity:'总量',
      quantity:'计划数量',
      outQuantity:'已出库数量',
      conversionQuantity:'转换量',
      outboundQuantity:'出库量',
      plannedDeliveryTime:'计划发货时间',
      plannedReceivedTime:'要求出库时间',
      purchaseSalesPerson:'采购/销售员',
      createUserName:'申请人',
      createTime:'申请时间',
      outboundUserName:'出库人',
      outboundTime:'出库时间',
      customerName:'客户',
      supplierName:'供应商',
      settlementMethod:'结算方式',
      presentSettlement:'现结',
      accountHanging:'挂账',
      noSettlementRequired:'免结算',
      contractCode:'合同编码',
      contractClassification:'合同分类',
      remark:'备注',
      salesperson:'业务员',
      originalOrderNumber:'原单号',
      customerAddress:'客户地址',
      customerPerson:'客户联系人',
      customerMobile:'客户联系电话',
      areaCode:'区号',
      supplierAddress:'供应商地址',
      supplierPerson:'供应商联系人',
      supplierMobile:'供应商联系电话',
      contractName:'合同名称',
      sourceOrderRemark:'来源单备注',
      detailList:'明细列表',
      productDetail:'商品明细',
      outboundDetail:'出库明细',
      productInfo:'商品信息',
      product:'商品',
      productCode:'商品编码',
      goodsCategory:'商品分类',
      productSpecs:'规格',
      commodityProperty:'商品属性',
      returnVolume:'退货量',
      plannedConversionQuantity:'计划转换量',
      outboundConversionQuantity:'出库转换量',
      pickingList:'拣货单',
      actualDistributionMethod:'实际配送方式',
      actualOutboundTime:'实际出库时间',
      carrier:'承运商',
      vehicleNumber:'车号',
      weighbillNumber:'磅单编号',
      outboundRemark:'出库备注',
      salesUnitPrice:'销售单价(RMB)',
      salesAmount:'销售金额(RMB)',
      costUnitPrice:'成本单价(RMB)',
      costAmount:'成本金额(RMB)',
      inboundStorageArea:'入库库区',
      goodsPackaging:'商品包装',
      outboundStorageArea:'出库库区',
      unitPrice:'出库单价(RMB)',
      amount:'出库金额(RMB)',
      residualQuantity:'剩余量',
      residualConversionQuantity:'剩余转换量',
      remainingAvailableInventory:'剩余可用库存',
      remainingAvailableQuantity:'剩余可用量',
      remainingConversionQuantity:'剩余转换量',
      summation:'合计',
      documentInformation:'单据信息',
      waitOutboundProductDetail:'待出库商品明细',
      attachment:'附件',
      success: "成功",
      fail: "失败",
      approveResultUnit: "条",
      failReason: "失败原因",
      unit:'单位',
      createOrder:'制单',
      sendPerson:'发货人',
      extractPerson:'提货人',
      productName:'商品名称',
      unitPriceNoUnit:'单价',
      amountNoUnit:'金额',
      print:'打印',
      documentNumber: '单据号',
      brand: "品牌",

      //时间
      startTime:'开始时间',
      to:'至',
      endTime:'结束时间',
      yesterday:'昨天',
      today:'今天',
      tomorrow:'明天',
      weekday:'近七天',
    },
    placeholder: {
      inputLimitTips:'请输入大于等于4位的单号',
      defaultNowDate:'默认当天日期',
      chooseTime:'选择时间',
      defaultTips:'默认当前日期/时间',
      actualDeliveryType:'默认与计划配送方式一致',
      productInputTips:'请输入关键词查询商品',
      systemCreate:'系统生成',
      userNow:'默认当前用户',

    },
    button: {
      cancelShiftArrangement:'取消排班',
      batchShiftArrangement:'批量排班',
      export:'导出',
      toShiftArrangement:'去排班',
      confirmOutbound:'确认出库',
      detail:'详情',
      finished:'完结',
      cancel:'取消',
      submit:'提交',
      add:'+',
      delete:'-',
      upload:'上传',
      confirm:'确认',
      addBtn:'新增',
      saveDraft: "保存草稿",
    },
    statusList: {
      draft: "草稿",
      initial: "初始",
      picking: '拣货中',
      portionOutbound:'部分出库',
      finish:'完结',
      cancel:'已取消',
      arranged:'已排班'
    },
    outboundTypeList: {
      procurementOutbound: "销售出库",
      returnOutbound: "采购退货",
      allotOutbound:'调拨出库',
      sellAtTheFieldEdge:'地头售卖',
      reissue:'补发',
      pickupCardRedemption:'提货卡兑换',
      businessReception:'业务招待',
      directOutbound:'直接出库',
      participateInTheExhibition:'参展',
    },
    dateTypeList: {
      createDate:'创建时间',
      outboundDate:'出库时间',
      plannedDate:'计划发货时间',
      receivedDate:'要求出库时间',
    },
    title: {
      shiftArrangement:'去排班',
      batchShiftArrangement:'批量排班',
      addProduct:'添加商品',
    },
    rules: {
      plannedDeliveryTimeRules:'请完善计划发货时间',
      plannedDeliveryTime:'请选择计划发货时间',
      actualDeliveryType:'请选择实际配送方式',
      actualOutboundTime:'请选择实际出库时间',
      warehouseAreaCode:'请选择出库库区',
      outboundQty:'请输入出库量',
      outboundQtyFormat:'支持大于等于0的数字,小数点前8位后3位',
      outboundWeight: '请输入出库转换量',
      outboundWeightFormat: '支持大于等于0的数字,小数点前8位后3位',
      salePrice:'请输入单价',
      salePriceFormat:'单价支持小数点前7位后4位的数字',
      saleAmount:'请输入金额',
      saleAmountFormat:'金额支持小数点前9位后2位的数字',
    },
    message: {
      operationSuccess:'操作成功！',
      finishedWarning:'完结后不可再对当前单据进行出库！',
      cancelTip:'已取消操作！',
      cancelShiftArrangementWarning:'取消排班后，系统会将计划发货时间置空，是否确认取消当前单据的排班计划？',
      codeValidTips:'请输入大于等于4位的单号',
      batchSelectTip:'请选择需要批量排班的单据',
      generateNotBatchShiftArrangementTips:'只有初始状态的单据可以进行批量排班',
      cancelSelectTip:'请选择需要取消排班的单据',
      generateNotCancelShiftArrangementTips:'只有已排班状态的单据可以进行取消排班',
      selected:'已选中',
      batchShiftArrangementWarning:'条单据，统一填写计划发货时间后系统对当前选择单据进行批量排班，是否提交？',
      sameProduct:'商品+库区数据不唯一，请确认！',
      submitWarning:'提交后不可修改当前单据，系统自动完成商品拣货出库',
      availableTip:'剩余可用量不能为负数',
      deleteWarning:'是否确认删除已创建的出库通知单？',
      deleteSuccess:'删除成功！',
      noProduct:'待出库商品明细不能为空！',
      totalWarning:'至少有一条商品出库量或出库转换量大于0',
    }
  }
}
