export default {
  productInventory: {
    label: {
      product: "商品",
      productCategory: "商品分类",
      warehouseAreaCode: "库区",
      productCode: "商品编码",
      productName: "商品名称",
      totalStockQty: "总库存",
      totalQty:'总量',
      totalWeight:'总转换量',
      availableStockQty: "可用库存",
      lockedStockQty: "锁定数量",
      availableQty:'可用量',
      availableWeight:'可用转换量',
      productUnitName: "单位",
      productSpec: "规格",
      warehouseAreaName: "库区",
      warehouseAreaIdentification:'库区标识',
      operationType: "操作环节",
      sourceOrderCode: "对应单据号",
      operationQty: "操作量",
      operationWeight: "操作转换量",
      changeType: "变动方式",
      lockedStockWeight:'锁定重量',
      operatorName: "操作人",
      operationTime: "操作时间",
      primeval: "原始",
      afterChange: "变动后",
      changeNUm: "变动量",
      // 新增字段 - New fields
      operationAmount: "操作量",
      operationConvertAmount: "操作转换量",
      totalAmount: "总量",
      totalConvertAmount: "总转换量",
      availableAmount: "可用量",
      availableConvertAmount: "可用转换量",
      lockedAmount: "锁定量",
      lockedConvertAmount: "锁定转换量",
    },
    placeholder:{
      keywords: "请输入商品编码或商品名称",
    },
    button: {
      operationLog: "操作日志",
      closeBtn: "关闭",
      export: "导出",

    },
    title: {
      operationLogTitle: "库存操作日志",
    },
    warehouseAreaIdentificationList: {
      physicsWarehouseArea:'物理库区',
      virtualWarehouseArea:'入库虚拟库区',
    },
  },
};
