<template>
    <div>
      <el-dialog
        :model-value="visible"
        :title="$t('common.uploadFiLe')"
        :close-on-click-modal="false"
        @close="close"
        width="1012px"
      >
        <div>
          <upload-multiple
            :tips="''"
            :fileSize="10"
            :fileType="['rar', 'zip', 'pdf', 'jpg', 'png', 'jpeg']"
            :modelValue="imagesUrls"
            ref="detailPicsRef"
            @update:model-value="onChangeMultiple"
            :limit="6"
            :formRef="fileRef"
            class="modify-multipleUpload"
            name="detailPic"
            :ifDrag="true"
            :showUploadBtn="showUploadBtn"
            :listType="`icon-card`"
            :disabledVal="editType == 'detail' ? true : false"
          />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="info" @click="close">
              {{ $t("common.cancel") }}
            </el-button>
            <el-button type="primary" @click="handleSubmit">
              {{ $t("common.confirm") }}
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup lang="ts">
  const emit = defineEmits(["update:visible", "onSubmit"]);
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    showUploadBtn: {
      type: Boolean,
      default: true,
    },
  });
  
  const imagesUrls = ref();
  const fileRef = ref();
  const editType = ref();
  
  function close() {
    emit("update:visible", false);
    reset();
  }
  function reset() {
    imagesUrls.value = [];
  }
  
  function onChangeMultiple(val: any) {
    imagesUrls.value = val;
  }
  
  function handleSubmit() {
    emit("onSubmit", imagesUrls.value);
    close();
  }
  
  function setFormData(data: any) {
    imagesUrls.value = data;
  }
  
  function setEditType(data: any) {
    editType.value = data;
  }
  
  defineExpose({
    setFormData,
    setEditType,
  });
  </script>
  
  <style lang="scss"></style>
  