<template>
  <div class="app-container">
    <div class="transfer-order-add" v-loading="loading">
      <div class="page-title">
        <div @click="handleBack()" class="cursor-pointer mr8px">
          <el-icon>
            <Back />
          </el-icon>
        </div>
        <div>
          <span v-if="isEdit">{{ t('transferOrder.title.editTransferOrder') }}</span>
          <span v-else>{{ t('transferOrder.title.addTransferOrder') }}</span>
        </div>
      </div>

      <div class="page-content">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="108px" label-position="right">
          <!-- 基本信息 -->
          <div class="title-label">
            <div class="title-line"></div>
            <div class="title-content">{{ t('transferOrder.label.basicInformation') }}</div>
          </div>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.transferOrderCode')" prop="transferOrderCode">
                <el-input v-model="form.transferOrderCode" :placeholder="t('transferOrder.placeholder.systemGenerated')" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.transferOrderName')" prop="transferOrderName">
                <el-input v-model="form.transferOrderName" :placeholder="t('transferOrder.placeholder.enterInput')" maxlength="100" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.applicantUser')" prop="applicantUserName">
                <!-- <el-input
                  v-model="form.applicantUserName"
                  placeholder="默认当前用户"
                  disabled
                /> -->
                <el-select v-model="form.applicantUserId" :placeholder="t('transferOrder.placeholder.pleaseSelect')" @change="handleSelectApplicatioUser"
                  filterable>
                  <el-option v-for="item in allUserList" :key="item.userId" :label="item.nickName"
                    :value="item.userId" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.applicantTime')" prop="applicantTime">
                <el-date-picker v-model="form.applicantTime" type="datetime" :placeholder="t('transferOrder.placeholder.automaticTime')"
                  value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.planTransferOutTime')" prop="planTransferOutTime">
                <el-date-picker v-model="form.planTransferOutTime" type="datetime" :placeholder="t('transferOrder.placeholder.automaticTime')"
                  value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.planTransferInTime')" prop="planTransferInTime">
                <el-date-picker v-model="form.planTransferInTime" type="datetime" :placeholder="t('transferOrder.placeholder.automaticTime')"
                  value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
              </el-form-item>
            </el-col>
            <!-- 调出方 -->
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.sourceWarehouseName')" prop="sourceWarehouseCode">
                <el-select v-model="form.sourceWarehouseCode" :placeholder="t('transferOrder.placeholder.selectSourceWarehouse')" filterable clearable
                  @change="handleSourceWarehouseChange" :disabled="isInitStatus">
                  <el-option v-for="warehouse in warehouseList" :key="warehouse.warehouseCode"
                    :label="warehouse.warehouseName" :value="warehouse.warehouseCode" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="16">
              <el-form-item :label="t('transferOrder.label.address')" prop="sourceAddress">
                <section style="display: flex; gap: 10px;align-items: center;width: 100%;">
                  <el-input v-model="form.sourceCountryName" placeholder="" disabled style="flex:1;" />
                  <el-input
                    :value="formatAreaAddress(form.sourceProvinceName, form.sourceCityName, form.sourceDistrictName)"
                    :placeholder="t('transferOrder.placeholder.pleaseSelect')" disabled style="flex:1;" />
                  <el-input v-model="form.sourceAddress" placeholder="" disabled style="flex:2;" />
                </section>
                <!--  <SelAreaCascader :defaultCountryInfo="form.sourceCountryId" :defaultAreaInfo="getSourceAreaInfo()"
                  :defaultDesAddressInfo="form.sourceAddress" :isEditMode="isEdit"
                  @getCountryInfo="handleSourceCountryChange" @getAreaInfo="handleSourceAreaChange"
                  @getDesAddressInfo="handleSourceAddressChange" :flex="true" :paddingTop="'0px'" /> -->
              </el-form-item>
            </el-col>




            <!-- 调入方 -->
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.targetWarehouseName')" prop="targetWarehouseCode">
                <el-select v-model="form.targetWarehouseCode" :placeholder="t('transferOrder.placeholder.selectTargetWarehouse')" filterable clearable :disabled="isInitStatus"
                  @change="handleTargetWarehouseChange">
                  <el-option v-for="warehouse in warehouseList" :key="warehouse.warehouseCode"
                    :label="warehouse.warehouseName" :value="warehouse.warehouseCode" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item :label="t('transferOrder.label.address')" prop="targetAddress">
                <section style="display: flex; gap: 10px;align-items: center;width: 100%;">
                  <el-input v-model="form.targetCountryName" placeholder="" disabled style="flex:1;" />
                  <el-input
                    :value="formatAreaAddress(form.targetProvinceName, form.targetCityName, form.targetDistrictName)"
                    :placeholder="t('transferOrder.placeholder.pleaseSelect')" disabled style="flex:1;" />
                  <el-input v-model="form.targetAddress" placeholder="" disabled style="flex:2;" />
                </section>
                <!-- <SelAreaCascader v-model:defaultCountryInfo:="form.targetCountryId"
                  :defaultAreaInfo="getTargetAreaInfo()" :defaultDesAddressInfo="form.targetAddress"
                  :isEditMode="isEdit" @getCountryInfo="handleTargetCountryChange" @getAreaInfo="handleTargetAreaChange"
                  @getDesAddressInfo="handleTargetAddressChange" :flex="true" :paddingTop="'0px'" /> -->
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.planDeliveryType')" prop="planDeliveryType">
                <el-select v-model="form.planDeliveryType" :placeholder="t('transferOrder.placeholder.selectDeliveryMethod')" @change="handlePlanDeliveryTypeChange">
                  <el-option v-for="item in deliveryMethodList" :key="item.id"
                    :label="item.deliveryMethodsCategoryVO.deliveryMethodsCategoryName + '/' + item.methodName"
                    :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item :label="t('transferOrder.label.remark')" prop="remark">
                <el-input v-model="form.remark" :placeholder="t('transferOrder.placeholder.enterInput')" maxlength="200" clearable type="textarea"
                  show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 商品明细 -->
          <div class="title-label">
            <div class="title-line"></div>
            <div class="title-content">{{ t('transferOrder.label.productDetails') }}</div>
            <div class="flex-1"></div>
            <el-button type="primary" @click="handleAddProduct" class="add-product-btn">
              {{ t('transferOrder.button.add') }}
            </el-button>
          </div>

          <div class="product-table">
            <el-table :data="form.transferOrderDetailList" border stripe max-height="700" min-height="400" show-summary
              :summary-method="getSummaries">
              <el-table-column :label="t('transferOrder.label.serialNumber')" type="index" width="60" :index="(index) => index + 1" />

              <el-table-column :label="t('transferOrder.label.productInfo')" min-width="200">
                <template #default="scope">
                  <div>
                    <div>{{ t('transferOrder.label.productCode') }}:{{ scope.row.productCode }}</div>
                    <div>{{ scope.row.productName }}</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column :label="t('transferOrder.label.productCategory')" prop="categoryName" min-width="120" />

              <el-table-column :label="t('transferOrder.label.productSpec')" prop="productSpec" min-width="100" />

              <el-table-column :label="t('transferOrder.label.productAttribute')" prop="attributeTypeName" min-width="100" />

              <!--可用库存-->
              <el-table-column :label="t('transferOrder.label.availableStockQty')" min-width="100">
                <template #default="scope">
                  <div>可用库存：{{ parseFloat(scope.row.availableStockQty) === 0 ? '无库存' : scope.row.availableStockQty }}</div>
                  <div>转换量：{{ parseFloat(scope.row.availableStockWeight) === 0 ? '无库存' : scope.row.availableStockWeight }}</div>
                </template>
              </el-table-column>
              <el-table-column :label="t('transferOrder.label.planTransferQty')" min-width="120">
                <template #default="scope">
                  <el-form-item :prop="'transferOrderDetailList.' + scope.$index + '.planTransferQty'" class="mt15px"
                    label-width="0px" :rules="[
                      {
                        required: true,
                        message: t('transferOrder.rules.planTransferQtyRequired'),
                        trigger: ['blur', 'change'],
                      },
                      {
                        pattern:
                          /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                        message: t('transferOrder.rules.numberFormatError'),
                        trigger: ['blur', 'change'],
                      },
                    ]">
                    <el-input v-model="scope.row.planTransferQty" controls-position="right"
                      @change="handleQuantityChange(scope.row, scope.$index, 'FIRST_TO_SECOND')" style="width: 100%">
                      <template #append>
                        <span>{{ scope.row.productUnitName }}</span>
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column :label="t('transferOrder.label.planTransferConverted')" min-width="120">
                <template #default="scope">
                  <el-form-item :prop="'transferOrderDetailList.' + scope.$index + '.planTransferConverted'"
                    class="mt15px" label-width="0px" :rules="[
                      {
                        required: true,
                        message: t('transferOrder.rules.planTransferConvertedRequired'),
                        trigger: ['blur', 'change'],
                      },
                      {
                        pattern:
                          /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                        message: t('transferOrder.rules.numberFormatError'),
                        trigger: ['blur', 'change'],
                      },
                    ]">
                    <el-input v-model="scope.row.planTransferConverted"
                      @change="handleQuantityChange(scope.row, scope.$index, 'SECOND_TO_FIRST')"
                      controls-position="right" style="width: 100%">
                      <template #append>
                        <span>{{ scope.row.convertUnit }}</span>
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column :label="t('transferOrder.label.operation')" width="80" fixed="right">
                <template #default="scope">
                  <el-button type="danger" link @click="handleRemoveProduct(scope.$index)">
                    {{ t('transferOrder.button.delete') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>


          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button @click="handleBack">{{ t('transferOrder.button.cancel') }}</el-button>
            <!-- 编辑初始状态的调拨单，不需要【保存草稿】按钮 -->
            <el-button @click="handleSaveDraft" v-if="!isInitStatus">{{ t('transferOrder.button.saveDraft') }}</el-button>
            <el-button type="primary" @click="handleSubmit">{{ t('transferOrder.button.submit') }}</el-button>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 商品选择侧边栏 -->
    <AddProduct ref="addProductRef" v-model:visible="dialog.visible" :title="dialog.title" :totalStockQtyShow="false"
      :availableStockQtyShow="false" :outWarehouseAreaFromShow="false" :hasTotalStockQty="false" @onSubmit="onSubmit" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Back } from '@element-plus/icons-vue';
import TransferOrderAPI, {
  type TransferOrderSaveForm
} from '@/modules/wms/api/transferOrder';
import AddProduct from '@/modules/wms/components/addProduct.vue';
// import SelAreaCascader from '@/modules/wms/components/SelAreaCascader.vue';
import CurrentWarehouseAPI, { type warehouseInfo } from '@/modules/wms/api/currentWarehouse';
import { parseTime } from '@/core/utils';
import { useUserStore, useWarehouseStore } from '@/core/store';
import CommonAPI from '@/modules/wms/api/common';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useConvertProductUnit } from "@/modules/wms/composables";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
const route = useRoute();
const router = useRouter();

// 根据路由参数动态设置组件名称
const isEditMode = computed(() => !!route.params.transferOrderCode);

defineOptions({
  name: 'TransferOrderForm',
  inheritAttrs: false,
});
const { convertProductUnitStragery } = useConvertProductUnit();

const userStore = useUserStore();
const warehouseStore = useWarehouseStore();

// 响应式数据
const loading = ref(false);
const formRef = ref();
const addProductRef = ref();

// 对话框状态
const dialog = reactive({
  visible: false,
  title: '添加商品'
});

// 判断是否为编辑模式
const isEdit = computed(() => route.params.transferOrderCode !== undefined);
const isInitStatus = computed(() => route.query.transferOrderStatus === '1');
// 表单数据
const form = reactive<TransferOrderSaveForm & { id?: number }>({
  // transferOrderCode: '',
  transferOrderName: '',
  applicantUserId: undefined,
  applicantUserName: '',
  applicantTime: '',
  sourceWarehouseId: undefined,
  sourceWarehouseCode: '',
  sourceWarehouseName: '',
  sourceCountryId: '',
  sourceCountryName: '',
  sourceProvinceId: '',
  sourceProvinceName: '',
  sourceCityId: '',
  sourceCityName: '',
  sourceDistrictId: '',
  sourceDistrictName: '',
  sourceAddress: '',
  targetWarehouseId: undefined,
  targetWarehouseCode: '',
  targetWarehouseName: '',
  targetCountryId: '',
  targetCountryName: '',
  targetProvinceId: '',
  targetProvinceName: '',
  targetCityId: '',
  targetCityName: '',
  targetDistrictId: '',
  targetDistrictName: '',
  targetAddress: '',
  planTransferOutTime: '',
  planTransferInTime: '',
  planDeliveryType: undefined,
  remark: '',
  transferOrderDetailList: [],
  transferOrderStatus: 0,
  id: undefined,
  planDeliveryName: undefined
});

const deliveryMethodList = ref<any[]>([]);
const getDeliveryMethodList = async () => {
  const data = {
    page: 1,
    limit: 100,
    enableStatus: 1
  }
  const res = await TransferOrderAPI.queryDeliveryMethodList(data);
  deliveryMethodList.value = res.records || [];
}


const handlePlanDeliveryTypeChange = (value: any) => {
  console.log(value);
  const deliveryMethod = deliveryMethodList.value.find(item => item.id === value);
  form.planDeliveryName = deliveryMethod?.deliveryMethodsCategoryVO.deliveryMethodsCategoryName + '/' + deliveryMethod?.methodName;
}
// 下拉选项数据
const warehouseList = ref<warehouseInfo[]>([]);

// 表单验证规则
const rules = {
  applicantUserName: [
    { required: true, message: t('transferOrder.rules.applicantUserRequired'), trigger: 'blur' }
  ],
  applicantTime: [
    { required: true, message: t('transferOrder.rules.applicantTimeRequired'), trigger: 'blur' }
  ],
  sourceWarehouseCode: [
    { required: true, message: t('transferOrder.rules.sourceWarehouseRequired'), trigger: 'change' }
  ],
 /* planTransferOutTime: [
    { required: true, message: t('transferOrder.rules.planTransferOutTimeRequired'), trigger: 'change' }
  ],*/
  targetWarehouseCode: [
    { required: true, message: t('transferOrder.rules.targetWarehouseRequired'), trigger: 'change' }
  ],

  /*planTransferInTime: [
    { required: true, message: t('transferOrder.rules.planTransferInTimeRequired'), trigger: 'change' }
  ],*/
  planDeliveryType: [
    { required: true, message: t('transferOrder.rules.planDeliveryTypeRequired'), trigger: 'change' }
  ]
};

// 表格合计方法
const getSummaries = (param: any) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }

    // 计划量列的合计
    if (column.label === '计划量') {
      const values = data.map((item: any) => Number(item.planTransferQty) || 0);
      if (!values.every((value: number) => Number.isNaN(value))) {
        sums[index] = values.reduce((prev: number, curr: number) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0).toFixed(3);
      } else {
        sums[index] = '0';
      }
      return;
    }

    // 计划转换量列的合计
    if (column.label === '计划转换量') {
      const values = data.map((item: any) => Number(item.planTransferConverted) || 0);
      if (!values.every((value: number) => Number.isNaN(value))) {
        sums[index] = values.reduce((prev: number, curr: number) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0).toFixed(3);
      } else {
        sums[index] = '0';
      }
      return;
    }

    // 其他列不显示合计
    sums[index] = ' ';
  });

  return sums;
};

/** 格式化地址信息显示 */
function formatAreaAddress(provinceName?: string, cityName?: string, districtName?: string): string {
  const addressParts = [provinceName, cityName, districtName].filter(part => part && part.trim());
  return addressParts.length > 0 ? addressParts.join('/') : '';
}

/** 返回上一页 */
async function handleBack() {
  await tagsViewStore.delView(route);
  await router.push({
    path: '/wms/transferManagement/transferOrder'
  })
  /* router.replace({
    path: '/wms/transferManagement/transferOrder'
  }); */
}

/** 获取基础数据 */
async function getBaseData() {
  try {
    getDeliveryMethodList();
    const warehouseRes = await CurrentWarehouseAPI.allListByTenant({status: 1});
    warehouseList.value = warehouseRes || [];

    // 设置默认申请时间
    /*  if (!form.applicantTime) {
       form.applicantTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
     } */
  } catch (error) {
    ElMessage.error('获取基础数据失败');
  }
}

// 填充表单数据
function fillFormData(data: any) {
  form.planTransferOutTime = parseTime(data.planTransferOutTime);
  form.applicantTime = parseTime(data.applicantTime);
  form.planTransferInTime = parseTime(data.planTransferInTime);
  form.transferOrderName = data.transferOrderName;
  form.applicantUserId = data.applicantUserId;
  form.applicantUserName = data.applicantUserName;
  form.sourceWarehouseCode = data.sourceWarehouseCode;
  form.sourceWarehouseName = data.sourceWarehouseName;
  form.targetWarehouseCode = data.targetWarehouseCode;
  form.targetWarehouseName = data.targetWarehouseName;
  form.planDeliveryType = data.planDeliveryType;
  form.remark = data.remark;
  form.transferOrderStatus = data.transferOrderStatus;
  form.sourceCountryId = data.sourceCountryId;
  form.sourceCountryName = data.sourceCountryName;
  form.sourceProvinceId = data.sourceProvinceId;
  form.sourceProvinceName = data.sourceProvinceName;
  form.sourceCityId = data.sourceCityId;
  form.sourceCityName = data.sourceCityName;
  form.sourceDistrictId = data.sourceDistrictId;
  form.sourceDistrictName = data.sourceDistrictName;
  form.sourceAddress = data.sourceAddress;
  form.targetCountryId = data.targetCountryId;
  form.targetCountryName = data.targetCountryName;
  form.targetProvinceId = data.targetProvinceId;
  form.targetProvinceName = data.targetProvinceName;
  form.targetCityId = data.targetCityId;
  form.targetCityName = data.targetCityName;
  form.targetDistrictId = data.targetDistrictId;
  form.targetDistrictName = data.targetDistrictName;
  form.targetAddress = data.targetAddress;
  form.transferOrderCode = data.transferOrderCode;
  form.planDeliveryName = data.planDeliveryName;
  data.detailList.forEach((item: any) => {
    item.categoryName = `${item.firstCategoryName}-${item.secondCategoryName}-${item.thirdCategoryName}`;
  });
  form.transferOrderDetailList = data.detailList || [];

}
/** 获取调拨单详情（编辑模式） */
async function getTransferOrderDetail() {
  if (!isEdit.value) return;

  const transferOrderCode = route.params.transferOrderCode as string;
  try {
    loading.value = true;
    const response = await TransferOrderAPI.getTransferOrderDetail(transferOrderCode);
    response.planDeliveryType = Number(response.planDeliveryType);
    const data = response;
    // 填充表单数据
    fillFormData(data);
  } catch (error) {
    // ElMessage.error('获取调拨单详情失败');
  } finally {
    loading.value = false;
  }
}

async function getWarehouseDetail(warehouseCode: string, suffiexKey: string) {
  if (!warehouseCode) return;
  const warehouseDetail = await TransferOrderAPI.getWarehouseDetail(warehouseCode);
  (form as any)[suffiexKey + 'CountryId'] = warehouseDetail.countryId;
  (form as any)[suffiexKey + 'CountryName'] = warehouseDetail.countryName;
  (form as any)[suffiexKey + 'ProvinceId'] = warehouseDetail.provinceId;
  (form as any)[suffiexKey + 'ProvinceName'] = warehouseDetail.provinceName;
  (form as any)[suffiexKey + 'CityId'] = warehouseDetail.cityId;
  (form as any)[suffiexKey + 'CityName'] = warehouseDetail.cityName;
  (form as any)[suffiexKey + 'DistrictId'] = warehouseDetail.districtId;
  (form as any)[suffiexKey + 'DistrictName'] = warehouseDetail.districtName;
  (form as any)[suffiexKey + 'Address'] = warehouseDetail.address;
}

/** 调出仓库变更 */
async function handleSourceWarehouseChange(warehouseCode: string) {
  const warehouse = warehouseList.value.find(w => w.warehouseCode === warehouseCode);
  if (warehouse) {
    form.sourceWarehouseCode = warehouse.warehouseCode;
    form.sourceWarehouseName = warehouse.warehouseName;
    getWarehouseDetail(warehouse.warehouseCode, 'source');
  }
}

/** 调入仓库变更 */
async function handleTargetWarehouseChange(warehouseCode: string) {
  const warehouse = warehouseList.value.find(w => w.warehouseCode === warehouseCode);
  if (warehouse) {
    form.targetWarehouseCode = warehouse.warehouseCode;
    form.targetWarehouseName = warehouse.warehouseName;
    getWarehouseDetail(warehouse.warehouseCode, 'target');
  }
}

/** 添加商品 */
function handleAddProduct() {
  dialog.title = '添加商品';
  let params = {
    typeList: 2, // 查询所有可选的目标商品列表
    status: 1 // 上架商品
  };
  addProductRef.value.setFormData({ queryParams: params});
  addProductRef.value.getOutWarehouseAreaList();
  addProductRef.value.queryManagerCategoryList();
  dialog.visible = true;
}

/** 商品选择确认 */
async function onSubmit(data: any) {
  if (data && data.collection) {
    const newProducts = data.collection.map((product: any) => ({
      productCode: product.productCode,
      productName: product.productName,
      productSpec: product.productSpec,
      categoryName: product.fullCategoryName || `${product.firstCategoryName || ''}/${product.secondCategoryName || ''}/${product.thirdCategoryName || ''}`,
      attributeTypeName: product.attributeTypeName,
      planTransferQty: null,
      planTransferConverted: null,
      convertUnit: product.conversionRelSecondUnitName,
      convertedQty: product.conversionRelSecondUnitCoefficient,
      productUnitName: product.productUnitName,
      isSku: product.isSku,
      isDiscreteUnit: product.isDiscreteUnit
    }));

    // 合并新选择的商品到现有列表
    form.transferOrderDetailList = [...(form.transferOrderDetailList || []), ...newProducts];
    const stockData  = await doQueryStock({
      warehouseCode: form.sourceWarehouseCode, // 调出仓库
      productCodeList: form.transferOrderDetailList.map(item => {
        return item.productCode;
      })
    })
    // 根据productCode匹配 availableStockQty 可用库存数量，回填form.transferOrderDetailList数据。
    form.transferOrderDetailList.forEach(item => {
      const stock = stockData?.find(stock => stock.productCode === item.productCode);
      if (stock) {
        item.availableStockQty = stock.availableStockQty;
        item.availableStockWeight = stock.availableStockWeight;
      }
      else{
        item.availableStockQty = 0;
        item.availableStockWeight = 0;
      }
    })

  }
}

/** 删除商品 */
function handleRemoveProduct(index: number) {
  form.transferOrderDetailList?.splice(index, 1);
}

function isNotNull(value: any) {
  return value !== null && value !== undefined && value !== '';
}

/** 数量变更 */
/* 数量与转换量
输入若商品的【一级单位增减】为【是】      
  输入数量时，转换量自动转换      
  输入转换量时，数量不管，用户手动填写
若商品的【一级单位增减】为【否】       
  输入数量时，转换量内无值时自动转换，有值时不转换       
  输入转换量时，数量内无值时自动转换，且数量向上取整，有值时不转换 */
async function handleQuantityChange(row: any, index: number, type: string) {
  // convertProductUnit(row, type);
  convertProductUnitStragery(row, type, 'planTransferQty', 'planTransferConverted', false);
 /*  const data = {
    convertUnitTypeEnum: type,
    originalValue: type === 'FIRST_TO_SECOND' ? row.planTransferQty : row.planTransferConverted,
    productCode: row.productCode
  }
  if (row.isSku === 1) {
    if (type === 'FIRST_TO_SECOND') {
     row.planTransferConverted = await convertProductUnit(data);
    }
  
  }
  else if (row.isSku === 0) {
    if (type === 'FIRST_TO_SECOND') {
      if (!isNotNull(row.planTransferConverted)) {
        row.planTransferConverted = await convertProductUnit(data);
      }
    }
    else if (type === 'SECOND_TO_FIRST') {
      if (!isNotNull(row.planTransferQty)) {
        const value = await convertProductUnit(data);
        row.planTransferQty = Math.ceil(value);
      }
    }
  } */
}

/* async function convertProductUnit(data: any) {
  const res = await CommonAPI.convertProductUnit(data);
  return res?.convertedValue;
} */

/** 保存草稿 */
async function handleSaveDraft() {
  try {
    await formRef.value?.validate();
    loading.value = true;
    form.transferOrderStatus = 0; // 草稿状态
    const formCopy = { ...form };
    if (formCopy.planTransferOutTime) {
      (formCopy as any).planTransferOutTime = new Date(formCopy.planTransferOutTime).getTime();
    }
    if (formCopy.applicantTime) {
      (formCopy as any).applicantTime = new Date(formCopy.applicantTime).getTime();
    }
    if (formCopy.planTransferInTime) {
      (formCopy as any).planTransferInTime = new Date(formCopy.planTransferInTime).getTime();
    }
    const response = await TransferOrderAPI.saveTransferOrder(formCopy);
    ElMessage.success('保存草稿成功');

    handleBack();
  } catch (error) {
    // ElMessage.error('保存失败');
  } finally {
    loading.value = false;
  }
}

/** 提交 */
async function handleSubmit() {
  try {
    await formRef.value?.validate();

    if (!form.transferOrderDetailList || form.transferOrderDetailList.length === 0) {
      ElMessage.warning('请添加商品明细');
      return;
    }
    // planTransferOutTime、applicantTime、planTransferInTime转换为时间戳
    const formCopy = { ...form };
    if (formCopy.planTransferOutTime) {
      (formCopy as any).planTransferOutTime = new Date(formCopy.planTransferOutTime).getTime();
    }
    if (formCopy.applicantTime) {
      (formCopy as any).applicantTime = new Date(formCopy.applicantTime).getTime();
    }
    if (formCopy.planTransferInTime) {
      (formCopy as any).planTransferInTime = new Date(formCopy.planTransferInTime).getTime();
    }

    loading.value = true;
    formCopy.transferOrderStatus = 1; // 提交状态

    await TransferOrderAPI.saveTransferOrder(formCopy);
    ElMessage.success(isEdit.value ? '修改成功' : '新增成功');
    handleBack();
  } catch (error) {
    // ElMessage.error(isEdit.value ? '修改失败' : '新增失败');
  } finally {
    loading.value = false;
  }
}

const allUserList = ref<any[]>([]);
// 获取所有用户列表
async function getAllUser() {
  const response = await TransferOrderAPI.queryAllUser();
  allUserList.value = response || [];

}

const handleSelectApplicatioUser = (value: string) => {
  const user = allUserList.value.find(item => item.userId === value);
  if (user) {
    form.applicantUserName = user.nickName;
  }
}

// 默认当前仓库
watch(() => warehouseStore.selectedWarehouseId, (newVal) => {
  if (!isEdit.value && newVal) {
    // 默认当前仓库
    console.log("warehouseStore.selectedWarehouseId newVal---", newVal)
    form.sourceWarehouseCode = String(newVal);
    form.targetWarehouseCode = String(newVal);
    getWarehouseDetail(String(newVal), 'source');
    getWarehouseDetail(String(newVal), 'target');
  }
});

async function doQueryStock(data){
  const res = TransferOrderAPI.queryStock(data)
  return res;
}

// 初始化
onMounted(async () => {
  await getBaseData();
  // await getTransferOrderDetail();
  await getAllUser();

  if (!isEdit.value) {
    form.applicantUserId = userStore.user.userId;
    form.applicantUserName = userStore.user.username;
    handleSelectApplicatioUser(form.applicantUserId);
  }
  else{
    await getTransferOrderDetail();
  }
});
</script>

<style scoped lang="scss">
.transfer-order-add {
  min-height: 100%;
  background: #ffffff;
  border-radius: 4px;

  .page-title {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7f3;
    font-size: 18px;
    font-weight: 500;
    color: #151719;

    .mr8px {
      margin-right: 8px;
    }
  }

  .page-content {
    padding: 0 24px 24px 24px;

    .title-label {
      display: flex;
      align-items: center;
      margin: 32px 0 20px 0;

      .title-line {
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        margin-right: 8px;
      }

      .title-content {
        font-size: 16px;
        font-weight: 500;
        color: #151719;
      }

      .flex-1 {
        flex: 1;
      }

      .add-product-btn {
        margin-left: auto;
      }
    }

    .product-table {
      margin-bottom: 32px;

      .table-summary {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 16px;
        background: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-top: none;
        font-weight: 500;

        .ml-4 {
          margin-left: 16px;
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      padding-top: 24px;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style>
