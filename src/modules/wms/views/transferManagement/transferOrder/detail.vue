<template>
  <div class="app-container">
    <div class="transfer-order-detail" v-loading="loading">
      <div class="page-title">
        <div @click="handleBack()" class="cursor-pointer mr8px">
          <el-icon>
            <Back />
          </el-icon>
        </div>
        <div>{{ t('transferOrder.label.transferInfo') }}</div>
        <div class="flex-1"></div>
        <el-button @click="handleBack()">{{ t('transferOrder.button.back') }}</el-button>
      </div>

      <div class="page-content">
        <!-- 调拨信息 -->
        <div class="info-section">
          <div class="section-title">{{ t('transferOrder.label.transferInfo') }}</div>

          <el-row :gutter="20" class="grad-row">
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.transferOrderCode')+':'">
                <span>{{ detail.transferOrderCode || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.transferOrderName')+':'">
                <span>{{ detail.transferOrderName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.applicantUser')+':'">
                <span>{{ detail.applicantUserName || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--        </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.applicantTime')+':'">
                <span>{{ parseDateTime(detail.applicantTime, 'dateTime') || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--         </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.sourceWarehouseName')+':'">
                <span>{{ detail.sourceWarehouseName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.address')+':'">
                <span class="encryptBox">
                  <span v-if="detail.sourceAddressShow">
                    {{ detail.sourceAddressFormat }}
                    <el-icon
                      v-if="detail.sourceFullAddress"
                      @click="detail.sourceAddressShow = false"
                      class="encryptBox-icon"
                      color="#762ADB"
                      size="16"
                    >
                      <View />
                    </el-icon>
                  </span>
                  <span v-else>
                    {{ detail.sourceFullAddress || '-' }}
                  </span>
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.planTransferOutTime')+':'">
                <span>{{ parseDateTime(detail.planTransferOutTime, 'dateTime') || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--           </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
           <!--  <el-col :span="8">
              <el-form-item label="详细地址：">
                <span>{{ detail.sourceAddress || '-' }}</span>
              </el-form-item>
            </el-col> -->
            <!--   </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.targetWarehouseName')+':'">
                <span>{{ detail.targetWarehouseName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.address')+':'">
                <span class="encryptBox">
                  <span v-if="detail.targetAddressShow">
                    {{ detail.targetAddressFormat }}
                    <el-icon
                      v-if="detail.targetFullAddress"
                      @click="detail.targetAddressShow = false"
                      class="encryptBox-icon"
                      color="#762ADB"
                      size="16"
                    >
                      <View />
                    </el-icon>
                  </span>
                  <span v-else>
                    {{ detail.targetFullAddress || '-' }}
                  </span>
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.planTransferInTime')+':'">
                <span>{{ parseDateTime(detail.planTransferInTime, 'dateTime') || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--    </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
           <!--  <el-col :span="8">
              <el-form-item label="详细地址：">
                <span>{{ detail.targetAddress || '-' }}</span>
              </el-form-item>
            </el-col> -->
            <!--           </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item :label="t('transferOrder.label.planDeliveryType')+':'">
                <!-- <span>{{ getDeliveryMethodName(detail.planDeliveryType) || '-' }}</span> -->
                <span>{{ detail.planDeliveryName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item :label="t('transferOrder.label.remark')+':'">
                <span>{{ detail.remark || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 调拨明细 -->
        <div class="info-section">
          <div class="section-title">{{ t('transferOrder.label.transferDetails') }}</div>

          <el-table :data="detail.detailList" border stripe max-height="700" min-height="400">
            <el-table-column :label="t('transferOrder.label.serialNumber')" type="index" width="60" :index="(index) => index + 1" />

            <el-table-column :label="t('transferOrder.label.productInfo')" min-width="200">
              <template #default="scope">
                <div>
                  <div>{{ t('transferOrder.label.productCode') }}:{{ scope.row.productCode }}</div>
                  <div>{{ scope.row.productName }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column :label="t('transferOrder.label.productCategory')" min-width="150">
              <template #default="scope">
                <span>{{ scope.row.firstCategoryName }}/{{ scope.row.secondCategoryName }}/{{
                  scope.row.thirdCategoryName }}</span>
              </template>
            </el-table-column>

            <el-table-column :label="t('transferOrder.label.productSpec')" prop="productSpec" min-width="100" />

            <el-table-column :label="t('transferOrder.label.productAttribute')" prop="attributeTypeName" min-width="100" />

            <el-table-column :label="t('transferOrder.label.planTransferQty')" min-width="120">
              <template #default="scope">
                <div>
                  <div>总量({{ scope.row.productUnitName }})：{{ scope.row.planTransferQty}}</div>
                  <div>转换量({{ scope.row.conversionRelSecondUnitName }})：{{ scope.row.planTransferConverted}}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column :label="t('transferOrder.label.outboundTotalQty')" min-width="120">
              <template #default="scope">
                <div>
                  <div>总量({{ scope.row.productUnitName }})：{{ scope.row.outQty }}</div>
                  <div>转换量({{ scope.row.conversionRelSecondUnitName }})：{{ scope.row.outConvertedQty}}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column :label="t('transferOrder.label.actualInboundQty')" min-width="120">
              <template #default="scope">
                <div>
                  <div>总量({{ scope.row.productUnitName }})：{{ scope.row.inQty || 0 }}</div>
                  <div>转换量({{ scope.row.conversionRelSecondUnitName }})：{{ scope.row.inConvertedQty || 0 }}</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 出库信息 -->
        <div class="info-section" v-if="detail.outboundNoticeInfoVO?.outboundPickingListVOList?.length > 0">
          <div class="section-title">{{ t('transferOrder.label.outboundInfo') }}</div>
          <section v-for="productItem in detail.outboundNoticeInfoVO?.outboundPickingListVOList" class="outbound-info-section">
            <el-row :gutter="20" class="grad-row">
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.outboundNoticeCode')+':'">
                  <span>{{ productItem.deliveryNoticeCode || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.pickingListCode')+':'">
                  <span>{{ productItem.pickingListCode || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.actualDeliveryMethod')+':'">
                  <span>{{ productItem.deliveryName || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.actualOutboundTime')+':'">
                  <span>{{ parseDateTime(productItem.outboundTime, 'dateTime') || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="grad-row">
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.carrier')+':'">
                  <span>{{ productItem.carrier || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.carNumber')+':'">
                  <span>{{ productItem.carNumber || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.poundCode')+':'">
                  <span>{{ productItem.poundCode || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.attachment')+':'">
                  <el-badge :value="calculateLength(productItem.poundAttachmentFiles)" class="ml-2" :offset="[5, 10]">
                    <el-button type="primary" link @click="handleAttachmentUpload(productItem.poundAttachmentFiles)">查看</el-button>
                  </el-badge>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('transferOrder.label.remark')+':'">
                  <span>{{ productItem.remark || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-table :data="productItem.outboundPickingListProductVOList" 
            border stripe max-height="400" show-summary :summary-method="getSummaryMethodOfOut">
              <el-table-column :label="t('transferOrder.label.serialNumber')" type="index" width="60" :index="(index) => index + 1" fixed="left" />
              <el-table-column :label="t('transferOrder.label.productInfo')" min-width="200">
                <template #default="scope">
                  <div>
                    <div>{{ t('transferOrder.label.productCode') }}:{{ scope.row.productCode }}</div>
                    <div>{{ scope.row.productName }}</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column :label="t('transferOrder.label.productCategory')" min-width="150">
                <template #default="scope">
                  <span>{{ scope.row.firstCategoryName }}/{{ scope.row.secondCategoryName }}/{{
                    scope.row.thirdCategoryName }}</span>
                </template>
              </el-table-column>

              <el-table-column :label="t('transferOrder.label.productSpec')" prop="productSpecs" min-width="100" />
              <!-- 销售单价(RMB) salePrice-->
              <el-table-column :label="t('transferOrder.label.salePrice')" prop="salePrice" min-width="100" />
              <!-- 销售金额(RMB) saleAmount-->
              <el-table-column :label="t('transferOrder.label.saleAmount')" prop="saleAmount" min-width="100" />
              <!-- 成本单价(RMB) costPrice-->
              <!-- <el-table-column label="成本单价(RMB)" prop="costPrice" min-width="100" /> -->
              <!-- 出库量 -->
              <el-table-column :label="t('transferOrder.label.outboundQty')" prop="actualPickThisTime" min-width="100">
                <template #default="scope">
                  {{ scope.row.actualPickThisTime }} {{ scope.row.productUnitName }}
                </template>
              </el-table-column>
              <!-- 出库转换量 -->
              <el-table-column :label="t('transferOrder.label.outboundConvertedQty')" prop="warehouseAreaActualPickWeight" min-width="100">
                <template #default="scope">
                  {{ scope.row.warehouseAreaActualPickWeight }} {{ scope.row.conversionRelSecondUnitName }}
                </template>
              </el-table-column>
              <!-- 成本金额(RMB) costAmount-->
              <!-- <el-table-column label="成本金额(RMB)" prop="costAmount" min-width="100" /> -->
              <!-- 入库库区 -->
              <el-table-column :label="t('transferOrder.label.outboundWarehouseArea')" prop="warehouseAreaName" min-width="100">
                <template #default="scope">
                  <span>{{ scope.row.warehouseAreaName || '-' }}|{{ scope.row.warehouseAreaCode || '-'  }}</span>
                </template>
              </el-table-column>
              <!-- 商品包装-->
              <el-table-column :label="t('transferOrder.label.productPackage')" prop="productPackage" min-width="100" />

            </el-table>
          </section>
        </div>
        <!-- 入库信息 -->
        <div class="info-section" v-if="detail.inboundNoticeInfoVO">
          <div class="section-title">{{ t('transferOrder.label.inboundInfo') }}</div>
          <section v-for="productItem in detail.inboundNoticeInfoVO">
           <el-row :gutter="20" class="grad-row">
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.inboundNoticeCode')+':'">
                  <span>{{ productItem.receiptNoticeCode || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.inboundOrderCode')+':'">
                  <span>{{ productItem.entryOrderCode || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.poundCode')+':'">
                  <span>{{ productItem.weighbridgeNo || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.attachment')+':'">

                  <el-badge :value="calculateLength(productItem.weighbridgeNoAttachment)" class="ml-2" :offset="[5, 10]">
                    <el-button type="primary" link @click="handleAttachmentUpload(productItem.weighbridgeNoAttachment)">查看</el-button>
                  </el-badge>
                  <!-- <span>{{ productItem.weighbridgeNoAttachment || '-' }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('transferOrder.label.inboundCarNumber')+':'">
                  <span>{{ productItem.vehicleNo || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('transferOrder.label.inboundRemark')+':'">
                  <span>{{ productItem.remark || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
           
            <el-table :data="productItem.productList" border stripe max-height="400" show-summary :summary-method="getSummaryMethodOfIn">
              <el-table-column :label="t('transferOrder.label.serialNumber')" type="index" width="60" :index="(index) => index + 1" fixed="left" />

              <el-table-column :label="t('transferOrder.label.productInfo')" min-width="200">
                <template #default="scope">
                  <div>
                    <div>{{ t('transferOrder.label.productCode') }}:{{ scope.row.productCode }}</div>
                    <div>{{ scope.row.productName }}</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column :label="t('transferOrder.label.productCategory')" min-width="150">
                <template #default="scope">
                  <span>{{ scope.row.firstCategoryName }}/{{ scope.row.secondCategoryName }}/{{
                    scope.row.thirdCategoryName }}</span>
                </template>
              </el-table-column>

              <el-table-column :label="t('transferOrder.label.productSpec')" prop="productSpecs" min-width="100" />
              <!-- 销售单价(RMB) salePrice-->
              <!-- <el-table-column label="销售单价(RMB)" prop="salePrice" min-width="100" /> -->
              <!-- 销售金额(RMB) saleAmount-->
              <!-- <el-table-column label="销售金额(RMB)" prop="saleAmount" min-width="100" /> -->
              <!-- 成本单价(RMB) costPrice-->
              <el-table-column :label="t('transferOrder.label.costPrice')" prop="costUnitPrice" min-width="100" />
              <el-table-column :label="t('transferOrder.label.costAmount')" prop="costAmount" min-width="100" />
              <el-table-column :label="t('transferOrder.label.inboundPrice')" prop="unitPrice" min-width="100" /> 
              <!-- 出库量 alreadyOutboundQty-->
              <el-table-column :label="t('transferOrder.label.inboundQty')" prop="productActualQty" min-width="100" />
              <!-- 出库转换量 alreadyOutboundWeight-->
              <el-table-column :label="t('transferOrder.label.inboundConvertedQty')" prop="productActualWeight" min-width="100" />
              <!-- 成本金额(RMB) costAmount-->
              <el-table-column :label="t('transferOrder.label.inboundAmount')" prop="amount" min-width="100" />
              <el-table-column :label="t('transferOrder.label.inboundWarehouseArea')" prop="warehouseAreaName" min-width="100">
                <template #default="scope">
                  <span>{{ scope.row.warehouseAreaName || '-' }}|{{ scope.row.warehouseAreaCode || '-'  }}</span>
                </template>
              </el-table-column>
              <!-- 商品包装 productPackaging-->
              <el-table-column :label="t('transferOrder.label.productPackage')" prop="productPackaging" min-width="100" />

              <el-table-column :label="t('transferOrder.label.qualityInspection')" width="100">
                <template #default="scope">
                  <el-button type="primary" link @click="handleShowCheckInfo(scope.row)">{{ t('transferOrder.button.view') }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </section>

        </div>
        <!-- 上传弹窗 -->
        <UploadDialog ref="uploadDialogRef" v-model:visible="uploadDialog.visible" :showUploadBtn="false"/>
        <QualityCheckDialog ref="qualityCheckDialogRef" v-model:visible="qualityCheckDialog.visible" type="show" :product-data="qualityCheckDialog.productData"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Back, View } from '@element-plus/icons-vue';
import TransferOrderAPI, {
  type TransferOrderDetailVO,
  type DeliveryMethodVO
} from '@/modules/wms/api/transferOrder';
import { parseDateTime } from '@/core/utils/index';
import UploadDialog from '@/modules/wms/components/uploadDialog.vue';
import QualityCheckDialog from './components/QualityCheckDialog.vue';
import QuickWarehousingAPI from '@/modules/wms/api/quickWarehousing';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const detail = ref<TransferOrderDetailVO>({});
const deliveryMethodList = ref<DeliveryMethodVO[]>([]);
const qualityCheckDialog = reactive({
  visible: false,
  productData: [],
});
const qualityCheckDialogRef = ref();
const handleShowCheckInfo = async (row: any) => {
  const res = await handleQualityCheck(row);
  qualityCheckDialog.productData = res;
  qualityCheckDialog.visible = true;
}
/** 返回上一页 */
function handleBack() {
  router.replace({
    path: '/wms/transferManagement/transferOrder'
  });
}

// 上传弹窗状态
const uploadDialog = reactive({
  visible: false,
});

// const addProductRef = ref();
const uploadDialogRef = ref();
// const currentUploadIndex = ref<number>();

const handleAttachmentUpload = (files: any) => {
  // uploadDialogRef.value?.setEditType("detail");
  if (files) {
    const filesList = JSON.parse(files);
    if (filesList.length > 0) {
      uploadDialog.visible = true;
      nextTick(() => {
        uploadDialogRef.value?.setFormData(filesList);
        // uploadDialogRef.value?.setEditType("detail");
      });
    }
  }
}

// 入库信息合计计算
const getSummaryMethodOfIn = (param: any) => {
  console.log(param);
  // 计算出库信息table中销售金额、成本单价、出库量、出库转换量、成本金额,分别是5，6，7，8，9列
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计';
    } else if (index === 5 || index === 6 || index === 7 || index === 8 || index === 9) {
      // 计算6、7、8列的和
      const values = data.map((item: any) => {
        if (index === 5) {
          return Number(item.costAmount) || 0;
        }/*  else if (index === 6) {
          return Number(item.costPrice) || 0; 
        } */ else if (index === 7) {
          return Number(item.productActualQty) || 0;
        } else if (index === 8) {
          return Number(item.productActualWeight) || 0;
        } else if (index === 9) {
          return Number(item.amount) || 0;
        }
        return 0;
      });
      
      if (values.every((value: number) => value === 0)) {
        sums[index] = '';
      } else {
        const sum = values.reduce((prev: number, curr: number) => {
          return prev + curr;
        }, 0);
        
        // 根据列类型格式化显示
        if (index === 6 || index === 7) {
          sums[index] = sum.toFixed(3);
        } else {
          sums[index] = sum.toFixed(4);
        }
      }
    } else {
      sums[index] = ' ';
    }
  });
  
  return sums;
}
// 出库信息合计计算
const getSummaryMethodOfOut = (param: any) => {
  console.log(param);
  // 计算出库信息table中销售金额、出库量、出库转换量应列的合计，在表格中分别是5，6，7列，完善这个方法
  const { columns, data } = param;
  const sums: string[] = [];
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计';
    } else if (index === 5 || index === 6 || index === 7) {
      // 计算6、7、8列的和
      const values = data.map((item: any) => {
        if (index === 5) {
          return Number(item.saleAmount) || 0;
        } else if (index === 6) {
          return Number(item.actualPickThisTime) || 0; 
        } else if (index === 7) {
          return Number(item.warehouseAreaActualPickWeight) || 0;
        }
        return 0;
      });
      
      if (values.every((value: number) => value === 0)) {
        sums[index] = '';
      } else {
        const sum = values.reduce((prev: number, curr: number) => {
          return prev + curr;
        }, 0);
        
        // 根据列类型格式化显示
        if (index === 6 || index === 7) {
          sums[index] = sum.toFixed(3);
        } else {
          sums[index] = sum.toFixed(4);
        }
      }
    } else {
      sums[index] = ' ';
    }
  });
  
  return sums;
}

const calculateLength = (files: string) => {
  if (!files) return 0;
  return JSON.parse(files).length;
}

// 地址加密处理函数
function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}

function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}

// 初始化地址加密
function initializeAddressEncryption() {
  // 构建完整的source地址
  const sourceFullAddress = getFullAddress('source');
  (detail.value as any).sourceFullAddress = sourceFullAddress;
  
  // 构建完整的target地址
  const targetFullAddress = getFullAddress('target');
  (detail.value as any).targetFullAddress = targetFullAddress;
  
  // 处理source地址加密
  if (sourceFullAddress && sourceFullAddress !== '-' && containsNumber(sourceFullAddress)) {
    (detail.value as any).sourceAddressShow = true;
    (detail.value as any).sourceAddressFormat = replaceNumbersWithAsterisk(sourceFullAddress);
  } else {
    (detail.value as any).sourceAddressShow = false;
  }
  
  // 处理target地址加密
  if (targetFullAddress && targetFullAddress !== '-' && containsNumber(targetFullAddress)) {
    (detail.value as any).targetAddressShow = true;
    (detail.value as any).targetAddressFormat = replaceNumbersWithAsterisk(targetFullAddress);
  } else {
    (detail.value as any).targetAddressShow = false;
  }
}
/** 获取调拨单详情 */
async function getTransferOrderDetail() {
  const transferOrderCode = route.params.transferOrderCode as string;
  if (!transferOrderCode) {
    ElMessage.error('调拨单号不能为空');
    return;
  }

  try {
    loading.value = true;
    const response = await TransferOrderAPI.getTransferOrderDetail(transferOrderCode);
    detail.value = response || {};
    
    // 处理地址加密
    initializeAddressEncryption();
  } catch (error) {
    ElMessage.error('获取调拨单详情失败');
  } finally {
    loading.value = false;
  }
}

/** 获取配送方式列表 */
async function getDeliveryMethodList() {
  try {
    const response = await TransferOrderAPI.getDeliveryMethodList();
    deliveryMethodList.value = response.data || [];
  } catch (error) {
    console.error('获取配送方式列表失败', error);
  }
}

/** 获取完整地址 */
function getFullAddress(type: 'source' | 'target') {
  const prefix = type === 'source' ? 'source' : 'target';
  const countryName = detail.value[`${prefix}CountryName` as keyof TransferOrderDetailVO];
  const provinceName = detail.value[`${prefix}ProvinceName` as keyof TransferOrderDetailVO];
  const cityName = detail.value[`${prefix}CityName` as keyof TransferOrderDetailVO];
  const districtName = detail.value[`${prefix}DistrictName` as keyof TransferOrderDetailVO];
  const address = detail.value[`${prefix}Address` as keyof TransferOrderDetailVO];
  const parts = [countryName, provinceName, cityName, districtName,address].filter(Boolean);
  return parts.join('/');
}

/** 获取配送方式名称 */
function getDeliveryMethodName(id?: number) {
  if (!id) return '';
  const method = deliveryMethodList.value.find(m => m.id === id);
  return method?.deliveryMethodName || '';
}

/** 计算计划总量 */
/* function getTotalPlanQty() {
  const total = detail.value.detailList?.reduce((sum, item) => sum + (item.planTransferQty || 0), 0) || 0;
  return total;
} */

/** 计算计划转换总量 */
/* function getTotalPlanConverted() {
  const total = detail.value.detailList?.reduce((sum, item) => sum + (item.planTransferConverted || 0), 0) || 0;
  return total.toFixed(3);
} */
// 质检弹窗
async function handleQualityCheck(row:any) {
  // receivingOrderCode
  // 入库量和转换量取同商品productCode对应的字段之和
  const res = await QuickWarehousingAPI.queryProductQualityInspectionList({
    receivingOrderCode: row.receivingOrderCode,
    productCode: row.productCode,
  });
  return {
      ...res,
      productCode: row.productCode,
      productName: row.productName,
      productSpecs: row.productSpecs,
      actualInQty: row.productActualQty,
      actualInWeight: row.productActualWeight,
      productUnitName: row.productUnitName,
      conversionRelSecondUnitName: row.conversionRelSecondUnitName,
    } ||
     {
    productCode: row.productCode,
    productName: row.productName,
    productSpecs: row.productSpecs,
    actualInQty: row.productActualQty,
    actualInWeight: row.productActualWeight,
    productUnitName: row.productUnitName,
    conversionRelSecondUnitName: row.conversionRelSecondUnitName,
  };
};
// 初始化
onMounted(async () => {
  await Promise.all([
    getDeliveryMethodList(),
    getTransferOrderDetail(),
  ]);
});
</script>

<style scoped lang="scss">
:deep(.el-form-item--default){
  margin-bottom: 10px;
}
.transfer-order-detail {
  background: #ffffff;
  border-radius: 4px;
  min-height: 100%;

  .page-title {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7f3;
    font-size: 18px;
    font-weight: 500;
    color: #151719;

    .mr8px {
      margin-right: 8px;
    }

    .flex-1 {
      flex: 1;
    }
  }

  .page-content {
    padding: 24px;

    .info-section {
      margin-bottom: 24px;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        font-size: 16px;
        font-weight: 500;
        color: #151719;

        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background: var(--el-color-primary);
          margin-right: 8px;
        }
      }

      .table-summary {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 16px;
        background: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-top: none;
        font-weight: 500;

        .ml-4 {
          margin-left: 16px;
        }
      }
    }
  }
}
.outbound-info-section{
  padding: 12px;
}
.link-text {
  color: var(--el-color-primary);
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.encryptBox {
  display: flex;
  align-items: center;
  
  .encryptBox-icon {
    margin-left: 8px;
    cursor: pointer;
  }
}
</style>
