<template>
    <div class="app-container">
        <div class="disassemblyAssembleTemplate">
            <div class="search-container">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="98px">
                    <el-form-item prop="disassemblyTemplateCode" :label="$t('disassemblyAssembleTemplate.label.templateCode')">
                        <el-input
                                v-model="queryParams.disassemblyTemplateCode"
                                :placeholder="$t('disassemblyAssembleTemplate.placeholder.templateCode')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="disassemblyTemplateName" :label="$t('disassemblyAssembleTemplate.label.templateName')">
                      <el-input
                        v-model="queryParams.disassemblyTemplateName"
                        :placeholder="$t('disassemblyAssembleTemplate.placeholder.templateName')"
                        clearable
                        class="!w-[256px]"
                      />
                    </el-form-item>
                    <el-form-item prop="applyUserName" :label="$t('disassemblyAssembleTemplate.label.applyName')">
                      <el-input
                        v-model="queryParams.applyUserName"
                        :placeholder="$t('disassemblyAssembleTemplate.placeholder.applyName')"
                        clearable
                        class="!w-[256px]"
                      />
                    </el-form-item>
                    <el-form-item prop="statusList" :label="$t('disassemblyAssembleTemplate.label.templateStatus')">
                        <el-select
                          v-model="queryParams.statusList"
                          :placeholder="$t('disassemblyAssembleTemplate.placeholder.templateStatus')"
                          multiple
                          clearable
                          collapse-tags
                          collapse-tags-tooltip
                          class="!w-[256px]"
                        >
                            <el-option v-for="(item,index) in templateStatusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="queryTimeType">
                        <el-select
                                v-model="queryParams.queryTimeType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                class="!w-[200px] ml5px"
                        >
                            <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                        <el-date-picker
                                :editable="false"
                                class="!w-[370px]"
                                v-model="queryParams.dateRange"
                                type="datetimerange"
                                range-separator="~"
                                :start-placeholder="$t('disassemblyAssembleTemplate.placeholder.startTime')"
                                :end-placeholder="$t('disassemblyAssembleTemplate.placeholder.endTime')"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                :default-time="defaultTime"
                                :placeholder="$t('common.placeholder.selectTips')"
                        />
                        <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(1)">{{$t('disassemblyAssembleTemplate.label.today')}}</span>
                        <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(2)">{{$t('disassemblyAssembleTemplate.label.yesterday')}}</span>
                        <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(3)">{{$t('disassemblyAssembleTemplate.label.weekday')}}</span>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-hasPerm="['wms:insideWarehouseManagement:disassemblyAssembleTemplate:search']" type="primary" @click="handleQuery">
                            {{$t('common.search')}}
                        </el-button>
                        <el-button v-hasPerm="['wms:insideWarehouseManagement:disassemblyAssembleTemplate:reset']" @click="handleResetQuery">
                            {{$t('common.reset')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-card shadow="never" class="table-container">
                <template #header>
                    <el-button v-hasPerm="['wms:insideWarehouseManagement:disassemblyAssembleTemplate:add']" type="primary" @click="addDisassemblyAssembleTemplate('','add')">
                        {{$t('disassemblyAssembleTemplate.button.addTemplate')}}
                    </el-button>
                    <el-button v-hasPerm="['wms:insideWarehouseManagement:disassemblyAssembleTemplate:deactivate']" @click="deactivateTemplate(null,'batchStop')">
                      {{$t('disassemblyAssembleTemplate.button.deactivate')}}
                    </el-button>
                </template>
                <el-table
                    ref="tempDataTableRef"
                    v-loading="loading"
                    :data="productDisassemblyAssembleOrderList"
                    highlight-current-row
                    stripe
                    @selection-change="handleSelectionChange"
                >
                    <template #empty>
                        <Empty/>
                    </template>
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column type="index" :label="$t('common.sort')" width="60" />
                    <el-table-column :label="$t('disassemblyAssembleTemplate.label.templateCode')" prop="disassemblyTemplateCode" show-overflow-tooltip min-width="150px"></el-table-column>
                    <el-table-column :label="$t('disassemblyAssembleTemplate.label.templateName')" prop="disassemblyTemplateName" show-overflow-tooltip  min-width="150px"></el-table-column>
                    <el-table-column :label="$t('disassemblyAssembleTemplate.label.apply')" prop="apply" show-overflow-tooltip min-width="200px">
                        <template #default="scope">
                            <div class="item">{{$t('disassemblyAssembleTemplate.label.applyName')}}:{{scope.row.applyUserName ? scope.row.applyUserName : '-'}}</div>
                            <div class="item"> {{$t('disassemblyAssembleTemplate.label.applyTime')}}:{{ scope.row.applyTime ? parseDateTime(scope.row.applyTime, "dateTime"):'-' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('disassemblyAssembleTemplate.label.remark')" prop="remark" show-overflow-tooltip min-width="150px"></el-table-column>
                    <el-table-column :label="$t('disassemblyAssembleTemplate.label.approve')" prop="approve" show-overflow-tooltip min-width="200px">
                      <template #default="scope">
                        <div class="item">{{$t('disassemblyAssembleTemplate.label.approveName')}}:{{scope.row?.approveUserName ? scope.row.approveUserName:'-'}}</div>
                        <div class="item"> {{$t('disassemblyAssembleTemplate.label.approveTime')}}:{{ scope.row.approveTime ? parseDateTime(scope.row.approveTime, "dateTime"):'-' }}</div>
                        <div class="item"> {{$t('disassemblyAssembleTemplate.label.approveResult')}}:
                          {{ scope.row?.approveStatus ? $t(`disassemblyAssembleTemplate.approveStatus[${scope.row?.approveStatus}]`) :'-' }}
                        </div>
                      </template>
                    </el-table-column>
                  <el-table-column :label="$t('disassemblyAssembleTemplate.label.createUserName')" prop="createUserName" show-overflow-tooltip min-width="100px"></el-table-column>
                  <el-table-column :label="$t('disassemblyAssembleTemplate.label.createTime')" prop="createTime" show-overflow-tooltip min-width="180px">
                    <template #default="scope">
                      <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('disassemblyAssembleTemplate.label.updateTime')" prop="updateTime" show-overflow-tooltip min-width="180px">
                    <template #default="scope">
                      <span>{{ parseDateTime(scope.row.updateTime, "dateTime") }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('disassemblyAssembleTemplate.label.templateStatus')" prop="status" show-overflow-tooltip min-width="100px">
                    <template #default="scope">
                      <div class="purchase">
                        <span class="purchase-status purchase-status-color0" v-if="scope.row.status==0">{{$t('disassemblyAssembleTemplate.label.draft')}}</span>
                        <span class="purchase-status purchase-status-color3" v-if="scope.row.status==1">{{$t('disassemblyAssembleTemplate.label.unApprove')}}</span>
                        <span class="purchase-status purchase-status-color2" v-if="scope.row.status==2">{{$t('disassemblyAssembleTemplate.label.approvePass')}}</span>
                        <span class="purchase-status purchase-status-color5" v-if="scope.row.status==3">{{$t('disassemblyAssembleTemplate.label.unApprovePass')}}</span>
                        <span class="purchase-status purchase-status-color1" v-if="scope.row.status==4">{{$t('disassemblyAssembleTemplate.label.stop')}}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column fixed="right" :label="$t('common.handle')" width="200">
                      <template #default="scope">
                        <el-button
                          v-hasPerm="['wms:insideWarehouseManagement:disassemblyAssembleTemplate:edit']"
                          type="primary" link
                          v-if="hasEditPrem(scope.row)"
                          @click="addDisassemblyAssembleTemplate(scope.row.id,'edit')"
                        >
                          {{$t('disassemblyAssembleTemplate.button.edit')}}
                        </el-button>
                        <el-button
                          v-hasPerm="['wms:insideWarehouseManagement:disassemblyAssembleTemplate:delete']"
                          type="primary" link
                          v-if="hasDeletePrem(scope.row)"
                          @click="deleteDisassemblyAssembleTemplate(scope.row.id)"
                        >
                          {{$t('disassemblyAssembleTemplate.button.delete')}}
                        </el-button>
                        <el-button
                          v-hasPerm="['wms:insideWarehouseManagement:disassemblyAssembleTemplate:approve']"
                          type="primary" link
                          v-if="hasApprovePrem(scope.row)"
                          @click="detailDisassemblyAssembleTemplate(scope.row.id, 'approve')"
                        >
                          {{$t('disassemblyAssembleTemplate.button.approve')}}
                        </el-button>
                        <el-button
                          v-hasPerm="['wms:insideWarehouseManagement:disassemblyAssembleTemplate:deactivate']"
                          type="primary"
                          link
                          v-if="hasStopPem(scope.row)"
                          @click="deactivateTemplate(scope.row.id,'single')"
                        >
                          {{$t('disassemblyAssembleTemplate.button.deactivate')}}
                        </el-button>
                        <el-button
                          v-hasPerm="['wms:insideWarehouseManagement:productDisassemblyAssembleOrder:detail']"
                          type="primary" link
                          v-if="hasDetailPem(scope.row)"
                          @click="detailDisassemblyAssembleTemplate(scope.row.id,'detail')"
                        >
                          {{$t('common.detailBtn')}}
                        </el-button>
                      </template>
                  </el-table-column>
                </el-table>
                <pagination
                    v-if="total > 0"
                    v-model:total="total"
                    v-model:page="queryParams.page"
                    v-model:limit="queryParams.limit"
                    @pagination="handleQuery"
                />
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">
    defineOptions({
        name: "DisassemblyAssembleTemplate",
        inheritAttrs: false,
    });

    import {changeDateRange, convertToTimestamp, parseDateTime} from "@/core/utils/index.js";
    import DisassemblyAssembleTemplateAPI, { DisassemblyAssembleTemplatePageQuery, DisassemblyAssembleTemplatePageVO, } from "@/modules/wms/api/disassemblyAssembleTemplate";
    import {useRouter} from "vue-router";
    import moment from "moment";
    import { emitter } from "@/core/utils/eventBus";
    import { useUserStore } from "@/core/store";
    import { useNavigation } from "@/core/composables";

    const { refreshAndNavigate } = useNavigation();
    const userStore = useUserStore();
    const router = useRouter();
    const { t } = useI18n();
    const queryFormRef = ref(null);
    const loading = ref(false);
    const total = ref(0);


    const dateTypeList = ref([
        {
            key: 1,
            value: t('disassemblyAssembleTemplate.dateTypeList.applyTime')
        },
        {
            key: 2,
            value:t('disassemblyAssembleTemplate.dateTypeList.approveTime')
        },
        {
            key: 3,
            value:t('disassemblyAssembleTemplate.dateTypeList.createTime')
        },
        {
          key: 4,
          value:t('disassemblyAssembleTemplate.dateTypeList.updateTime')
        }
    ])

    const templateStatusOption=ref([
        {
            value: 0,
            label: t('disassemblyAssembleTemplate.templateStatusList[0]')
        },
        {
            value: 1,
            label: t('disassemblyAssembleTemplate.templateStatusList[1]')
        },
        {
            value: 2,
            label: t('disassemblyAssembleTemplate.templateStatusList[2]')
        },
        {
            value: 3,
            label: t('disassemblyAssembleTemplate.templateStatusList[3]')
        },
        {
            value: 4,
            label: t('disassemblyAssembleTemplate.templateStatusList[4]')
        }
    ])
    const queryParams = reactive<DisassemblyAssembleTemplatePageQuery>({
        queryTimeType:1,
        dateRange: [],
        page: 1,
        limit: 20,
    });
    const defaultTime: [Date, Date] = [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
    ]
    const productDisassemblyAssembleOrderList = ref<DisassemblyAssembleTemplatePageVO[]>();

    /** 时间转换 */
    function handleChangeDateRange(val: any) {
        queryParams.dateRange = changeDateRange(val);
    }

    const tempDataTableRef = ref()
    const multipleSelection = ref([]);
    function handleSelectionChange(val: any) {
      multipleSelection.value = val;
    }

    /*   0=草稿 1=待审核 2=审核完成 3=驳回 4=停用 */
    /*编辑*/
    const hasEditPrem = function(record: any) {
      return record.status == 0 || record.status == 3 || record.status == 4;
    }
    /*删除*/
    const hasDeletePrem = function(record: any) {
      return record.status == 0 || record.status == 3 || record.status == 4;
    }
    /*审核*/
    const hasApprovePrem = function(record: any) {
      return record.status == 1 && (userStore.user.userId == record.approveUser) ;
    }
    /*详情*/
    const hasDetailPem = function(record: any) {
      return record.status == 1 || record.status == 2
    }
    /*停用*/
    const hasStopPem = function(record: any) {
      return record.status == 2
    }

    /** 查询 */
    function handleQuery() {
        if (queryParams?.disassemblyTemplateCode?.length < 4) {
            return ElMessage.error(t("disassemblyAssembleTemplate.message.codeValideTips"));
        }
        loading.value = true;
        let params = {
            ...queryParams,
        }
        if(queryParams?.queryTimeType && queryParams.dateRange && queryParams.dateRange.length>0){
            params.startTime = convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.endTime = convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
        }
        delete params.dateRange
        DisassemblyAssembleTemplateAPI.getDisassemblyAssembleTemplatePage(params)
            .then((data) => {
                productDisassemblyAssembleOrderList.value = data?.records || [];
                total.value = parseInt(data?.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 重置查询 */
    function handleResetQuery() {
        queryFormRef.value?.resetFields();
        queryParams.queryTimeType=1
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }

    /** 新增/编辑*/
    function addDisassemblyAssembleTemplate(id?:string,type?:string){
      refreshAndNavigate({
        path: "/wms/insideWarehouseManagement/addDisassemblyAssembleTemplate",
        query: { id, type, title:type=='add'? t('disassemblyAssembleTemplate.button.addDisassemblyAssembleTemplate'): type=='edit'? t('disassemblyAssembleTemplate.button.editDisassemblyAssembleTemplate'):t('common.detailBtn')}
      })
    }

    /** 删除模板*/
    function deleteDisassemblyAssembleTemplate(id?:string){
      ElMessageBox.confirm(t('disassemblyAssembleTemplate.message.deleteTips'), t('common.tipTitle'), {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: "warning",
      }).then(
        () => {
          loading.value = true;
          DisassemblyAssembleTemplateAPI.deleteDisassemblyAssembleTemplate({id:id})
            .then(() => {
              ElMessage.success(t('disassemblyAssembleTemplate.message.deleteSucess'));
              handleResetQuery();
            })
            .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info(t('disassemblyAssembleTemplate.message.deleteCancel'));
        }
      );
    }

    /*详情*/
    function detailDisassemblyAssembleTemplate(id?:string,type?:string){
      router.push({
        path: "/wms/insideWarehouseManagement/detailDisassemblyAssembleTemplate",
        query: {
          id,
          type,
          title: type == 'detail' ? t('common.detailBtn') : t('common.detailBtn')}
      });
    }

    /*停用*/
    function deactivateTemplate(id?:string, type?:string) {
      if(type == 'batchStop' && multipleSelection.value?.length == 0){
        return ElMessage.warning(t('disassemblyAssembleTemplate.message.pleaseSelect'));
      }
      ElMessageBox.confirm(t('disassemblyAssembleTemplate.message.stopTips'), t('common.tipTitle'), {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: "warning",
      }).then(
        () => {
          loading.value = true;
          if(type == 'single'){
            DisassemblyAssembleTemplateAPI.stopDisassemblyAssembleTemplateDetail({id})
              .then(() => {
                ElMessage.success(t('disassemblyAssembleTemplate.message.stopSuccess'));
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
          }else{
            const ids = multipleSelection.value?.map(item => item.id)
            DisassemblyAssembleTemplateAPI.batchStopDisassemblyAssembleTemplateDetail(ids).then(() => {
              ElMessage.success(t('disassemblyAssembleTemplate.message.stopSuccess'));
              handleResetQuery();
            })
              .finally(() => (loading.value = false));
          }
        },
        () => {
          ElMessage.info(t('disassemblyAssembleTemplate.message.stopCancel'));
        }
      );
    }

    onActivated(() => {
        handleQuery();
    });

    emitter.on("reloadListByWarehouseId", (e) => {
        nextTick(() => {
            handleQuery();
        });
    });

</script>

