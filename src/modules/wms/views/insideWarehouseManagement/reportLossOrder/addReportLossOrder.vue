<template>
  <div class="app-container">
    <div class="addReportLossOrder" v-loading="loading">
      <div>
        <div class="page-title">
          <div @click="handleClose()" class="cursor-pointer mr8px">
            <el-icon>
              <Back />
            </el-icon>
          </div>
          <div>
            <span>
              {{ $t("reportLossOrder.button.addReportLossOrder") }}
            </span>
          </div>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" :rules="rules" ref="fromRef" label-width="98px" label-position="right">
          <div class="title-lable">
            <div class="title-content">
              {{ $t("reportLossOrder.label.basicInformation") }}
            </div>
          </div>
          <!--新增、编辑-->
          <div>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('reportLossOrder.label.orderType')" prop="orderType">
                  <el-select v-model="form.orderType" :placeholder="$t('common.placeholder.selectTips')" clearable
                    filterable class="w-full" @change="handleChangeOrderType">
                    <el-option v-for="item in reportLossOrderTypeList" :key="item.reportLossOrderTypeId"
                      :label="item.reportLossOrderTypeName" :value="item.reportLossOrderTypeId"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 拆装单号 -->
                <el-form-item v-if="isDisassemblyLossType" :label="$t('reportLossOrder.label.sourceOrderCode')"
                  prop="sourceOrderCode" :rules="rules.sourceOrderCode">
                  <el-select v-model="form.sourceOrderCode" :placeholder="$t('common.placeholder.selectTips')" clearable
                    filterable @change="changeSourceOrderCode" :disabled="type == 'edit'" class="w-full">
                    <el-option v-for="item in disassemblyAssembleOrderList" :key="item.code" :label="item.code"
                      :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
                <!-- 分拣单号 -->
                <el-form-item v-if="isSortingLossType" :label="$t('reportLossOrder.label.pickingOrderCode')"
                  prop="sourceOrderCode" :rules="rules.sourceOrderCode">
                  <el-select v-model="form.sourceOrderCode" :placeholder="$t('common.placeholder.selectTips')" clearable
                    filterable @change="changeSourceOrderCode" :disabled="type == 'edit'" class="w-full">
                    <el-option v-for="item in disassemblyAssembleOrderList" :key="item.code" :label="item.code"
                      :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
                <!-- 其他报损：库区选择 -->
                <el-form-item v-if="isOtherLossType" :label="$t('qualityInspectionOrder.label.warehouseArea')"
                  prop="warehouseAreaCode">
                  <el-select v-model="form.warehouseAreaCode" :placeholder="$t('common.placeholder.selectTips')"
                    clearable filterable :disabled="type == 'edit'" @change="changeQualityInspectionOrder(null)"
                    class="w-full">
                    <el-option v-for="item in outWarehouseAreaList" :key="item.areaCode" :label="item.warehouseArea"
                      :value="item.areaCode"></el-option>
                  </el-select>
                </el-form-item>


              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item :label="$t('reportLossOrder.label.remark')" prop="remark">
                  <el-input :rows="4" type="textarea" show-word-limit v-model="form.remark"
                    :placeholder="$t('common.placeholder.inputTips')" maxlength="200" clearable />
                </el-form-item>
              </el-col>

            </el-row>
          </div>
          <div class="line"></div>
          <div class="title-lable" style="justify-content: space-between">
            <div class="title-content">
              {{ $t("reportLossOrder.label.reportLossInformation") }}
            </div>
            <div class="button-add cursor-pointer" @click="addProduct()" v-if="isOtherLossType">
              {{ $t('qualityInspectionOrder.button.addProduct') }}
            </div>
          </div>
          <!--  报损明细 -->
          <div>
            <el-table :data="form.productList" highlight-current-row stripe>
              <el-table-column type="index" :label="$t('common.sort')" width="60" fixed="left" />
              <!--商品信息-->
              <el-table-column :label="$t('reportLossOrder.label.productInformation')" show-overflow-tooltip
                fixed="left" min-width="130px">
                <template #default="scope">
                  <div class="product-div">
                    <div class="product">
                      <div class="product-name">
                        {{ scope.row.productName }}
                      </div>
                      <div>
                        <span class="product-key">
                          {{ $t("reportLossOrder.label.productCode") }}：
                        </span>
                        <span class="product-value">
                          {{ scope.row.productCode }}
                        </span>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!--规格-->
              <el-table-column :label="$t('reportLossOrder.label.productSpec')" prop="productSpec" show-overflow-tooltip
                width="100px" />
              <!--出库库区-->
              <el-table-column :label="$t('reportLossOrder.label.warehouseArea')" show-overflow-tooltip
                min-width="130px">
                <template #default="scope">
                  <span>{{ scope.row.warehouseAreaName }}</span>
                  <span>|</span>
                  <span>{{ scope.row.warehouseAreaCode }}</span>
                </template>
              </el-table-column>
              <!--数量-->
              <el-table-column :label="$t('reportLossOrder.label.disassemblyOutQty')" prop="inventoryQty"
                show-overflow-tooltip min-width="100px" />
              <!-- 转换量 inventoryWeight-->
              <el-table-column :label="'转换量'" prop="inventoryWeight" show-overflow-tooltip min-width="100px" />
              <!-- 附件 -->

              <!--  <el-table-column :label="'出库重量(Kg)'" show-overflow-tooltip min-width="100px"
                prop="disassemblyOutWeight"></el-table-column> -->
              <!-- 新增\编辑 -->
              <!-- <el-table-column :label="'报损重量(Kg)'" show-overflow-tooltip min-width="100px">
                <template #default="scope">
                  <el-input v-model="scope.row.lossQty" placeholder="请输入报损重量" clearable></el-input>
                </template>
              </el-table-column> -->

              <!-- 报损数量 -->
              <el-table-column :label="'报损量'" prop="lossQty" show-overflow-tooltip width="200px">
                <template #default="scope">
                  <el-form-item
                    v-if="(form.productList && form.productList.some((perm) => perm.lossStatus == 1)) && scope.row.lossStatus == 1"
                    class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.lossQty'" :rules="[
                      {
                        required: true,
                        message: t('reportLossOrder.rules.lossQty'),
                        trigger: ['blur', 'change'],
                      },
                      {
                        required: true,
                        validator: validatorLossQty,
                        trigger: ['blur', 'change'],
                      },
                      {
                        pattern:
                          /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                        message: t('reportLossOrder.rules.lossQtyFormat'),
                        trigger: ['blur', 'change'],
                      },
                    ]">
                    <el-input v-model="scope.row.lossQty" :placeholder="$t('common.placeholder.inputTips')" clearable
                      :disabled="type == 'detail'" @change="convertProductUnitStragery(scope.row, 'FIRST_TO_SECOND', 'lossQty', 'lossWeight')">
                      <template #append>
                        {{ scope.row.productUnitName }}
                      </template>
                    </el-input>
                  </el-form-item>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="'报损转换量'" prop="lossWeight" show-overflow-tooltip width="200px">
                <template #default="scope">
                  <el-form-item
                    v-if="(form.productList && form.productList.some((perm) => perm.lossStatus == 1)) && scope.row.lossStatus == 1"
                    class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.lossWeight'" :rules="[
                      {
                        required: true,
                        message: t('reportLossOrder.rules.lossWeight'),
                        trigger: ['blur', 'change'],
                      },
                      {
                        required: true,
                        validator: validatorLossQtyConverted,
                        trigger: ['blur', 'change'],
                      },
                      {
                        pattern:
                          /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                        message: t('reportLossOrder.rules.lossWeightFormat'),
                        trigger: ['blur', 'change'],
                      },
                    ]">
                    <el-input v-model="scope.row.lossWeight" :placeholder="$t('common.placeholder.inputTips')" clearable
                      :disabled="type == 'detail'" @change="convertProductUnitStragery(scope.row, 'SECOND_TO_FIRST', 'lossQty', 'lossWeight')">
                      <template #append>
                        {{ scope.row.conversionRelSecondUnitName }}
                      </template>
                    </el-input>
                  </el-form-item>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <!--  单价 -->
              <el-table-column :label="'单价'" prop="unitPrice" show-overflow-tooltip min-width="100px" />
              <!-- 金额 -->
              <el-table-column :label="'金额'" prop="amount" show-overflow-tooltip min-width="100px" />
              <!-- 附件 -->
              <el-table-column :label="'附件'" prop="lossAttachment" show-overflow-tooltip min-width="100px">
                <template #default="scope">
                  <div
                    v-if="(form.productList && form.productList.some((perm) => perm.lossStatus == 1)) && scope.row.lossStatus == 1">
                    <div v-if="scope.row.attachmentNum">
                      <el-badge :value="scope.row.attachmentNum" :offset="[10, 8]" class="item" type="primary">
                        <el-button type="primary" link @click="handleAttachmentUpload(scope.row, scope.$index)">
                          {{ $t('common.uploadBtn') }}
                        </el-button>
                      </el-badge>
                    </div>
                    <div v-else>
                      <el-button type="primary" link @click="handleAttachmentUpload(scope.row, scope.$index)">
                        {{ $t('common.uploadBtn') }}
                      </el-button>
                    </div>
                  </div>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <!-- 报损操作 -->
              <el-table-column :label="$t('common.handle')" show-overflow-tooltip min-width="100px">
                <template #default="scope">
                  <el-switch :active-text="$t('reportLossOrder.label.activeBtn')"
                    :inactive-text="$t('reportLossOrder.label.inactiveBtn')" inline-prompt
                    v-model="scope.row.lossStatus" :active-value="1" :inactive-value="0"></el-switch>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
        <el-button type="primary" plain @click="handleSubmit(0)" :loading="submitLoading">
          {{ $t("reportLossOrder.button.saveDraft") }}
        </el-button>
        <el-button type="primary" @click="handleSubmit(1)" :loading="submitLoading">
          {{ $t("reportLossOrder.button.confirm") }}
        </el-button>
      </div>

      <AddProduct ref="addProductRef" v-model:visible="dialogState.visible" :title="dialogState.title"
        :outWarehouseAreaShow="true" :availableStockQtyShow="false" :outWarehouseAreaFromShow="true"
        :hasTotalStockQty="true" @on-submit="onSubmit" :showConverUnit="true"/>

      <!-- 上传弹窗 -->
      <UploadDialog ref="uploadDialogRef" v-model:visible="uploadDialog.visible" @onSubmit="onSubmitUpload" />
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "AddReportLossOrder",
  inheritAttrs: false,
});

import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import ReportLossOrderAPI, {
  ReportLossOrderFrom,
} from "@/modules/wms/api/reportLossOrder";
import ProductDisassemblyAssembleOrderAPI from "@/modules/wms/api/productDisassemblyAssembleOrder";
import CommonAPI from "@/modules/wms/api/common";
import AddProduct from "@wms/components/addProduct.vue";
import UploadDialog from "./components/uploadDialog.vue";

// import { useReportLossOrder } from './composables/index'
// const { convertProductUnitStragery } = useReportLossOrder();
import { useConvertProductUnit } from "@/modules/wms/composables";
const { convertProductUnitStragery } = useConvertProductUnit();
// 将魔法数字定义为常量
const ORDER_TYPES = {
  DISASSEMBLY: 1,  // 拆装报损
  SORTING: 2,      // 分拣报损
  OTHER: 3         // 其他报损
} as const;

// 源单据类型
const SOURCE_ORDER_TYPES = {
  DISASSEMBLY: 12, // 拆装单
  SORTING: 21      // 分拣单
} as const;

// 类型定义
interface WarehouseAreaItem {
  id: string;
  areaCode: string;
  areaName: string;
  areaType: string;
  warehouseCode: string;
  warehouseArea?: string;
}

interface OrderItem {
  code: string;
  [key: string]: any;
}

interface ReportLossOrderTypeItem {
  reportLossOrderTypeId: number;
  reportLossOrderTypeName: string;
}

// 扩展产品对象类型以支持分拣报损
interface ExtendedProductVO {
  id?: string;
  productCode?: string;
  productName?: string;
  productSpec?: string;
  productSpecs?: string;
  productUnitName?: string;
  productUnit?: string;
  productUnitId?: string;
  warehouseAreaId?: string;
  warehouseAreaCode?: string;
  warehouseAreaName?: string;
  warehouseCode?: string;
  // 拆装报损字段
  disassemblyOutQty?: number;
  disassemblyOutWeight?: number;
  sourceWarehouseAreaCode?: string;
  // 分拣报损字段
  receivedQty?: number;
  receivedWeight?: number;
  sortingBeforeQty?: number;
  sortingBeforeWeight?: number;
  lossWeight?: number;
  // 通用字段
  lossQty?: number;
  lossStatus?: number;
  outQty?: number;
  outWeight?: number;
}

// 扩展报损单表单类型
interface ExtendedReportLossOrderFrom extends Omit<ReportLossOrderFrom, 'productList'> {
  productList: ExtendedProductVO[];
  warehouseAreaCode?: string;
  warehouseAreaName?: string;
}

// 表单提交参数类型
interface SubmitParams {
  id?: string;
  lossOrderCode?: string;
  orderType: number;
  sourceOrderCode?: string;
  sourceOrderType: number; // 源单据类型12:拆装单，21：分拣单
  remark?: string;
  productList: ProductSubmitItem[];
}

interface ProductSubmitItem {
  id?: string;
  productCode?: string;
  productName?: string;
  productSpec?: string;
  productUnitName?: string;
  warehouseAreaId?: string;
  warehouseAreaName?: string;
  outQty?: number;
  outWeight?: number;
  lossQty?: number;
  lossWeight?: number;
  lossStatus?: number;
}

// 验证规则类型
interface ValidationRule {
  field?: string;
  required?: boolean;
  message?: string;
  trigger?: string[];
  validator?: (rule: ValidationRule, value: any, callback: (error?: Error) => void) => void;
  pattern?: RegExp;
}

// 响应式数据
const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();

const outWarehouseAreaList = ref<WarehouseAreaItem[]>([]);
const disassemblyAssembleOrderList = ref<OrderItem[]>([]);
const fromRef = ref();
const submitLoading = ref(false);
const loading = ref(false);

// 路由参数类型安全处理
const id = computed(() => {
  const queryId = route.query.id;
  return Array.isArray(queryId) ? queryId[0] : queryId;
});
const type = computed(() => {
  const queryType = route.query.type;
  return Array.isArray(queryType) ? queryType[0] : queryType;
});

const reportLossOrderTypeList = ref<ReportLossOrderTypeItem[]>([
  {
    reportLossOrderTypeId: ORDER_TYPES.DISASSEMBLY,
    reportLossOrderTypeName: "拆装报损",
  },
  {
    reportLossOrderTypeId: ORDER_TYPES.SORTING,
    reportLossOrderTypeName: "分拣报损",
  },
  {
    reportLossOrderTypeId: ORDER_TYPES.OTHER,
    reportLossOrderTypeName: '其他报损'
  }
]);

// 表单数据
const form = reactive<ExtendedReportLossOrderFrom>({
  orderType: ORDER_TYPES.DISASSEMBLY,
  sourceOrderType: SOURCE_ORDER_TYPES.DISASSEMBLY, // 源单据类型12:拆装单，21：分拣单
  productList: [],
});

// 弹窗状态
const dialogState = reactive({
  title: "",
  visible: false,
});

// 上传弹窗状态
const uploadDialog = reactive({
  visible: false,
});

const addProductRef = ref();
const uploadDialogRef = ref();
const currentUploadIndex = ref<number>();

// 计算属性
const isOtherLossType = computed(() => form.orderType === ORDER_TYPES.OTHER);
const isSortingLossType = computed(() => form.orderType === ORDER_TYPES.SORTING);
const isDisassemblyLossType = computed(() => form.orderType === ORDER_TYPES.DISASSEMBLY);

// 表单验证规则
const createRequiredRule = (message: string) => ({
  required: true,
  message,
  trigger: ['blur', 'change', 'input']
});

const rules = reactive({
  orderType: [createRequiredRule(t("reportLossOrder.rules.orderType"))],
  sourceOrderCode: computed(() => {
    if (form.orderType === ORDER_TYPES.DISASSEMBLY) {
      return [createRequiredRule('请选择拆装单号')];
    }
    if (form.orderType === ORDER_TYPES.SORTING) {
      return [createRequiredRule('请选择分拣单号')];
    }
    return [];
  }),
  warehouseAreaCode: computed(() => {
    if (form.orderType === ORDER_TYPES.OTHER) {
      return [createRequiredRule('请选择库区')];
    }
    return [];
  })
});

// 商品一级单位转二级单位
/* const convertProductUnit_bak = (row: any, index: number) => {
  CommonAPI.convertProductUnit({
    productId: row.id,
    originalValue: row.lossQty,
    convertUnitTypeEnum: "FIRST_TO_SECOND",
    productCode: row.productCode
  }).then((data) => {
    row.lossWeight = parseFloat(data?.convertedValue).toFixed(3);
    // 报损数量*单价（数量）
    row.amount = (row.unitPrice * row.lossQty).toFixed(2);

  });
} */
const handleAttachmentUpload = (row: any, index?: number) => {
  console.log('上传附件', row, index);
  currentUploadIndex.value = index;
  uploadDialog.visible = true;
  uploadDialogRef.value?.setEditType("add");
  if (row.lossAttachment) {
    uploadDialogRef.value?.setFormData(JSON.parse(row.lossAttachment));
  }
}

// 上传提交回调
function onSubmitUpload(data: any) {
  if (data && currentUploadIndex.value !== undefined) {
    form.productList[currentUploadIndex.value].lossAttachment = JSON.stringify(data);
    form.productList[currentUploadIndex.value].attachmentNum = data.length;
  }
}

// 报损数量验证器
function validatorLossQty(rule: any, value: any, callback: any) {
  const index = rule.field?.split(".")[1];
  let maxQty = 0;

  if (form.productList[index]) {
    /*  if (form.orderType === ORDER_TYPES.DISASSEMBLY) {
       maxQty = form.productList[index].disassemblyOutQty || 0;
     } else if (form.orderType === ORDER_TYPES.SORTING) {
       maxQty = form.productList[index].receivedQty || 0;
     } */
    maxQty = form.productList[index].inventoryQty;
  }

  if (value && maxQty) {
    if (value > parseFloat(maxQty)) {
      callback(new Error('报损数量不能大于数量'));
    } else {
      callback();
    }
  } else {
    callback();
  }
}
// 转换量校验
function validatorLossQtyConverted(rule: any, value: any, callback: any) {
  const index = rule.field?.split(".")[1];
  let maxQty = 0;

  if (form.productList[index]) {
    maxQty = form.productList[index].inventoryWeight;
  }

  if (value && maxQty) {
    if (value > parseFloat(maxQty)) {
      callback(new Error('报损转换量不能大于转换量'));
    } else {
      callback();
    }
  } else {
    callback();
  }
}

// 报损类型改变获取不同单号列表
const handleChangeOrderType = () => {
  disassemblyAssembleOrderList.value = [];
  form.productList = [];
  form.sourceOrderCode = undefined;

  if (form.orderType === ORDER_TYPES.DISASSEMBLY) {
    form.sourceOrderType = SOURCE_ORDER_TYPES.DISASSEMBLY;
    getDisassemblyAssembleOrderList();
  } else if (form.orderType === ORDER_TYPES.SORTING) {
    form.sourceOrderType = SOURCE_ORDER_TYPES.SORTING;
    getCompletedOrderListFetch();
  }
};

// 获取已完成的分拣单列表
const getCompletedOrderListFetch = async () => {
  try {
    const response = await ReportLossOrderAPI.getCompletedOrderList({ sortingStatus: 2 });
    disassemblyAssembleOrderList.value = response?.data || response;
  } catch (error: any) {
    console.error('获取分拣单列表失败:', error);
    ElMessage.error(error.message || '获取分拣单列表失败');
  }
};

// 获取出库库区列表
async function getOutWarehouseAreaList() {
  try {
    const response = await ReportLossOrderAPI.queryHasStockListByCurrentWarehouse();
    const data = response.data || response;

    outWarehouseAreaList.value = data;
    if (outWarehouseAreaList.value && outWarehouseAreaList.value.length > 0) {
      outWarehouseAreaList.value = outWarehouseAreaList.value.map((item) => ({
        ...item,
        warehouseArea: `${item.areaName}|${item.areaCode}`
      }));
    }
  } catch (error: any) {
    console.error('获取库区列表失败:', error);
    ElMessage.error(error.message || '获取库区列表失败');
  }
}

// 获取拆装单号下拉数据
async function getDisassemblyAssembleOrderList() {
  try {
    const response = await ProductDisassemblyAssembleOrderAPI.getDisassemblyAssembleOrderList();
    disassemblyAssembleOrderList.value = response?.data || response;
  } catch (error: any) {
    console.error('获取拆装单列表失败:', error);
    ElMessage.error(error.message || '获取拆装单列表失败');
  }
}

// 根据单号查询商品明细
function changeSourceOrderCode() {
  form.productList = [];
  if (form.sourceOrderCode) {
    if (form.orderType === ORDER_TYPES.DISASSEMBLY) { // 拆装报损
      queryLossProductListByOrderId();
    } else if (form.orderType === ORDER_TYPES.SORTING) { // 分拣报损
      queryPickOrderProcuctsById();
    }
  }
}

// 查询分拣单商品
async function queryPickOrderProcuctsById() {
  if (!form.sourceOrderCode) {
    ElMessage.warning('请先选择分拣单号');
    return;
  }

  try {
    const response = await ProductDisassemblyAssembleOrderAPI.queryProductOfPickOrders(form.sourceOrderCode);
    const data = response.data || response;

    if (data?.detailVOList && Array.isArray(data.detailVOList)) {
      form.productList = data.detailVOList.map((item: any) => ({
        ...item,
        receivedQty: item.sortingBeforeQty,
        receivedWeight: item.sortingBeforeWeight,
        productUnitName: item.productUnit,
        lossStatus: 0,
        inventoryQty: item.sortingBeforeQty, // 数量
        inventoryWeight: item.sortingBeforeWeight, // 转换量
        isDiscreteUnit: item.isDiscreteUnit,
        warehouseLocationCode: item.warehouseLocationCode || item.warehouseAreaLocationCode,
        pricingScheme: item.pricingScheme,

      }));
    }
  } catch (error: any) {
    console.error('查询分拣单商品失败:', error);
    ElMessage.error(error.message || '查询分拣单商品失败');
  }
}

// 添加商品
function addProduct() {
  dialogState.title = t('qualityInspectionOrder.title.addProduct');
  addProductRef.value.getOutWarehouseAreaList();
  addProductRef.value.queryManagerCategoryList();

  const queryParams = {
    warehouseAreaCode: form.warehouseAreaCode,
    typeList: 3,
  };

  addProductRef.value.setFormData({ queryParams });
  dialogState.visible = true;
}

// 其他报损选择商品后的字段转换
const otherLossProductListTransfer = (data: any) => {
  return data.map((item: any) => {
    return {
      ...item,
    }
  });
}

// 获取弹窗商品信息
function onSubmit(data: any) {
  if (data?.collection) {
    for (let item of data.collection) { // 处理提交的商品数据
      item.inventoryQty = item.totalStockQty;
      item.inventoryWeight = item.totalStockWeight;
      item.warehouseLocationCode = item.warehouseLocationCode || item.warehouseAreaLocationCode;
    }
    const arr = data.collection.concat(form.productList);
    const uniqueArr = [...new Map(arr.map((item: any) => [item.productCode, item])).values()];
    form.productList = uniqueArr as ExtendedProductVO[];
    form.productList.forEach((item) => {
      // 计算加权平均单价
      CommonAPI.calculateWeightedAveragePrice({
        productCode: item.productCode,
        warehouseAreaCode: item.warehouseAreaCode,
        warehouseLocationCode: item.warehouseLocationCode,
        isDiscreteUnit: item.isDiscreteUnit,
        pricingScheme: item.pricingScheme
      }).then((data) => {
        item.unitPrice = data;
      });
    });
  }
}

// 质检订单改变处理（暂时保留空实现）
function changeQualityInspectionOrder(_val: any) {
  // 如果后续需要实现业务逻辑，在这里添加
}

// 关闭页面
async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}

// 暂存/提交
function handleSubmit(val: number) {
  // 报损单状态：0:草稿;1:已完成;2:报损中
  fromRef.value.validate((valid: boolean) => {
    if (!valid) return;

    if (form.productList && form.productList.length === 0) {
      return ElMessage.error(t("reportLossOrder.message.reportLossOrderTips"));
    }

    if (form.productList && form.productList.length > 0) {
      const flag = form.productList.some((item) => item.lossStatus === 1);
      if (!flag) {
        return ElMessage.error(t("reportLossOrder.message.reportLossOrderLessTips"));
      }
    }

    submitLoading.value = true;
    const productList: any[] = [];

    if (form.productList && form.productList.length > 0) {
      form.productList.forEach((item) => {
        let obj: any = {
          ...item
        };
        const sourceWarehouseArea = outWarehouseAreaList.value.find(
          (out) => item.warehouseAreaCode === out.areaCode // warehouseCode
        );
        if (form.orderType === ORDER_TYPES.DISASSEMBLY) { //拆装报损

          obj = {
            ...obj,
            /*  warehouseAreaId: sourceWarehouseArea?.id,
             warehouseAreaCode: sourceWarehouseArea?.areaCode,
             warehouseAreaName: item.warehouseAreaName, */
            outQty: item.disassemblyOutQty,
            // warehouseLocationCode: item.warehouseLocationCode
          };
        } else if (form.orderType === ORDER_TYPES.SORTING) { // 分拣报损
          obj = {
            ...obj,
            warehouseAreaId: sourceWarehouseArea?.id,
            warehouseAreaCode: sourceWarehouseArea?.areaCode,
            warehouseAreaName: item.warehouseAreaName,
            outQty: item.receivedQty,
            outWeight: item.receivedWeight,
            lossWeight: item.lossWeight,
            // warehouseLocationCode: item.warehouseLocationCode
          };
        }

        productList.push(obj);
      });
    }

    const params: any = {
      orderType: form.orderType,
      sourceOrderCode: form.sourceOrderCode,
      sourceOrderType: form.sourceOrderType,
      remark: form.remark,
      productList: productList,
    };

    if (type.value !== "add") {
      params.id = form.id;
      params.lossOrderCode = form.lossOrderCode;
    }

    if (val === 0) {
      saveReportLossOrder(params);
    } else {
      submitReportLossOrder(params);
    }
  });
}

// 保存报损单
async function saveReportLossOrder(params: any) {
  try {
    await ReportLossOrderAPI.saveReportLossOrder(params);
    ElMessage.success(t("reportLossOrder.message.saveSucess"));
    handleClose();
  } catch (error: any) {
    // console.error('保存失败:', error);
    // ElMessage.error(error.message || '保存失败');
  } finally {
    submitLoading.value = false;
  }
}

// 提交报损单
function submitReportLossOrder(params: any) {
  ElMessageBox.confirm(
    t("reportLossOrder.message.submitTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    async () => {
      try {
        await ReportLossOrderAPI.submitReportLossOrder(params);
        ElMessage.success(t("reportLossOrder.message.submitSucess"));
        handleClose();
      } catch (error: any) {
        // console.error('提交失败:', error);
        // ElMessage.error(error.message || '提交失败');
      } finally {
        submitLoading.value = false;
      }
    },
    () => {
      submitLoading.value = false;
      ElMessage.info(t("reportLossOrder.message.submitConcel"));
    }
  );
}

// 查询报损详情
async function getReportLossOrderDetail() {
  if (!id.value) return;

  loading.value = true;
  try {
    const params = { id: id.value };
    const data = await ReportLossOrderAPI.getReportLossOrderDetail(params);

    Object.assign(form, data);

    if (form.productList && form.productList.length > 0) {
      form.productList.forEach((item) => {
        // item.warehouseAreaId = item.warehouseAreaCode;
        item.disassemblyOutQty = item.outQty;
        item.attachmentNum = item.lossAttachment ? JSON.parse(item.lossAttachment).length : 0;
      });
    }

    if (type.value === "detail") {
      const reportLossOrderType = reportLossOrderTypeList.value.find(
        (item) => form.orderType === item.reportLossOrderTypeId
      );
      if (reportLossOrderType) {
        // 注意：这里应该保持数字类型，如果需要显示文本，应该在模板中处理
        // form.orderType = reportLossOrderType.reportLossOrderTypeName;
      }
    }
  } catch (error: any) {
    console.error('获取详情失败:', error);
    ElMessage.error(error.message || '获取详情失败');
  } finally {
    loading.value = false;
  }
}

// 根据拆装单号查询报损商品列表
function queryLossProductListByOrderId() {
  fromRef.value.validate(async (valid: boolean) => {
    if (!valid) return;

    loading.value = true;
    try {
      const params = { disassemblyOrderCode: form.sourceOrderCode };
      const response = await ProductDisassemblyAssembleOrderAPI.queryLossProductListByOrderId(params);

      // 处理API响应数据 - PageResult类型包含list属性
      form.productList = response || [];

      if (form.productList && form.productList.length > 0) {
        form.productList.forEach((item) => {
          /* const sourceWarehouseArea = outWarehouseAreaList.value.find(
            (out) => item.sourceWarehouseAreaCode === out.areaCode
          ); */
          // 拆装商品类型：0=源商品 1=目标商品
          if (item.disassemblyProductType === 1) {
            item.warehouseAreaCode = item.targetWarehouseAreaCode;
            item.warehouseAreaId = item.targetWarehouseAreaId;
            item.warehouseAreaName = item.targetWarehouseAreaName;
            item.warehouseLocationCode = item.targetWarehouseLocationCode;
          }
          else if (item.disassemblyProductType === 0) {
            item.warehouseAreaCode = item.sourceWarehouseAreaCode;
            item.warehouseAreaId = item.sourceWarehouseAreaId;
            item.warehouseAreaName = item.sourceWarehouseAreaName;
            item.warehouseLocationCode = item.sourceWarehouseLocationCode;
          }

          item.lossStatus = 0;
          // disassemblyProductType 拆装商品类型：0=源商品 1=目标商品
          item.inventoryQty = item.disassemblyProductType === 0 ? item.disassemblyOutQty : item.disassemblyInQty;
          item.inventoryWeight = item.disassemblyProductType === 0 ? item.disassemblyOutWeight : item.disassemblyInWeight;
          item.isDiscreteUnit = item.isDiscreteUnit;
          item.warehouseLocationCode = item.warehouseLocationCode || item.warehouseAreaLocationCode;
          item.pricingScheme = item.pricingScheme;
          item.amount = null;


        });
      }
    } catch (error: any) {
      console.error('查询报损商品失败:', error);
      ElMessage.error(error.message || '查询报损商品失败');
    } finally {
      loading.value = false;
    }
  });
}

// 组件挂载
onMounted(async () => {
  await getOutWarehouseAreaList();
  // 根据报损类型获取拆装单号或分拣单号
  handleChangeOrderType();
  // 如果是编辑或详情页面，获取详情数据
  if (type.value !== 'add') {
    await getReportLossOrderDetail();
  }
});
</script>
<style scoped lang="scss">
.addReportLossOrder {
  background: #ffffff;
  border-radius: 4px;

  .page-content {
    .button-add {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary);
    }

    .table-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 50px;
      background: #f4f6fa;
      box-shadow:
        inset 1px 1px 0px 0px #e5e7f3,
        inset -1px -1px 0px 0px #e5e7f3;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585f;
      font-style: normal;
      padding: 15px 12px;
    }
  }
}
</style>
<style lang="scss">
.addReportLossOrder {
  .page-content {
    .el-switch .el-switch__inner {
      width: 60px !important;
    }
  }
}
</style>
