import CommonAPI, { ProductAllPageVO } from "@/modules/wms/api/common";
import { useI18n } from "vue-i18n";

export const useReportLossOrder = () => {
  const { t } = useI18n();
  // 商品一级单位转二级单位
  async function convertProductUnit(data: any) {
    const res = await CommonAPI.convertProductUnit(data);
    return res?.convertedValue;
  }

  /**
   一级单位增减 isDiscreteUnit: 1开启，0关闭
   输入若商品的【一级单位增减isDiscreteUnit】为【是】
   输入数量时，转换量自动转换
   输入转换量时，数量不管，用户手动填写
   若商品的【一级单位增减】为【否】
   输入数量时，转换量内无值时自动转换，有值时不转换
   输入转换量时，数量内无值时自动转换，且数量向上取整，有值时不转换
   */
  async function convertProductUnitStragery(row: any, stragetry: string) {
    const data = {
      convertUnitTypeEnum: stragetry,
      originalValue: null,
      productId: row.productId || row.id,
      productCode: row.productCode,
    }
    if (stragetry == "FIRST_TO_SECOND") {
      if (row.isDiscreteUnit == 1) {
        if(!isNull(row.lossQty)){
          data.originalValue = row.lossQty;
          const val = await convertProductUnit(data);
          row.lossWeight = parseFloat(val).toFixed(3);
        }
      }
      else if (row.isDiscreteUnit == 0) {
        if (isNull(row.lossWeight) && !isNull(row.lossQty)) { // 输入数量时，转换量内无值时自动转换，有值时不转换
          data.originalValue = row.lossQty;
          const val = await convertProductUnit(data);
          row.lossWeight = parseFloat(val).toFixed(3);
        }
      }
    }
    else if (stragetry == "SECOND_TO_FIRST") {
      if (row.isDiscreteUnit == 0) {
        if (isNull(row.lossQty) && !isNull(row.lossWeight)) { // 输入数量时，转换量内无值时自动转换，有值时不转换
          data.originalValue = row.lossWeight;
          const val = await convertProductUnit(data);
          row.lossQty = Math.ceil(val);
        }
      }
    }
    // 报损数量*单价（数量）
    row.amount = (row.unitPrice * row.lossQty).toFixed(2);
    // calculateAmount(row);

  }


  // 判断值部位null，undefined，''
  function isNull(val: any) {
    return val === null || val === undefined || val === '';
  }
  // 单价+量+转换量:计算金额
 /* async function calculateAmount(row: any) {
    if (isNull(row.actualInQty) || isNull(row.actualInWeight) || isNull(row.unitPrice)) return;
    const data = {
      convertedQty: row.lossWeight,
      productId: row.productId,
      qty: row.lossQty,
      unitPrice: row.unitPrice,
      productCode: row.productCode,
    }
    const res = await CommonAPI.calculateAmount(data);
    row.amount = res?.amount;
  }*/

  return {
    convertProductUnit,
    convertProductUnitStragery,
    // calculateAmount,
  }
}
