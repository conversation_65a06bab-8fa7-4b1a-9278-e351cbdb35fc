<template>
    <div class="app-container">
        <div class="reportLossOrder">
            <div class="search-container">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="98px">
                    <el-form-item prop="lossOrderCode" :label="$t('reportLossOrder.label.lossOrderCode')">
                        <el-input v-model="queryParams.lossOrderCode"
                            :placeholder="$t('reportLossOrder.placeholder.lossOrderCode')" clearable
                            class="!w-[256px]" />
                    </el-form-item>
                    <el-form-item :label="$t('reportLossOrder.label.orderStatus')" prop="orderStatusList">
                        <el-select v-model="queryParams.orderStatusList"
                            :placeholder="$t('common.placeholder.selectTips')" multiple collapse-tags
                            collapse-tags-tooltip clearable class="!w-[256px]">
                            <el-option v-for="item in statusList" :key="item.statusId" :label="item.statusName"
                                :value="item.statusId"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 是否领单receivingStatus：0->未领用 1->已领用 -->
                    <!-- 报损类型orderType：1:拆装单报损;2:分拣单报损;3:其他报损 -->
                    <el-form-item prop="orderTypeList" :label="$t('reportLossOrder.label.orderType')">
                        <el-select v-model="queryParams.orderTypeList"
                            :placeholder="$t('common.placeholder.selectTips')" clearable multiple collapse-tags
                            collapse-tags-tooltip class="!w-[256px]">
                            <el-option v-for="item in reportLossOrderTypeList" :key="item.reportLossOrderTypeId"
                                :label="item.reportLossOrderTypeName" :value="item.reportLossOrderTypeId"></el-option>
                        </el-select>
                    </el-form-item>

                    <!-- 是否领单筛选 -->
                   <!--  <el-form-item prop="receivingStatusList" :label="$t('reportLossOrder.label.receivingStatus')">
                        <el-select v-model="queryParams.receivingStatus"
                            :placeholder="$t('common.placeholder.selectTips')" clearable collapse-tags
                            collapse-tags-tooltip class="!w-[256px]">
                            <el-option v-for="item in receivingStatusList" :key="item.statusId" :label="item.statusName"
                                :value="item.statusId"></el-option>
                        </el-select>
                    </el-form-item> -->

                  
                    <el-form-item prop="dateRange">
                        <el-select v-model="queryParams.dateType" :placeholder="$t('common.placeholder.selectTips')"
                            class="!w-[200px] ml5px">
                            <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value"
                                :value="item.key"></el-option>
                        </el-select>
                        <el-date-picker class="!w-[370px]" v-model="queryParams.dateRange" type="datetimerange"
                            range-separator="~" start-placeholder="开始时间" end-placeholder="截止时间"
                            value-format="YYYY-MM-DD HH:mm:ss" :default-time="defaultTime"
                            :placeholder="$t('common.placeholder.selectTips')" />
                        <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)"
                            @click="handleChangeDateRange(1)">{{ $t('reportLossOrder.label.today') }}</span>
                        <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)"
                            @click="handleChangeDateRange(2)">{{ $t('reportLossOrder.label.yesterday') }}</span>
                        <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)"
                            @click="handleChangeDateRange(3)">{{ $t('reportLossOrder.label.weekday') }}</span>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-hasPerm="['wms:insideWarehouseManagement:reportLossOrder:search']" type="primary"
                            @click="handleQuery">
                            {{ $t('common.search') }}
                        </el-button>
                        <el-button v-hasPerm="['wms:insideWarehouseManagement:reportLossOrder:reset']"
                            @click="handleResetQuery">
                            {{ $t('common.reset') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-card shadow="never" class="table-container">
                <template #header>
                    <el-button v-hasPerm="['wms:insideWarehouseManagement:reportLossOrder:add']" type="primary"
                        @click="addReportLossOrder(null, 'add')">
                        {{ $t('reportLossOrder.button.addReportLossOrder') }}
                    </el-button>
                </template>

                <el-table v-loading="loading" :data="reportLossOrderList" highlight-current-row stripe>
                    <template #empty>
                        <Empty />
                    </template>
                    <el-table-column type="index" :label="$t('common.sort')" width="60" fixed="left" />
                    <el-table-column :label="$t('reportLossOrder.label.lossOrderCode')" prop="lossOrderCode"
                        show-overflow-tooltip min-width="150px" fixed="left"></el-table-column>
                    <el-table-column :label="$t('reportLossOrder.label.orderType')" prop="orderType"
                        show-overflow-tooltip min-width="100px">
                        <template #default="scope">
                            <span v-if="scope.row.orderType == 1">{{
                                $t('reportLossOrder.reportLossOrderTypeList.productDisassembleInstall') }}</span>
                            <span v-if="scope.row.orderType == 2">{{
                                $t('reportLossOrder.reportLossOrderTypeList.sortingOrder') }}</span>
                                <span v-if="scope.row.orderType == 3">其他报损</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('reportLossOrder.label.sourceOrderCodeNew')" show-overflow-tooltip
                        min-width="150px" prop="lossOrderCode">
                        <template #default="scope">
                           {{ scope.row.sourceOrderCode || '-' }}
                        </template>
                    </el-table-column>

                    <el-table-column :label="'报损商品量'" prop="productTotalLossQty" show-overflow-tooltip
                        min-width="150px">
                        <template #default="scope">
                            <div>
                                <div><span class="text-left">报损量:</span><span>{{ scope.row.productTotalLossQty }}</span>
                                </div>
                                <div><span class="text-left">转换量:</span><span>{{ scope.row.productTotalLossWeight
                                        }}</span></div>
                            </div>

                        </template>
                   </el-table-column>
                    <!--  <el-table-column :label='"领用信息"' prop="receivingUserName" show-overflow-tooltip min-width="220px">
                        <template #default="scope">
                            <div>
                                <div><span class="text-left">领单人:</span><span>{{ scope.row.receivingUserName }}</span>
                                </div>
                                <div><span class="text-left">领单时间:</span><span>{{ parseDateTime(scope.row.receivingTime,
                                        "dateTime") }}</span></div>
                            </div>
                        </template>
                    </el-table-column> -->


                    <el-table-column :label="$t('reportLossOrder.label.reporterName')" prop="reporterName"
                        show-overflow-tooltip min-width="100px"></el-table-column>
                    <el-table-column :label="$t('reportLossOrder.label.reportTime')" prop="reportTime"
                        show-overflow-tooltip min-width="180px">
                        <template #default="scope">
                            <span v-if="scope.row.reportTime">{{ parseDateTime(scope.row.reportTime, "dateTime")
                                }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('reportLossOrder.label.createUserName')" prop="createUserName"
                        show-overflow-tooltip min-width="100px"></el-table-column>
                    <el-table-column :label="$t('reportLossOrder.label.createTime')" prop="createTime"
                        show-overflow-tooltip min-width="180px">
                        <template #default="scope">
                            <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column :label="$t('reportLossOrder.label.remark')" prop="remark" show-overflow-tooltip
                        min-width="150px"></el-table-column> -->
                    <el-table-column :label="$t('reportLossOrder.label.orderStatus')" prop="orderStatus" min-width="100"
                        show-overflow-tooltip>
                        <template #default="scope">
                            <div class="purchase">
                                <span class="purchase-status purchase-status-color0"
                                    v-if="scope.row.orderStatus == 0">{{ $t('reportLossOrder.statusList.draft')
                                    }}</span>
                                <span class="purchase-status purchase-status-color3"
                                    v-if="scope.row.orderStatus == 1">{{ $t('reportLossOrder.statusList.finish')
                                    }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="150" align="center">
                        <template #default="scope">
                            <!-- v-if="scope.row.orderStatus === 0 && scope.row.receivingStatus === 1 && scope.row.receivingUserId === userStore.user.userId" -->
                            <el-button v-hasPerm="['wms:insideWarehouseManagement:reportLossOrder:edit']"
                                v-if="scope.row.orderStatus === 0"
                                type="primary" link @click="editReportLossOrder(scope.row.id, 'edit')">
                                {{ $t('common.edit') }}
                            </el-button>
                            <el-button v-hasPerm="['wms:insideWarehouseManagement:reportLossOrder:detail']"
                                v-if="scope.row.orderStatus === 1" type="primary" link
                                @click="detailReportLossOrder(scope.row.id, 'detail')">
                                {{ $t('common.detailBtn') }}
                            </el-button>
                            <el-button v-hasPerm="['wms:insideWarehouseManagement:reportLossOrder:delete']"
                                v-if="scope.row.orderStatus === 0" type="danger" link
                                @click="handleDelete(scope.row.id)">
                                {{ $t('common.delete') }}
                            </el-button>
                          <!--   <el-button v-if="showReceiveOrderBtn(scope.row)"
                                v-hasPerm="['wms:insideWarehouseManagement:reportLossOrder:receive:order']" link
                                type="primary" @click="handleReceiveOrder(scope.row)">领单</el-button>
                            <el-button v-if="showCancelReceiveOrderBtn(scope.row)"
                                v-hasPerm="['wms:insideWarehouseManagement:reportLossOrder:cancel:order']" link
                                type="primary" @click="handleCancelReceiveOrder(scope.row)">取消领单</el-button> -->

                          <!--   <el-button type="primary" link
                                v-hasPerm="['wms:insideWarehouseManagement:reportLossOrder:detail']"
                                @click="detailReportLossOrder(scope.row.id, 'detail')">
                                {{ $t('common.detailBtn') }}
                            </el-button> -->
                        </template>
                    </el-table-column>
                </el-table>

                <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.page"
                    v-model:limit="queryParams.limit" @pagination="handleQuery" />
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">

defineOptions({
    name: "ReportLossOrder",
    inheritAttrs: false,
});

import { convertToTimestamp, parseDateTime, changeDateRange } from "@/core/utils/index.js";
import ReportLossOrderAPI, { ReportLossOrderPageVO, ReportLossOrderPageQuery } from "@/modules/wms/api/reportLossOrder";
import { useRouter } from "vue-router";
import moment from "moment";
import { emitter } from "@/core/utils/eventBus";
import { useTagsViewStore, useUserStore } from "@/core/store";
import { useNavigation } from "@/core/composables/useNavigation";
const { refreshAndNavigate } = useNavigation();

const userStore = useUserStore();
const router = useRouter();
const { t } = useI18n();
const queryFormRef = ref(null);
const loading = ref(false);
const total = ref(0);
const reportLossOrderTypeList = ref([
    {
        reportLossOrderTypeId: 1,
        reportLossOrderTypeName: t('reportLossOrder.reportLossOrderTypeList.productDisassembleInstall')
    },
    {
        reportLossOrderTypeId: 2,
        reportLossOrderTypeName: t('reportLossOrder.reportLossOrderTypeList.sortingOrder')
    },
    {
        reportLossOrderTypeId: 3,
        reportLossOrderTypeName: '其他报损'
    }
])
const statusList = ref([
    {
        statusId: 0,
        statusName: t('reportLossOrder.statusList.draft')
    },
    {
        statusId: 1,
        statusName: '完结' //t('reportLossOrder.statusList.finish')
    }
])
const dateTypeList = ref([
    {
        key: 1,
        value: t('reportLossOrder.dateTypeList.createDate')
    },
  /*   {
        key: 2,
        value: t('reportLossOrder.dateTypeList.approvalDate')
    } */
])
const queryParams = reactive<ReportLossOrderPageQuery>({
    dateType: 1,
    dateRange: [moment().subtract('days', 29).startOf("days").format('YYYY-MM-DD HH:mm:ss'), moment().endOf("days").format("YYYY-MM-DD HH:mm:ss")],
    page: 1,
    limit: 20,
    receivingStatus: '', // 是否领单状态筛选
});
const defaultTime: [Date, Date] = [
    new Date(2000, 1, 1, 0, 0, 0),
    new Date(2000, 2, 1, 23, 59, 59),
]
const reportLossOrderList = ref<ReportLossOrderPageVO[]>();

// 是否领单状态列表
const receivingStatusList = ref([
    {
        statusId: 0,
        statusName: '否'
    },
    {
        statusId: 1,
        statusName: '是'
    },
])
/** 时间转换 */
function handleChangeDateRange(val: any) {
    queryParams.dateRange = changeDateRange(val);
}
// 领单：领用状态=否且状态=草稿
function showReceiveOrderBtn(row: ReportLossOrderPageVO) {
    return row.receivingStatus == 0 && row.orderStatus == 0;
}
// 取消领单：领用状态=是且状态=草稿
function showCancelReceiveOrderBtn(row: ReportLossOrderPageVO) {
    return row.receivingStatus == 1 && row.orderStatus == 0;
}
/** 查询 */
function handleQuery() {
    if (queryParams.lossOrderCode && queryParams.lossOrderCode.length < 4) {
        return ElMessage.error(t("reportLossOrder.message.codeValideTips"));
    }
    loading.value = true;
    let params = {
        ...queryParams,
    }
    if (queryParams.dateType == 1 && queryParams.dateRange && queryParams.dateRange.length > 0) {
        params.createStartTime = convertToTimestamp(queryParams.dateRange[0])
        params.createEndTime = convertToTimestamp(queryParams.dateRange[1])
    }
    if (queryParams.dateType == 2 && queryParams.dateRange && queryParams.dateRange.length > 0) {
        /* params.reportStartTime = convertToTimestamp(queryParams.dateRange[0])
        params.reportEndTime = convertToTimestamp(queryParams.dateRange[1]) */
        params.receivingStartTime = convertToTimestamp(queryParams.dateRange[0])
        params.receivingEndTime = convertToTimestamp(queryParams.dateRange[1])
    }
    delete params.dateType
    delete params.dateRange
    ReportLossOrderAPI.getReportLossOrderPage(params)
        .then((data) => {
            reportLossOrderList.value = data.records;
            total.value = parseInt(data.total);
        })
        .finally(() => {
            loading.value = false;
        });
}

/** 重置查询 */
function handleResetQuery() {
    queryFormRef.value.resetFields();
    queryParams.dateType = 1
    queryParams.page = 1;
    queryParams.limit = 20;
    handleQuery();
}

/** 删除 */
function handleDelete(id?: string) {
    ElMessageBox.confirm(t('reportLossOrder.message.deleteTips'), t('common.tipTitle'), {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: "warning",
    }).then(
        () => {
            loading.value = true;
            let data = {
                id: id
            }
            ReportLossOrderAPI.deleteReportLossOrder(data)
                .then(() => {
                    ElMessage.success(t('reportLossOrder.message.deleteSucess'));
                    handleResetQuery();
                })
                .finally(() => (loading.value = false));
        },
        () => {
            ElMessage.info(t('reportLossOrder.message.deleteConcel'));
        }
    );
}

// 新增
function addReportLossOrder(id?: string, type?: string) {
    refreshAndNavigate({
        path: "/wms/insideWarehouseManagement/addReportLossOrder",
        query: { id: id, type: type, title: t('reportLossOrder.button.addReportLossOrder') }
    });
}

// 编辑
function editReportLossOrder(id: string, type: string) {
    refreshAndNavigate({
        path: "/wms/insideWarehouseManagement/editReportLossOrder",
        query: { id: id, type: type, title: t('reportLossOrder.button.editReportLossOrder') }
    });
}

// 详情
function detailReportLossOrder(id: string, type: string) {
    router.push({
        path: "/wms/insideWarehouseManagement/detailReportLossOrder",
        query: { id: id, type: type, title: t('common.detailBtn') }
    });
}

/** 领单 */
function handleReceiveOrder(row: ReportLossOrderPageVO) {
    loading.value = true;
    const data = {
        id: row.id
    };
    ReportLossOrderAPI.receiveReportLossOrder(data)
        .then(() => {
            ElMessage.success('领单成功');
            handleQuery();
        })
        .catch(() => {
            ElMessage.error('领单失败');
        })
        .finally(() => {
            loading.value = false;
        });
}

/** 取消领单 */
function handleCancelReceiveOrder(row: ReportLossOrderPageVO) {
    ElMessageBox.confirm('确定要取消领单吗？', t('common.tipTitle'), {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: "warning",
    }).then(
        () => {
            loading.value = true;
            const data = {
                id: row.id
            };
            ReportLossOrderAPI.cancelReceiveReportLossOrder(data)
                .then(() => {
                    ElMessage.success('取消领单成功');
                    handleQuery();
                })
                .catch(() => {
                    ElMessage.error('取消领单失败');
                })
                .finally(() => {
                    loading.value = false;
                });
        },
        () => {
            ElMessage.info('已取消操作');
        }
    );
}

onActivated(() => {
    handleQuery();
});

emitter.on("reloadListByWarehouseId", (e) => {
    nextTick(() => {
        handleQuery();
    });
});

</script>

<style lang="scss" scoped>
.reportLossOrder {}
</style>
