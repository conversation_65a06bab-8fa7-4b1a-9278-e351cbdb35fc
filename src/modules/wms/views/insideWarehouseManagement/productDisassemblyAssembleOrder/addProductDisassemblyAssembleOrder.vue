<template>
    <div class="app-container">
        <div class="addProductDisassemblyAssembleOrder"  v-loading="loading">
            <div >
                <div class="page-title">
                    <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                    <div>
                        <span v-if="type=='add'">{{ $t("productDisassemblyAssembleOrder.button.addProductDisassemblyAssembleOrder") }}</span>
                        <span v-else> {{t('productDisassemblyAssembleOrder.label.disassemblyOrderCode')}}：{{form.disassemblyOrderCode}}</span>
                    </div>
                </div>
            </div>
            <div class="page-content">
                <el-form
                        :model="form"
                        :rules="rules"
                        ref="fromRef"
                        label-width="120px"
                        label-position="right"
                >
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("productDisassemblyAssembleOrder.label.basicInformation") }}
                        </div>
                    </div>

                    <div>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.orderType')" prop="orderType">
                                    <el-select
                                            v-model="form.orderType"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            :disabled="type=='edit'"
                                            @change="changeOrderType"
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in orderTypeList" :key="item.orderTypeId" :label="item.orderTypeName" :value="item.orderTypeId"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.template')" prop="disassemblyTemplateId">
                                    <el-select
                                            v-model="form.disassemblyTemplateId"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            filterable
                                            @change="changeTemplate"
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in templateOptionList" :key="item.id" :label="item.disassemblyTemplateName" :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                          <el-col :span="8"  class="flex-center-start">
                            <el-form-item v-if="showCalcNumber" :label="getCalculateLabel" prop="calculateNum">
                              <template #label>
                                {{ getCalculateLabel }}
                                <el-tooltip
                                  placement="bottom"
                                  effect="light"
                                  :content="getCalculateTip"
                                  :raw-content="true"
                                >
                                  <el-icon class="mt8px ml4px" size="16">
                                    <QuestionFilled />
                                  </el-icon>
                                </el-tooltip>
                              </template>
                              <el-input-number
                                  v-model="form.calculateNum"
                                  :placeholder="$t('common.placeholder.inputTips')"
                                  :controls="false"
                                  class="!w-[256px]"
                              />
                              <el-button type="primary" @click="calculateHandle" :loading="submitLoading" class="ml20px">
                                {{ $t("productDisassemblyAssembleOrder.button.calculate") }}
                              </el-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="16">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.remark')" prop="remark">
                                    <el-input
                                            :rows="4"
                                            type="textarea"
                                            show-word-limit
                                            v-model="form.remark"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            maxlength="200"
                                            clearable
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line"></div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("productDisassemblyAssembleOrder.label.productDisassemblyAssembleOrderInformation") }}
                        </div>
                    </div>
                       <div class="mb-20">
                           <div class="flex-center-but">
                               <div class="trapezoid trapezoid-color1">
                                   {{$t('productDisassemblyAssembleOrder.label.sourceProduct')}}
                                   <span class="fw-400">（{{$t('productDisassemblyAssembleOrder.label.totalCountProduct')}}:{{sourceTotalNum}}</span>
                                   <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalWeight')}}:{{sourceTotalWeight}}</span>
                                   <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalRowCount')}}:{{ sourceSkuProductNum }}）</span>
                               </div>
                               <div class="button-add cursor-pointer" @click="addProduct(SourceDetailDataEnum.SOURCE)" >
                                   {{$t('productDisassemblyAssembleOrder.button.addProduct')}}
                               </div>
                           </div>
                           <el-table class="product-list-table" v-loading="loadSource" :data="form.sourceList" max-height="530"  highlight-current-row stripe>
                               <el-table-column type="index" :label="$t('common.sort')" width="60" />
                               <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productInformation')" width="150px">
                                   <template #default="scope">
                                     <div style="word-break: break-all"><span style="color: #90979E">{{scope.row.productCode}} | </span>{{scope.row.productName}}</div>
                                     <!--<div class="product-name">{{scope.row.productName}}</div>
                                     <div>
                                       <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productCode')}}：</span>
                                       <span class="product-value">{{scope.row.productCode || '-'}}</span>
                                     </div>-->
                                   </template>
                               </el-table-column>
                               <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productSpec')" prop="productSpec" show-overflow-tooltip width="80px"></el-table-column>
                               <!--出库库区-->
                               <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.sourceWarehouseArea')" width="130px">
                                 <template #header>
                                   <el-dropdown trigger="click" @command="handleFilterChange" placement="bottom-end" :disabled="!outWarehouseAreaList || outWarehouseAreaList.length ===0">
                                        <span class="dropdown-trigger">
                                          *{{ $t('productDisassemblyAssembleOrder.label.sourceWarehouseArea') }}
                                          <svg-icon style="height: 1em;width: 1em;" icon-class="iconBottom"></svg-icon>
                                        </span>
                                     <template #dropdown>
                                       <el-dropdown-menu>
                                         <el-dropdown-item
                                           v-for="item in outWarehouseAreaList"
                                           :key="item.id"
                                           :command="item.id"
                                           :class="{ 'is-selected': chooseWarehouse === item.id }">
                                           {{ item.areaName }}
                                         </el-dropdown-item>
                                       </el-dropdown-menu>
                                     </template>
                                   </el-dropdown>
                                 </template>
                                   <template #default="scope">
                                       <el-form-item label-width="0px" class="mt18px" :prop="'sourceList.'+scope.$index+'.sourceWarehouseAreaId'"
                                                     :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.sourceWarehouseAreaId'),trigger:['blur','change']}]">
                                           <el-select
                                               v-model="scope.row.sourceWarehouseAreaId"
                                               :placeholder="$t('common.placeholder.selectTips')"
                                               filterable
                                               @change="(val: string) => sourceWarehouseChange(val, scope.row)"
                                               clearable
                                           >
                                               <el-option v-for="item in scope.row.outWarehouseAreaList" :key="item.id" :label="item.areaName" :value="item.id"></el-option>
                                           </el-select>
                                       </el-form-item>
                                   </template>
                               </el-table-column>
                              <!--单价-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.price')" min-width="260px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" class="mt18px" :prop="'sourceList.'+scope.$index+'.unitPrice'" :rules="[
                                     {required:true,message:t('productDisassemblyAssembleOrder.rules.unitPrice'),trigger:['blur','change']},
                                      {pattern: unitPriceRegExp,message:t('productDisassemblyAssembleOrder.rules.unitPriceFormat'), trigger: ['blur','change']}
                                     ]">
                                   <el-input
                                       v-model="scope.row.unitPrice"
                                       disabled
                                       @change="(val: number) => unitPriceChange(val, scope.row, SourceDetailDataEnum.SOURCE)"
                                       :placeholder="$t('common.placeholder.inputTips')"
                                       clearable
                                   >
                                     <template #append>元/{{ scope.row?.pricingScheme == 0 ? scope.row?.productUnitName : scope.row?.conversionRelSecondUnitName }}</template>
                                   </el-input>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--出库量-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.outWarehouseNum')" min-width="260px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" class="mt18px" :prop="'sourceList.'+scope.$index+ '.disassemblyOutQty'"
                                               :rules="[
                                                   {required:true,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQty'),trigger:['blur','change']},
                                                    {pattern: patternRegExp,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQtyFormat'), trigger: ['blur','change']}
                                                   ]">
                                   <div>
                                     <el-input
                                         v-model="scope.row.disassemblyOutQty"
                                         @change="(val: number) => firstLevelQtyChange(val, scope.row, 'disassemblyOutWeight', SourceDetailDataEnum.SOURCE)"
                                         :placeholder="$t('common.placeholder.inputTips')"
                                         clearable
                                         :precision="3" :min="0"
                                     >
                                       <template #append>{{ scope.row?.productUnitName}}</template>
                                     </el-input>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--一级单位增减-->
                             <el-table-column :label="$t('productDisassemblyAssembleOrder.label.allUnitIncreaseAndDecrease')" prop="isDiscreteUnit" show-overflow-tooltip min-width="120px">
                               <template #default="scope">
                                 {{scope.row.isDiscreteUnit === 1 ? $t('productDisassemblyAssembleOrder.whetherOption.yes') : $t('productDisassemblyAssembleOrder.whetherOption.no')}}
                               </template>
                             </el-table-column>
                             <!--出库转换量-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.outWarehouseTransferNum')" prop="isDiscreteUnit" show-overflow-tooltip min-width="260px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" class="mt18px" :prop="'sourceList.'+scope.$index+ '.disassemblyOutWeight'"
                                               :rules="[
                                                   {required:true,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQty'),trigger:['blur','change']},
                                                   {pattern: patternRegExp ,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutWeightFormat'), trigger: ['blur','change']}
                                                   ]">
                                   <div>
                                     <el-input
                                         v-model="scope.row.disassemblyOutWeight"
                                         @change="(val: number) => secondLevelQtyChange(val, scope.row, 'disassemblyOutQty', SourceDetailDataEnum.SOURCE)"
                                         :placeholder="$t('common.placeholder.inputTips')"
                                         :precision="3" :min="0"
                                         clearable
                                     >
                                       <template #append>{{ scope.row?.conversionRelSecondUnitName}}</template>
                                     </el-input>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--金额-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.amount')" prop="amount" show-overflow-tooltip min-width="260px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" class="mt18px" :prop="'sourceList.'+scope.$index+ '.amount'"
                                               :rules="[
                                                   {required:true,message:t('productDisassemblyAssembleOrder.rules.amount'),trigger:['blur','change']},
                                                    {pattern: amountRegExp,message:t('productDisassemblyAssembleOrder.rules.amountFormat'), trigger: ['blur','change']}
                                                   ]">
                                   <div>
                                     <el-input
                                         v-model="scope.row.amount"
                                         disabled
                                         @change="(val: number) => amountChange(val, scope.row, SourceDetailDataEnum.SOURCE)"
                                         :placeholder="$t('common.placeholder.inputTips')"
                                         clearable
                                     ></el-input>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--包装-->
                             <el-table-column :label="$t('productDisassemblyAssembleOrder.label.packaging')" prop="productPackaging" show-overflow-tooltip min-width="160px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" class="mt18px" :prop="'sourceList.'+scope.$index+ '.productPackaging'"
                                               :rules="[{required:false,message:t('productDisassemblyAssembleOrder.rules.productQty'),trigger:['blur','change']}]">
                                   <div>
                                     <el-input
                                         v-model="scope.row.productPackaging"
                                         :placeholder="$t('common.placeholder.inputTips')"
                                         maxlength="30"
                                         clearable
                                     >
                                     </el-input>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--可用库存-->
                             <el-table-column :label="$t('productDisassemblyAssembleOrder.label.availableWarehouse')" width="210px">
                               <template #default="scope">
                                 <!--<div>
                                   <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.availableWarehouseNum')}}：</span>
                                   <span class="product-value">{{scope.row.availableStockQty || '-'}}</span>
                                 </div>
                                 <div>
                                   <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.availableWarehouseTransferNum')}}：</span>
                                   <span class="product-value">{{scope.row.availableStockWeight || '-'}}</span>
                                 </div>-->
                                 <div style="word-break: break-all">{{scope.row.availableStockDisplay}}</div>
                               </template>
                             </el-table-column>
                               <el-table-column fixed="right" :label="$t('common.handle')" width="80">
                                 <template #default="scope">
                                   <el-button type="primary" link size="small" @click="handleAddRow(scope.$index, scope.row, SourceDetailDataEnum.SOURCE)"><el-icon><Plus /></el-icon></el-button>
                                   <el-button type="danger" link size="small" @click="handleDeleteRow(scope.$index, SourceDetailDataEnum.SOURCE)"><el-icon><Minus /></el-icon></el-button>
                                 </template>
                               </el-table-column>
                           </el-table>
                       </div>
                        <div class="mb-20">
                            <div class="flex-center-but">
                                <div class="trapezoid trapezoid-color2">
                                    {{$t('productDisassemblyAssembleOrder.label.targetProduct')}}
                                    <span class="fw-400">（{{$t('productDisassemblyAssembleOrder.label.totalCountProduct')}}:{{targetTotalNum}}</span>
                                    <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalWeight')}}:{{targetTotalWeight}}</span>
                                    <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalRowCount')}}:{{ targetSkuProductNum }}）</span>
                                </div>
                                <div class="button-add cursor-pointer" @click="addProduct(SourceDetailDataEnum.TARGET)">
                                    {{$t('productDisassemblyAssembleOrder.button.addProduct')}}
                                </div>
                            </div>
                            <el-table class="product-list-table" :data="form.targetList" max-height="530" highlight-current-row stripe>
                                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                                <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productInformation')" width="150px">
                                    <template #default="scope">
                                      <div style="word-break: break-all"><span style="color: #90979E">{{scope.row.productCode}} | </span>{{scope.row.productName}}</div>
                                      <!--<div class="product-name">{{scope.row.productName}}</div>
                                      <div>
                                        <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productCode')}}：</span>
                                        <span class="product-value">{{scope.row.productCode || '-'}}</span>
                                      </div>-->
                                    </template>
                                </el-table-column>
                              <!--规格-->
                              <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productSpec')" prop="productSpec" show-overflow-tooltip width="80px"></el-table-column>
                              <!--入库库区-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.inSourceWarehouseArea')" width="130px">
                                <template #header>
                                  <el-dropdown trigger="click" @command="handleFilterChangeIn" placement="bottom-end" :disabled="!outWarehouseAreaEnableList || outWarehouseAreaEnableList.length ===0 || !form.targetList || form.targetList.length === 0">
                                        <span class="dropdown-trigger">
                                          *{{ $t('productDisassemblyAssembleOrder.label.inSourceWarehouseArea') }}
                                          <svg-icon style="height: 1em;width: 1em;" icon-class="iconBottom"></svg-icon>
                                        </span>
                                    <template #dropdown>
                                      <el-dropdown-menu>
                                        <el-dropdown-item
                                          v-for="item in outWarehouseAreaEnableList"
                                          :key="item.id"
                                          :command="item.id"
                                          :class="{ 'is-selected': chooseInWarehouse === item.id }">
                                          {{ item.areaName }}
                                        </el-dropdown-item>
                                      </el-dropdown-menu>
                                    </template>
                                  </el-dropdown>
                                </template>
                                <template #default="scope">
                                  <el-form-item label-width="0px" class="mt18px" :prop="'targetList.'+scope.$index+'.targetWarehouseAreaId'" :rules="[
                                      {required:true,message:t('productDisassemblyAssembleOrder.rules.targetWarehouseAreaId'),trigger:['blur','change']}
                                      ]">
                                    <el-select
                                        v-model="scope.row.targetWarehouseAreaId"
                                        :placeholder="$t('common.placeholder.selectTips')"
                                        filterable
                                        clearable
                                    >
                                      <el-option v-for="item in outWarehouseAreaEnableList" :key="item.id" :label="item.warehouseArea" :value="item.id"></el-option>
                                    </el-select>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--单价-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.price')" min-width="260px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" class="mt18px" :prop="'targetList.'+scope.$index+'.unitPrice'" :rules="[
                                      {required:true,message:t('productDisassemblyAssembleOrder.rules.unitPrice'),trigger:['blur','change']},
                                       {pattern: unitPriceRegExp,message:t('productDisassemblyAssembleOrder.rules.unitPriceFormat'), trigger: ['blur','change']}
                                      ]">
                                    <el-input
                                        v-model="scope.row.unitPrice"
                                        @change="(val: number) => unitPriceChange(val, scope.row, SourceDetailDataEnum.TARGET)"
                                        :placeholder="$t('common.placeholder.inputTips')"
                                        clearable
                                    >
                                      <template #append>元/{{ scope.row?.pricingScheme == 0 ? scope.row?.productUnitName : scope.row?.conversionRelSecondUnitName }}</template>
                                    </el-input>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--入库量-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.inWarehouseNum')" min-width="260px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" class="mt18px" :prop="'targetList.'+scope.$index+ '.disassemblyInQty'"
                                                :rules="[
                                                    {required:true,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQty'),trigger:['blur','change']},
                                                     {pattern: patternRegExp,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQtyFormat'), trigger: ['blur','change']}
                                                    ]">
                                    <div>
                                      <el-input
                                          v-model="scope.row.disassemblyInQty"
                                          @change="(val: number) => firstLevelQtyChange(val, scope.row, 'disassemblyInWeight', SourceDetailDataEnum.TARGET)"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          :precision="3" :min="0"
                                          clearable
                                      >
                                        <template #append>{{ scope.row?.productUnitName}}</template>
                                      </el-input>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--一级单位增减-->
                              <el-table-column :label="$t('productDisassemblyAssembleOrder.label.allUnitIncreaseAndDecrease')" prop="isDiscreteUnit" show-overflow-tooltip min-width="120px">
                                <template #default="scope">
                                  {{scope.row.isDiscreteUnit === 1 ? $t('productDisassemblyAssembleOrder.whetherOption.yes') : $t('productDisassemblyAssembleOrder.whetherOption.no')}}
                                </template>
                              </el-table-column>
                              <!--入库转换量-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.inWarehouseTransferNum')" prop="isDiscreteUnit" show-overflow-tooltip min-width="260px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" class="mt18px" :prop="'targetList.'+scope.$index+ '.disassemblyInWeight'"
                                                :rules="[
                                                    {required:true,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQty'),trigger:['blur','change']},
                                                     {pattern: patternRegExp ,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutWeightFormat'), trigger: ['blur','change']}
                                                    ]">
                                    <div>
                                      <el-input
                                          v-model="scope.row.disassemblyInWeight"
                                          @change="(val: number) => secondLevelQtyChange(val, scope.row, 'disassemblyInQty', SourceDetailDataEnum.TARGET)"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          :precision="3" :min="0"
                                          clearable
                                      >
                                        <template #append>{{ scope.row?.conversionRelSecondUnitName}}</template>
                                      </el-input>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--金额-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.amount')" prop="amount" show-overflow-tooltip min-width="260px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" class="mt18px" :prop="'targetList.'+scope.$index+ '.amount'"
                                                :rules="[
                                                    {required:true,message:t('productDisassemblyAssembleOrder.rules.amount'),trigger:['blur','change']},
                                                    {pattern: amountRegExp,message:t('productDisassemblyAssembleOrder.rules.amountFormat'), trigger: ['blur','change']}
                                                    ]">
                                    <div>
                                      <el-input
                                          v-model="scope.row.amount"
                                          @change="(val: number) => amountChange(val, scope.row, SourceDetailDataEnum.SOURCE)"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          clearable
                                      ></el-input>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--包装-->
                              <el-table-column :label="$t('productDisassemblyAssembleOrder.label.packaging')" prop="isDiscreteUnit" show-overflow-tooltip min-width="160px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" class="mt18px" :prop="'targetList.'+scope.$index+ '.productPackaging'"
                                                :rules="[{required:false,message:t('productDisassemblyAssembleOrder.rules.productQty'),trigger:['blur','change']}]">
                                    <div>
                                      <el-input
                                          v-model="scope.row.productPackaging"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          maxlength="30"
                                          clearable
                                      >
                                      </el-input>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                                <el-table-column fixed="right" :label="$t('common.handle')" width="80">
                                  <template #default="scope">
                                    <el-button type="primary" link size="small" @click="handleAddRow(scope.$index, scope.row, SourceDetailDataEnum.TARGET)"><el-icon><Plus /></el-icon></el-button>
                                    <el-button type="danger" link size="small" @click="handleDeleteRow(scope.$index, SourceDetailDataEnum.TARGET)"><el-icon><Minus /></el-icon></el-button>
                                  </template>
                                </el-table-column>
                            </el-table>
                        </div>
                </el-form>
            </div>
            <div class="page-footer">
                <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
                <el-button type="primary" @click="handleSubmit()" :loading="submitLoading">{{ $t("productDisassemblyAssembleOrder.button.confirm") }}</el-button>
            </div>
            <AddProduct
                    ref="addProductRef"
                    v-model:visible="dialog.visible"
                    :title="dialog.title"
                    :outWarehouseAreaShow="outWarehouseAreaShow"
                    :availableStockQtyShow="availableStockQtyShow"
                    :outWarehouseAreaFromMultipShow="outWarehouseAreaFromMultipShow"
                    @onSubmit="onSubmit"
            />
        </div>
    </div>
</template>

<script setup lang="ts">

    import {convertUnitParamsInterface} from "@/modules/wms/api/disassemblyAssembleTemplate";

    defineOptions({
        name: "WmsAddProductDisassemblyAssembleOrder",
        inheritAttrs: false,
    });

    import AddProduct from "../../../components/addProduct.vue";
    import { parseDateTime,isEmpty } from "@/core/utils/index.js";
    import {useRoute,useRouter} from "vue-router";
    import ProductDisassemblyAssembleOrderAPI,{ProductDisassemblyAssembleOrderFrom} from "@/modules/wms/api/productDisassemblyAssembleOrder";
    import CommonAPI, { ProductAllPageQuery,ProductAllPageVO}  from "@/modules/wms/api/common";
    import {Minus, Plus} from "@element-plus/icons-vue";


    const route = useRoute();
    const router = useRouter();
    const { t } = useI18n();
    const outWarehouseAreaList = ref([])
    const outWarehouseAreaEnableList = ref([])

    const fromRef = ref()
    const submitLoading = ref(false)
    const queryFormRef = ref(ElForm);
    const loadSource = ref(false);
    const loading = ref(false);
    const id = route.query.id;
    const type = route.query.type;
    const addProductRef = ref();
    const outWarehouseAreaShow = ref(false);
    const availableStockQtyShow = ref(false);
    const outWarehouseAreaFromMultipShow = ref(false);
    const orderTypeList = ref([
        {
            orderTypeId: 1,
            orderTypeName: t('productDisassemblyAssembleOrder.orderTypeList.productPortfolio')
        },
        {
            orderTypeId: 2,
            orderTypeName: t('productDisassemblyAssembleOrder.orderTypeList.productSplit')
        },
    ])

    const enum SourceDetailDataEnum {
      SOURCE = 1,
      TARGET = 2,
    }
    const enum ConvertUnitTypeEnum {
      /** 一级转二级 */
      FIRST_TO_SECOND = "FIRST_TO_SECOND",
      /** 二级转一级 */
      SECOND_TO_FIRST = "SECOND_TO_FIRST",
    }

    const dialog = reactive({
        title: "",
        visible: false,
    });
    const form = reactive<ProductDisassemblyAssembleOrderFrom>({
        orderType : undefined,
        sourceList:[],
        targetList:[],
    });

    const rules = reactive({
        orderType: [{ required: true, message: t("productDisassemblyAssembleOrder.rules.orderType"), trigger: ["blur","change"] }],
        calculateNum: [
          {
            pattern: /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
            message: t('productDisassemblyAssembleOrder.rules.calcNumFormat'),
            trigger: ["blur","change"]
          }
        ]
    });

    //计算源 总量: 出库量求和
    const sourceTotalNum = computed(() => {
        let calc = form.sourceList?.reduce((total, item) => {
            return Number(total) + Number(item.disassemblyOutQty || 0);
        }, 0)
        return calc?.toFixed(3)
    })

    //计算源 总重量 出库转换量求和
    const sourceTotalWeight = computed(() => {
        let calc = form.sourceList?.reduce((total, item) => {
            return Number(total) +  Number(item.disassemblyOutWeight || 0);
        }, 0).toFixed(3)
        return calc
    })

    //计算源 商品个数 SKU的商品个数
    const sourceSkuProductNum = computed(() => {
      let sourceNum = new Set(form.sourceList?.filter(item => item.isSku).map(item => item.productCode))
      return sourceNum?.size || '0'
    })

    //计算目标 总量: 入库量求和
    const targetTotalNum = computed(() => {
        let calc = form.targetList?.reduce((total, item) => {
            return Number(total) + Number(item.disassemblyInQty || 0);
        }, 0)
        return calc?.toFixed(3)
    })

    //计算目标 总重量:入库转换量求和
    const targetTotalWeight = computed(() => {
        let calc = form.targetList?.reduce((total, item) => {
            return Number(total) + Number(item.disassemblyInWeight || 0);
        }, 0).toFixed(3)
        return calc
    })

    //计算目标 商品个数 SKU的商品个数
    const targetSkuProductNum = computed(() => {
      let targetNum = new Set(form.targetList?.filter(item => item.isSku).map(item => item.productCode))
      return targetNum?.size || '0'
    })

    /** 查询出库库区列表（全部） */
    function getOutWarehouseAreaList() {
        CommonAPI.getOutWarehouseAreaList({status:1})
            .then((data) => {
              outWarehouseAreaList.value = data?.map(item => {
                return {
                  ...item,
                  // warehouseArea: item.areaName + '|' + item.areaCode
                  warehouseArea: item.areaName
                }
              }) || []
            })
    }

    /** 查询出库库区列表(启用) */
    function getOutWarehouseAreaEnableList(params) {
        return new Promise((resolve, reject) => {
            CommonAPI.getOutWarehouseAreaList(params)
                .then((data) => {
                    outWarehouseAreaEnableList.value = data;
                    if(outWarehouseAreaEnableList.value && outWarehouseAreaEnableList.value.length>0){
                        outWarehouseAreaEnableList.value.map((item)=>{
                            // item.warehouseArea =item.areaName + '|' + item.areaCode
                            item.warehouseArea =item.areaName
                            return item
                        });
                    }
                    resolve();
                })
                .catch((error) => {
                    loading.value = false;
                    reject(error);
                })
        });
    }

    function changeOrderType () {
      if(form.orderType && form.disassemblyTemplateId){
        form.calculateNum = undefined;
        chooseWarehouse.value = ''
        chooseInWarehouse.value = ''
        queryDisassemblyProductDetailList()
      }else{
        clearEmptyData()
      }
    }
    function changeTemplate() {
      if(form.orderType && form.disassemblyTemplateId){
        form.calculateNum = undefined;
        chooseWarehouse.value = ''
        chooseInWarehouse.value = ''
        queryDisassemblyProductDetailList()
      }else{
        clearEmptyData()
      }
    }
    function clearEmptyData() {
      templateProductSourceList.value = [];
      templateProductTargetList.value = [];
      baseRateNumber.value = null;
      form.calculateNum = undefined;
      chooseWarehouse.value = ''
      chooseInWarehouse.value = ''
      form.sourceList = [];
      form.targetList = [];
    }
    const templateProductSourceList = ref([]);
    const templateProductTargetList = ref([]);
    const baseRateNumber = ref(null);

    function queryDisassemblyProductDetailList() {
      const params = {
        orderType: form.orderType,
        disassemblyTemplateId: form.disassemblyTemplateId
      }
      ProductDisassemblyAssembleOrderAPI.queryDisassemblyProductDetailList(params).then((data) => {
        if(form.orderType == 2){//分拆源数量
          form.calculateNum = Number(data?.disassemblyTargetQty);
          baseRateNumber.value = Number(data?.disassemblyTargetQty);
        }
        form.sourceList = []
        const promises = data?.sourceList.map((product,index) => {
          return new Promise((resolve) => {
            let params = {
              productCode:product.productCode,
              isDiscreteUnit:product.isDiscreteUnit,
            }
            CommonAPI.queryWarehouseAreaProductStockQtyList(params).then((res)=>{
              product.outWarehouseAreaList = res.areaList ? res.areaList : []
              product.availableStockDisplay = res.availableStockDisplay && res.availableStockDisplay.length > 0 ? res.availableStockDisplay.join('，') : '总库存：无库存'
              resolve(product);
            }).catch(()=>{
            }).finally(()=>{
            })
          });
        });
        Promise.all(promises).then((results) => {
          results.forEach((a) => {
            form.sourceList.push(a);
          });
          queryAllHasStockListByReq()
        });
        /*form.sourceList = data?.sourceList.map((item: any) => {
          return { ...item }
        }) || []*/
        templateProductSourceList.value = data?.sourceList || []
        form.targetList = data?.targetList.map((item: any) => {
          return { ...item }
        }) || []
        templateProductTargetList.value = data?.targetList || []
      }).catch(() => {
        clearEmptyData()
      })
    }

    function queryAllHasStockListByReq() {
      if(form.sourceList && form.sourceList.length > 0){
        let productQueryDTOList = []
        form.sourceList.forEach(list=>{
          let obj = {
            productCode:list.productCode,
            isDiscreteUnit:list.isDiscreteUnit
          }
          productQueryDTOList.push(obj)
        })
        let params = {
          productQueryDTOList:productQueryDTOList
        }
        CommonAPI.queryAllHasStockListByReq(params).then((res)=>{
          outWarehouseAreaList.value = res ? res : []
        }).catch(()=>{
          outWarehouseAreaList.value = []
        })
      }
    }

    const chooseWarehouse = ref('')
    function handleFilterChange(val) {
      console.log("val====",val)
      if(val){
        chooseWarehouse.value = val
        form.sourceList.forEach(list=>{
          let warehouseList = []
          if(list.outWarehouseAreaList && list.outWarehouseAreaList.length > 0){
            list.outWarehouseAreaList.forEach(areaList=>{
              warehouseList.push(areaList.id)
            })
          }
          if(!list.sourceWarehouseAreaId && warehouseList.length > 0 && warehouseList.includes(chooseWarehouse.value)){
            list.sourceWarehouseAreaId = chooseWarehouse.value
            sourceWarehouseChange(val,list)
          }
        })
      }
    }
    const chooseInWarehouse = ref('')
    function handleFilterChangeIn(val) {
      if(val){
        chooseInWarehouse.value = val
        form.targetList.forEach(list=>{
          if(!list.targetWarehouseAreaId){
            list.targetWarehouseAreaId = chooseInWarehouse.value
          }
        })
      }
    }

    const showCalcNumber = computed(() => {
      return form.orderType && form.disassemblyTemplateId
    })

    const getCalculateLabel = computed(() => {
      if(form.orderType == 1){
        return t('productDisassemblyAssembleOrder.label.groupNum')
      }else if(form.orderType == 2){
        return t('productDisassemblyAssembleOrder.label.splitNum')
      }
    })
    const getCalculateTip = computed(() => {
      if(form.orderType == 1){
        return t('productDisassemblyAssembleOrder.message.groupNumTip')
      }else if(form.orderType == 2){
        return t('productDisassemblyAssembleOrder.message.splitNumTip')
      }
    })

    function unitPriceChange(val, row, sourceType) {
      //此处修改单价时，同一个商品的单价自动更改，并遍历查对应的商品金额
      if(sourceType == SourceDetailDataEnum.SOURCE){
        form.sourceList?.forEach(item => {
          if(item.productCode == row.productCode){
            item.unitPrice = val;
            calcAmountFetch(item, sourceType);
          }
        })
      }else if(sourceType == SourceDetailDataEnum.TARGET){
        form.targetList?.forEach(item => {
          if(item.productCode == row.productCode){
            item.unitPrice = val;
            calcAmountFetch(item, sourceType);
          }
        })
      }
    }

    function amountChange(val, record, sourceType){}

    const apiLoading = ref(false)
    const patternRegExp = /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/;
    /*单价校验*/
    const unitPriceRegExp = /^(-?0(?:\.\d{1,4})?|[1-9]\d{0,6}(?:\.\d{1,4})?)$/;
    /*金额校验*/
    const amountRegExp =  /^(-?0(?:\.\d{1,2})?|-?[1-9]\d{0,8}(?:\.\d{1,2})?)$/;
    const firstLevelQtyChange = (val: number, record : Record<string, any>, convertKey: string, sourceType: SourceDetailDataEnum) => {
      /*一级单位增减为是 支持一级单位转二级单位*/
      if(patternRegExp.test(val) && ((record.isDiscreteUnit == 1) || (record.isDiscreteUnit == 0 && isEmpty(record[convertKey])))){
        apiLoading.value = true;
        const params: convertUnitParamsInterface = {
          productCode: record.productCode,
          originalValue: Number(val),
          convertUnitTypeEnum: ConvertUnitTypeEnum.FIRST_TO_SECOND,
        }
        CommonAPI.convertProductUnit(params)
            .then((data) => {
              record[convertKey] = data?.convertedValue;
              calcAmountFetch(record, sourceType);
            }).finally(() => {
              apiLoading.value = false;
        });
      }else{
        calcAmountFetch(record, sourceType);
      }
    }
    const secondLevelQtyChange = (val, record, convertKey,sourceType: SourceDetailDataEnum) => {
      /*一级单位增减为否 支持二级单位转一级单位*/
      if(patternRegExp.test(val) && record.isDiscreteUnit == 0 && isEmpty(record[convertKey])){
        apiLoading.value = true;
        const params: convertUnitParamsInterface = {
          productCode: record.productCode,
          originalValue: Number(val),
          convertUnitTypeEnum: ConvertUnitTypeEnum.SECOND_TO_FIRST,
        }
        CommonAPI.convertProductUnit(params)
            .then((data) => {
              record[convertKey] = data?.convertedValue;
              calcAmountFetch(record, sourceType);
            }).finally(() => {
          apiLoading.value = false;
        });
      }else {
        calcAmountFetch(record, sourceType);
      }
    }

    function calcAmountFetch(row, sourceType){
      if(isEmpty(row?.unitPrice)) return false
      let params = {
        productCode: row?.productCode,
        unitPrice: row?.unitPrice,
      }
      if(sourceType == SourceDetailDataEnum.SOURCE){
        if(isEmpty(row?.disassemblyOutWeight) || isEmpty(row?.disassemblyOutQty)) return false
        params.convertedQty = row.disassemblyOutWeight
        params.qty = row.disassemblyOutQty
      }
      if(sourceType == SourceDetailDataEnum.TARGET){
        if(isEmpty(row?.disassemblyInWeight) || isEmpty(row?.disassemblyInQty)) return false
        params.convertedQty = row.disassemblyInWeight
        params.qty = row.disassemblyInQty
      }
      calculateAmount(row,params)
    }

    // 计算入库金额
    const calculateAmount = (row: Record<string, any>, params: any) => {
      CommonAPI.calculateAmount(params).then((res) => {
        row.amount = res?.amount;
      });
    };

    function sourceWarehouseChange(val, record) {
      const params = {
        productCode: record.productCode,
        warehouseAreaId: record?.sourceWarehouseAreaId
      }
      ProductDisassemblyAssembleOrderAPI.queryProductWarehouseInfo( params)
          .then((data) => {
            record.availableStockQty = data?.availableStockQty
            record.availableStockWeight = data?.availableStockWeight
          })
    }


    /*计算*/
    function calculateHandle () {
      fromRef.value?.validateField('calculateNum',(resolve) => {
        if (resolve && form.calculateNum) {
          batchCalculateHandle()
        }
      })
    }

    /*批量计算出库量，入库量，出库转换量， 入库转换量 */
    function batchCalculateHandle () {
      if(isEmpty(form.calculateNum)){ return false}
      templateProductSourceList?.value?.forEach(templateItem => {
        let sourceIndex = form.sourceList?.findIndex(item => item.productCode === templateItem.productCode);
        if(sourceIndex !== -1){
          form.sourceList[sourceIndex].isFirstProduct = true;
        }
      })
      templateProductTargetList.value?.forEach(templateItem => {
        let sourceIndex = form.targetList?.findIndex(item => item.productCode === templateItem.productCode);
        if(sourceIndex !== -1){
          form.targetList[sourceIndex].isFirstProduct = true;
        }
      })

      form.sourceList?.forEach((item) => {
        /*只计算在模板中的商品,*/
        if(item?.isFirstProduct && templateProductSourceList?.value?.some(templateItem => templateItem.productCode === item.productCode)){
          const sourceBasePrdData = templateProductSourceList?.value?.find(templateItem => templateItem.productCode === item.productCode)
          if(form?.orderType == 1 && form?.disassemblyTemplateId){//组合直接相乘
            if(!isEmpty(sourceBasePrdData?.disassemblyOutQty)){
              item.disassemblyOutQty = (Number(sourceBasePrdData.disassemblyOutQty) * form.calculateNum).toFixed(3)
            }
            if(!isEmpty(sourceBasePrdData?.disassemblyOutWeight)){
              item.disassemblyOutWeight = (Number(sourceBasePrdData.disassemblyOutWeight) * form.calculateNum).toFixed(3)
            }
            if(!isEmpty(sourceBasePrdData?.amount)){
              item.amount = (Number(sourceBasePrdData.amount) * form.calculateNum).toFixed(2)
            }
          }
          if(form?.orderType == 2 && form?.disassemblyTemplateId && baseRateNumber.value){//拆分 需要 除以最小倍数 后再乘以分拆源数量
            if(!isEmpty(sourceBasePrdData?.disassemblyOutQty)){
              item.disassemblyOutQty = ((Number(sourceBasePrdData.disassemblyOutQty) / baseRateNumber.value) * form.calculateNum).toFixed(3)
            }
            if(!isEmpty(sourceBasePrdData?.disassemblyOutWeight)){
              item.disassemblyOutWeight = ((Number(sourceBasePrdData.disassemblyOutWeight) / baseRateNumber.value) * form.calculateNum).toFixed(3)
            }
            if(!isEmpty(sourceBasePrdData?.amount)){
              item.amount = ((Number(sourceBasePrdData.amount) / baseRateNumber.value) * form.calculateNum).toFixed(2)
            }
          }

          /*标识置为false 仅修改相同商品的第一个*/
          item.isFirstProduct = false
        }
      })
      form.targetList?.forEach(item => {
        /*只计算在模板中的商品,*/
        if(item?.isFirstProduct && templateProductTargetList?.value?.some(templateItem => templateItem.productCode === item.productCode)){
          const targetBasePrdData = templateProductTargetList?.value?.find(templateItem => templateItem.productCode === item.productCode)
          if(form?.orderType == 1 && form?.disassemblyTemplateId) {//组合直接相乘
            if(!isEmpty(targetBasePrdData?.disassemblyInQty)){
              item.disassemblyInQty = (Number(targetBasePrdData.disassemblyInQty) * form.calculateNum).toFixed(3)
            }
            if(!isEmpty(targetBasePrdData?.disassemblyInWeight)){
              item.disassemblyInWeight = (Number(targetBasePrdData.disassemblyInWeight) * form.calculateNum).toFixed(3)
            }
            if(!isEmpty(targetBasePrdData?.amount)){
              item.amount = (Number(targetBasePrdData.amount) * form.calculateNum).toFixed(2)
            }
          }
          if(form?.orderType == 2 && form?.disassemblyTemplateId && baseRateNumber.value) {//拆分 需要 除以最小倍数 后再乘以分拆源数量
            if(!isEmpty(targetBasePrdData?.disassemblyInQty)){
              item.disassemblyInQty = ((Number(targetBasePrdData.disassemblyInQty) / baseRateNumber.value) * form.calculateNum).toFixed(3)
            }
            if(!isEmpty(targetBasePrdData?.disassemblyInWeight)){
              item.disassemblyInWeight = ((Number(targetBasePrdData.disassemblyInWeight) / baseRateNumber.value) * form.calculateNum).toFixed(3)
            }
            if(!isEmpty(targetBasePrdData?.amount)){
              item.amount = ((Number(item.amount) / baseRateNumber.value) * form.calculateNum).toFixed(2)
            }
          }

          /*标识置为false 仅修改相同商品的第一个*/
          item.isFirstProduct = false
        }
      })
    }

    const templateOptionList = ref([])
    function queryTemplateList() {
      ProductDisassemblyAssembleOrderAPI.queryTemplateList().then(data => {
        templateOptionList.value = data?.map(item => {
          return { ...item }
        });
      })
    }

    function onSubmit(data) {
        if(data){
            if(data.typeList==1){
                if(data?.collection?.length>0){
                    data.collection.forEach(item=>{
                      item.sourceWarehouseAreaId = item?.warehouseAreaId
                      item.unitPrice = item?.weightedAveragePrice
                      item.isSku = true // 已与产品确认 手动选的原商品默认都是sku 非农资
                    })
                    const promises = data.collection.map((product,index) => {
                      return new Promise((resolve) => {
                        const existingIndex = form.sourceList?.findIndex(
                          (item: any) => (item.productCode === product.productCode && item.sourceWarehouseAreaId == product.sourceWarehouseAreaId)
                        );

                        if (existingIndex !== undefined && existingIndex >= 0) {
                          // 如果商品+库区已存在，不重复添加
                        } else {
                          let params = {
                            productCode:product.productCode,
                            isDiscreteUnit:product.isDiscreteUnit,
                          }
                          CommonAPI.queryWarehouseAreaProductStockQtyList(params).then((res)=>{
                            product.outWarehouseAreaList = res.areaList ? res.areaList : []
                            product.availableStockDisplay = res.availableStockDisplay && res.availableStockDisplay.length > 0 ? res.availableStockDisplay.join('，') : '总库存：无库存'
                            resolve(product);
                          }).catch(()=>{
                          }).finally(()=>{
                          })
                        }
                      });
                    });
                    Promise.all(promises).then((results) => {
                      results.forEach((a) => {
                        form.sourceList.push(a);
                      });
                      queryAllHasStockListByReq()
                    });

                }
                // form.sourceList = form.sourceList?.concat(data?.collection);
            }else{
                if(data?.collection?.length>0){
                  data.collection.forEach(item=>{
                    item.unitPrice = item?.weightedAveragePrice
                  })
                }
                form.targetList= form.targetList?.concat(data?.collection);
            }
        }
    }

    const initProductData = {
      sourceWarehouseAreaId: null,
      targetWarehouseAreaId: null,
      disassemblyInQty: null,
      disassemblyOutQty: null,
      disassemblyInWeight: null,
      disassemblyOutWeight: null,
      amount: null,
      productPackaging: null,
      availableStockQty: null,
      availableStockWeight: null,
    }
    const handleAddRow = (index: number, row: any, sourceType: SourceDetailDataEnum) => {
      if (sourceType == SourceDetailDataEnum.SOURCE && form.sourceList?.length) {
        const insertData = {
          ...row,
          ...initProductData
        }
        form.sourceList.splice(index + 1, 0, insertData);
      }
      if (sourceType == SourceDetailDataEnum.TARGET && form.targetList?.length) {
        const insertData = {
          ...row,
          ...initProductData
        }
        form.targetList.splice(index + 1, 0, insertData);
      }
    };

    const handleDeleteRow = (index: number, sourceType: SourceDetailDataEnum) => {
      if(sourceType == SourceDetailDataEnum.SOURCE){
        form.sourceList.splice(index, 1);
      }else{
        form.targetList.splice(index, 1);
      }
      if(form.sourceList.length === 0){
        outWarehouseAreaList.value = []
        chooseWarehouse.value = ''
      }
      if(form.targetList.length === 0){
        chooseInWarehouse.value = ''
      }
      ElMessage.success(t('productDisassemblyAssembleOrder.message.deleteSucess'));
    };

    async function handleClose() {
      router.push({
        path:'/wms/insideWarehouseManagement/productDisassemblyAssembleOrder',
      })
    }

    /** 添加/编辑库存转移 */
    function handleSubmit(){
        fromRef.value?.validate((valid) => {
            if (!valid) return;
            if(form.sourceList?.length == 0 || form.targetList?.length == 0){
                submitLoading.value = false;
                return  ElMessage.error(t('productDisassemblyAssembleOrder.message.addOrEditProductDisassemblyAssembleOrderTips'));
            }
            submitLoading.value=true;
            let isProductRepeat = false;
            let sourceList = []
            let targetList = []
            if(form.sourceList?.length>0){
              sourceList = form.sourceList.map(item => {
                return {...item}
              })
              /** 源商品判重 */
              productIsRepeat(sourceList, SourceDetailDataEnum.SOURCE,(result) => {
                isProductRepeat = result;
              })
              if(isProductRepeat){
                submitLoading.value = false;
                return ElMessage.error(t('productDisassemblyAssembleOrder.message.sourceProductRepeatTips'));
              }
            }
            if(form.targetList?.length>0){
                targetList = form.targetList.map(item => {
                  return {...item}
                })
              productIsRepeat(targetList, SourceDetailDataEnum.TARGET,(result) => {
                isProductRepeat = result;
              })
              if(isProductRepeat){
                submitLoading.value = false;
                return ElMessage.error(t('productDisassemblyAssembleOrder.message.targetProductReapetTips'));
              }
            }
            let params = {
                ...form,
                sourceList:sourceList,
                targetList:targetList,
            }
            params.disassemblyTemplateCode = templateOptionList.value?.find(item => item.id == form.disassemblyTemplateId)?.disassemblyTemplateCode
            params.disassemblyTemplateName = templateOptionList.value?.find(item => item.id == form.disassemblyTemplateId)?.disassemblyTemplateName

            if(type!=='add'){
               params.id = form.id
               params.disassemblyOrderCode= form.disassemblyOrderCode
            }
            const sourceAmountCalc = form.sourceList?.reduce((total, item) => {
                return Number(total) +  Number(item.amount || 0);
            }, 0).toFixed(2)
            const targetAmountCalc = form.targetList?.reduce((total, item) => {
              return Number(total) +  Number(item.amount || 0);
            }, 0).toFixed(2)
            let msg = `<span>${t('productDisassemblyAssembleOrder.message.sourceAmountCalc')}<span style="color:red">${t('productDisassemblyAssembleOrder.message.amountTotalTip')}【${sourceAmountCalc}】</span>,${t('productDisassemblyAssembleOrder.message.targetAmountCalc')}<span style="color:red">${t('productDisassemblyAssembleOrder.message.amountTotalTip')}【 ${targetAmountCalc}】</span>${t('productDisassemblyAssembleOrder.message.sumbitConfirmTips')}</span>`
            ElMessageBox.confirm(msg , t('common.tipTitle'), {
              confirmButtonText: t('common.confirm'),
              cancelButtonText: t('common.cancel'),
              dangerouslyUseHTMLString: true,
              showIcon: false,
              customClass: 'productDisassemblyAssembleOrderConfirmBox',
              type: "warning",
            }).then(
                () => {
                  ProductDisassemblyAssembleOrderAPI.submitProductDisassemblyAssembleOrder(params)
                      .then((data) => {
                        ElMessage.success(t('productDisassemblyAssembleOrder.message.confirmSucess'));
                        handleClose()
                      })
                      .finally(() => {
                        submitLoading.value = false;
                      });
                },
                () => {
                  submitLoading.value = false;
                  ElMessage.info(t('productDisassemblyAssembleOrder.message.confirmConcel'));
                }
            );
        })
    }

   /*商品判断是否重复*/
    function productIsRepeat(productData, sourceType, calBack){
      /** 目标商品判重 */
      let mapArr = productData?.map((item: any, index: any) => {
        let targetWarehouseAreaId = ''
        if(sourceType == SourceDetailDataEnum.SOURCE){
          targetWarehouseAreaId = item.sourceWarehouseAreaId
        }else{
          targetWarehouseAreaId = item.targetWarehouseAreaId
        }
        item.key = JSON.stringify({ productCode: item.productCode, targetWarehouseAreaId: targetWarehouseAreaId })
        return  item.key;
      });
      let setArr = new Set(mapArr);
      if(setArr.size < mapArr?.length){
        typeof calBack == 'function' && calBack(true)
      }else{
        typeof calBack == 'function' && calBack(false)
      }
    }

    /** 添加商品 */
   function addProduct(val) {
        outWarehouseAreaShow.value=val==1?true:false
        availableStockQtyShow.value=val==1?true:false
        outWarehouseAreaFromMultipShow.value=val==1?true:false
        dialog.title = val==1? t('productDisassemblyAssembleOrder.title.addSourceProduct'):val==2? t('productDisassemblyAssembleOrder.title.addTargetProduct'):t('productDisassemblyAssembleOrder.title.addProduct');
        let data = {
            typeList:val,
            status: val===1?'':'1',
            hasAvailableStockQty:val==1?true:'',
            isExcludeVirtualArea:val==1?false:'',
            enableExcludeVirtualArea:val==1?true:'',
            isShowAveragePrice: true,
        }
        addProductRef.value.setFormData({queryParams:data});
        addProductRef.value.getOutWarehouseAreaList();
        addProductRef.value.queryManagerCategoryList();
        dialog.visible = true;
    }

    onMounted(async () => {
      queryTemplateList()
      // getOutWarehouseAreaList();
      await getOutWarehouseAreaEnableList({status:1});
    });
</script>
<style lang="scss">
    .productDisassemblyAssembleOrderConfirmBox .el-message-box__status {
      display: none !important; /* 隐藏图标 */
    }
</style>
<style scoped lang="scss">
    .addProductDisassemblyAssembleOrder {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .trapezoid {
                max-width:80%;
                //width:250px;
                padding: 9px 50px 9px 26px;
                clip-path: polygon(0 0, 90% 0, 100% 100%, 0% 100%);
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 16px;
                color: #FFFFFF;
                text-align: left;
                font-style: normal;
            }
            right{
            }
            .fw-400{
                font-weight: 400;
                margin-right: 20px;
            }
            .trapezoid-color1 {
                background-color: var(--el-color-primary);
            }
            .trapezoid-color2 {
                background-color: #008E7C;
            }
            .button-add{
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                color: var(--el-color-primary)
            }
            .table-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 50px;
                background: #F4F6FA;
                box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #52585F;
                font-style: normal;
                padding: 15px 12px;
            }
        }
        .link{
            color: var(--el-color-primary);
            cursor: pointer;
        }
        .product-name {
          font-weight: 500;
          font-size: 14px;
          color: #151719;
        }
    }

    :deep(.el-dropdown){
      line-height: 23px;
      width: 100%;
    }
    :deep(.el-dropdown.is-disabled){
      color: #52585f;
    }

    .dropdown-trigger {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
    :deep(.el-dropdown-menu__item.is-selected) {
      color: #409EFF;
      background-color: #f5f7fa;
    }
</style>
