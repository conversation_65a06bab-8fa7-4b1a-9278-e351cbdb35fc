<template>
  <div class="app-container">
    <div class="addPage">
      <div class="page-content">
        <el-form :model="form" ref="formRef" label-width="112px" label-position="right">
          <div class="basicInformation item_content">
            <div class="flex-center-but">
              <div class="title">
                <div>{{ $t("quickOutbound.label.documentInformation") }}</div>
              </div>
              <div v-if="firstTableShow" @click="closeFirstTable" style="font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 14px;color: #762ADB;line-height: 22px;text-align: left;font-style: normal;">收起</div>
              <div v-if="!firstTableShow" @click="openFirstTable" style="font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 14px;color: #762ADB;line-height: 22px;text-align: left;font-style: normal;">全部展开</div>
            </div>
            <el-row>
              <!-- 出库通知单号 -->
              <el-col :span="8">
                <el-form-item :label="$t('quickOutbound.label.outboundNoticeCode')">
                  <el-input v-model="form.outboundNoticeCode" :placeholder="$t('quickOutbound.placeholder.systemCreate')" disabled class="!w-[256px]"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 出库类型:1:销售出库、2:提货卡兑换、3：业务招待、4：补发、5：地头售卖、6:采购退货、7：调拨出库、8：直接出库、9：参展 -->
                <el-form-item :label="$t('quickOutbound.label.outboundType')">
                  <el-select v-model="form.outboundType" :placeholder="$t('common.placeholder.selectTips')" class="w-full !w-[256px]" disabled>
                    <el-option :label="$t('quickOutbound.outboundTypeList.directOutbound')" :value="8" />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 主题描述 -->
              <el-col :span="8">
                <el-form-item :label="$t('quickOutbound.label.themeDescription')">
                  <el-input v-model.trim="form.orderTheme" :placeholder="$t('common.placeholder.inputTips')" maxlength="100" class="!w-[256px]"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 业务员 -->
              <el-col :span="8">
                <el-form-item :label="$t('quickOutbound.label.salesperson')">
                  <el-select v-model="form.purchaseSalesPersonId" :placeholder="$t('quickOutbound.placeholder.userNow')" @change="setPurchaseSalesPerson" clearable class="!w-[256px]">
                    <el-option v-for="item in personList" :key="item.userId" :label="item.nickName"
                               :value="item.userId" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 实际配送方式 -->
                <el-form-item :label="$t('quickOutbound.label.actualDistributionMethod')" prop="actualDeliveryType" :rules="[{required:true,message:t('quickOutbound.rules.actualDeliveryType'),trigger:['blur']}]">
                  <el-select
                    v-model="form.actualDeliveryType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    class="!w-[256px]"
                    @change="setName"
                  >
                    <el-option
                      v-for="(item, index) in deliveryTypeList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!--  磅单编号 -->
                <el-form-item :label="$t('quickOutbound.label.weighbillNumber')" prop="poundCode">
                  <el-input
                    v-model="form.poundCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                    class="!w-[256px]"
                  >
                    <template #append>
                      <div v-if="form.fileNum">
                        <el-badge :value="form.fileNum" :offset="[10, 8]" class="item" type="primary">
                          <!-- 上传 -->
                          <el-button type="primary" link @click="handleUpload(form.poundAttachmentFiles)">
                            <span style="color: #762ADB">{{ $t("quickOutbound.button.upload") }}</span>
                          </el-button>
                        </el-badge>
                      </div>
                      <div v-else>
                        <!-- 上传 -->
                        <el-button type="primary" link @click="handleUpload(form.poundAttachmentFiles)">
                          <span style="color: #762ADB">{{ $t("quickOutbound.button.upload") }}</span>
                        </el-button>
                      </div>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <div v-if="firstTableShow">
              <el-row>
                <!-- 来源单号 -->
                <el-col :span="8">
                  <el-form-item :label="$t('quickOutbound.label.sourceOrderCode')">
                    <el-input v-model.trim="form.sourceOrderCode" :placeholder="$t('common.placeholder.inputTips')" maxlength="30" class="!w-[256px]"></el-input>
                  </el-form-item>
                </el-col>
                <!-- 原单号 -->
                <el-col :span="8">
                  <el-form-item :label="$t('quickOutbound.label.originalOrderNumber')">
                    <el-input v-model.trim="form.sourceCode" :placeholder="$t('common.placeholder.inputTips')" maxlength="30" class="!w-[256px]"></el-input>
                  </el-form-item>
                </el-col>
                <!-- 申请人 -->
                <el-col :span="8">
                  <el-form-item :label="$t('quickOutbound.label.createUserName')" prop="">
                    <el-select v-model="form.createUser" :placeholder="$t('quickOutbound.placeholder.userNow')" @change="setCreateUserName" clearable class="!w-[256px]">
                      <el-option v-for="item in personList" :key="item.userId" :label="item.nickName"
                                 :value="item.userId" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <!-- 申请时间 -->
                <el-col :span="8">
                  <el-form-item :label="$t('quickOutbound.label.createTime')" prop="createTime">
                    <el-date-picker
                      class="!w-[256px]"
                      v-model="form.createTime"
                      type="datetime"
                      :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <!-- 客户 -->
                <el-col :span="8">
                  <el-form-item :label="$t('quickOutbound.label.customerName')">
                    <el-input v-model.trim="form.customerName" maxlength="100" class="!w-[256px]" clearable :placeholder="$t('common.placeholder.inputTips')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 客户地址 -->
                  <el-form-item :label="$t('quickOutbound.label.customerAddress')">
                    <el-form-item prop="countryId" label-width="0">
                      <!-- 国家 -->
                      <el-select v-model="form.countryId" :placeholder="$t('common.placeholder.selectTips')" clearable @change="countryChange" class="!w-[100px]">
                        <el-option
                          v-for="(item, index) in countryList"
                          :key="index"
                          :label="item.shortName"
                          :value="item.id"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="provinceId" label-width="0">
                      <!-- 省 -->
                      <el-select :disabled="!form.countryId" v-model="form.provinceId" :placeholder="$t('common.placeholder.selectTips')" @change="handleProvinceChange" class="!w-[100px]" clearable>
                        <el-option
                          v-for="(item, index) in provinceList"
                          :key="index"
                          :label="item.shortName"
                          :value="item.id"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="cityId" label-width="0" v-if="showCityInput">
                      <!-- 市 -->
                      <el-select v-model="form.cityId" :placeholder="$t('common.placeholder.selectTips')" @change="handleCityChange" class="!w-[120px]" clearable>
                        <el-option
                          v-for="(item, index) in cityList"
                          :key="index"
                          :label="item.shortName"
                          :value="item.id"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="districtId" label-width="0" v-if="showDistrictInput">
                      <!-- 区 -->
                      <el-select v-model="form.districtId" :placeholder="$t('common.placeholder.selectTips')" @change="handleDistrictChange" class="!w-[120px]" clearable>
                        <el-option
                          v-for="(item, index) in districtList"
                          :key="index"
                          :label="item.shortName"
                          :value="item.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item prop="address" label="详细地址">
                    <el-input v-model.time="form.address" :placeholder="$t('warehouse.placeholder.detailAddressInputTips')" maxlength="100" clearable class="!w-[256px]"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 客户联系人 -->
                  <el-form-item :label="$t('quickOutbound.label.customerPerson')">
                    <el-input v-model.trim="form.contactPerson" :placeholder="$t('common.placeholder.inputTips')" maxlength="30" class="!w-[256px]"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 客户联系电话 -->
                  <el-form-item
                    :label="$t('quickOutbound.label.customerMobile')"
                    prop="customerMobile"
                  >
                    <el-input
                      v-model="form.customerMobile"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                      maxlength="30"
                      class="!w-[256px]"
                    >
                      <template #prepend>
                        <el-select
                          v-model="form.customerAreaCode"
                          style="width: 80px;background: white;"
                        >
                          <el-option
                            v-for="(item, index) in areaList"
                            :key="index"
                            :label="item.internationalCode"
                            :value="item.internationalCode"
                          />
                        </el-select>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <!-- 要求到货时间 -->
                <el-col :span="8">
                  <el-form-item :label="$t('quickOutbound.label.plannedReceivedTime')" prop="plannedReceivedTime">
                    <el-date-picker
                      class="!w-[256px]"
                      v-model="form.plannedReceivedTime"
                      type="datetime"
                      :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <!-- 计划发货时间 -->
                <el-col :span="8">
                  <el-form-item prop="plannedDeliveryTime" :label="$t('quickOutbound.label.plannedDeliveryTime')">
                    <el-date-picker
                      class="!w-[256px]"
                      v-model="form.plannedDeliveryTime"
                      type="datetime"
                      :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <!-- 实际出库时间 -->
                <el-col :span="8">
                  <el-form-item prop="outboundTime" :label="$t('quickOutbound.label.actualOutboundTime')">
                    <el-date-picker
                      class="!w-[256px]"
                      v-model="form.outboundTime"
                      type="datetime"
                      :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <!--  承运商 -->
                  <el-form-item :label="$t('quickOutbound.label.carrier')" prop="carrier">
                    <el-input
                      v-model="form.carrier"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="30"
                      clearable
                      class="!w-[256px]"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!--  车号 -->
                  <el-form-item :label="$t('quickOutbound.label.vehicleNumber')" prop="carNumber">
                    <el-input
                      v-model="form.carNumber"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="30"
                      clearable
                      class="!w-[256px]"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- 出库备注 -->
                  <el-form-item :label="$t('quickOutbound.label.outboundRemark')" prop="outboundRemark">
                    <el-input
                      v-model="form.outboundRemark"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                      type="textarea"
                      maxlength="200"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              <div class="flex_style">
                <div>{{ $t("quickOutbound.label.waitOutboundProductDetail") }}</div>
                <div><el-button type="primary" @click="handleGoodsSelect">{{ t('common.add') }}</el-button></div>
              </div>
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 商品列表 -->
                <el-form-item label-width="0">
                  <el-table
                    :data="form.warehouseOutboundDetailVOList"
                    v-loading="formLoading"
                    stripe
                    highlight-current-row
                    show-summary
                    :summary-method="getSummaries"
                    border
                  >
                    <el-table-column fixed="left" type="index" :label="$t('common.sort')" width="60" align="center"/>
                    <!-- 商品信息 -->
                    <el-table-column :label="$t('quickOutbound.label.productInfo')" width="250">
                      <template #default="scope">
                        <div style="word-break: break-all">
                          <span style="color: #90979E;">{{ scope.row.productCode }} |</span><span style="color:#52585F;">{{ scope.row.productName }}</span>
                          <!--<div style="color: #90979E;">{{ scope.row.productCode }}</div>
                          <div style="color:#52585F;word-break: break-all">{{ scope.row.productName }}</div>-->
                        </div>
                      </template>
                    </el-table-column>
                    <!-- 单据号 -->
                    <el-table-column :label="$t('quickOutbound.label.documentNumber')" min-width="150">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.documentNumber'">
                          <el-select filterable clearable v-model="scope.row.docketCode" @change="setWarehouseAreaCode(scope.$index)" :placeholder="$t('common.placeholder.selectTips')">
                            <el-option v-for="item in scope.row.productDocketCodeInfoVOList" :label="item.billNo" :value="item.billNo"></el-option>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 出库库区 -->
                    <el-table-column width="130" :label="$t('quickOutbound.label.outboundStorageArea')">
                      <template #header>
                        <el-dropdown trigger="click" @command="handleFilterChange" placement="bottom-end" :disabled="!outWarehouseAreaList || outWarehouseAreaList.length ===0 || !form.warehouseOutboundDetailVOList || form.warehouseOutboundDetailVOList.length === 0">
                          <span class="dropdown-trigger">
                            {{ $t('quickOutbound.label.outboundStorageArea') }}
                            <svg-icon style="height: 1em;width: 1em;" icon-class="iconBottom"></svg-icon>
                          </span>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item
                                v-for="item in outWarehouseAreaList"
                                :key="item.areaCode"
                                :command="item.areaCode"
                                :class="{ 'is-selected': chooseWarehouse === item.areaCode }">
                                {{ item.areaName }}
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </template>
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.warehouseAreaCode'"
                                      :rules="(!scope.row.outboundQty || scope.row.outboundQty == 0) && (!scope.row.outboundWeight || scope.row.outboundWeight == 0) ? [] : [{required: true,message: t('quickOutbound.rules.warehouseAreaCode'),trigger: ['blur'],},]"
                        >
                          <!--@focus="getOutWarehouseAreaList(scope.row.productCode,scope.row.isDiscreteUnit,scope.$index)"-->
                          <el-select
                            v-model="scope.row.warehouseAreaCode"
                            :placeholder="$t('common.placeholder.selectTips')"
                            @change="getWarehouseAreaDetail($event, scope.$index)"
                            clearable
                            :loading="scope.row.areaLoading"
                            :disabled="scope.row.docketCode"
                          >
                            <el-option
                              v-for="item in outWarehouseAreaList"
                              :key="item.areaCode"
                              :label="item.areaName"
                              :value="item.areaCode"
                            />
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 出库量 -->
                    <el-table-column min-width="240" :label="$t('quickOutbound.label.outboundQuantity')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.outboundQty'"
                                      :rules="[{required: false,},
                            {pattern:/^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,message: t('quickOutbound.rules.outboundQtyFormat'),trigger: ['blur','change'],},]"
                        >
                          <el-input v-model="scope.row.outboundQty" @change="setOutboundWeight(scope.$index)" clearable :placeholder="$t('common.placeholder.inputTips')"><template #append>{{ scope.row.productUnitName }}</template></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 销售单价 -->
                    <el-table-column min-width="240" align="right" :label="$t('quickOutbound.label.unitPrice')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.salePrice'" :rules="[
                            {
                              required: false,
                            },
                            {
                              pattern:
                                /^(-?0(?:\.\d{1,4})?|-?[1-9]\d{0,6}(?:\.\d{1,4})?)$/,
                              message: t(
                                'quickOutbound.rules.salePriceFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]">
                          <el-input v-model="scope.row.salePrice" @change="setSaleAmount(scope.$index)" clearable :placeholder="$t('common.placeholder.inputTips')">
                            <template #append>元/{{scope.row.pricingScheme == 0 ? scope.row.productUnitName : scope.row.conversionRelSecondUnitName}}</template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 销售金额 -->
                    <el-table-column min-width="200" align="right" :label="$t('quickOutbound.label.amount')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.saleAmount'" :rules="[
                            {
                              required: false,
                            },
                            {
                              pattern:
                                /^(-?0(?:\.\d{1,2})?|-?[1-9]\d{0,8}(?:\.\d{1,2})?)$/,
                              message: t(
                                'quickOutbound.rules.saleAmountFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]">
                          <el-input v-model="scope.row.saleAmount" @change="getTotalAmount" clearable :placeholder="$t('common.placeholder.inputTips')"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 品牌 -->
                    <el-table-column :label="$t('quickOutbound.label.brand')" prop="productBrandName" min-width="100"/>
                    <!-- 商品包装 -->
                    <el-table-column min-width="130" :label="$t('quickOutbound.label.goodsPackaging')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.productPackage'">
                          <el-input v-model="scope.row.productPackage" clearable :placeholder="$t('common.placeholder.inputTips')" maxlength="30"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 出库转换量 -->
                    <el-table-column min-width="240" :label="$t('quickOutbound.label.outboundConversionQuantity')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.outboundWeight'"
                          :rules="[{required: false,},
                            {pattern:/^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,message: t('quickOutbound.rules.outboundWeightFormat'),trigger: ['blur','change'],},]"
                        >
                          <el-input v-model="scope.row.outboundWeight" @change="changeWeight(scope.$index)" clearable :placeholder="$t('common.placeholder.inputTips')"><template #append>{{ scope.row.conversionRelSecondUnitName }}</template></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 剩余可用库存 -->
                    <!--<el-table-column min-width="200" :label="$t('quickOutbound.label.remainingAvailableInventory')">
                      <template #default="scope">
                        <div>{{$t('quickOutbound.label.remainingAvailableQuantity')}}：<span style="color: #90979E ">{{scope.row.availableQty == null ? '-' : scope.row.availableQty}}</span></div>
                        <div>{{$t('quickOutbound.label.remainingConversionQuantity')}}：<span style="color: #90979E ">{{scope.row.availableWeight == null ? '-' : scope.row.availableWeight}}</span></div>
                      </template>
                    </el-table-column>-->
                    <!--可用库存-->
                    <el-table-column width="200" label="可用库存" prop="available">
                      <template #default="scope">
                        <div style="word-break: break-all">{{scope.row.available}}</div>
                      </template>
                    </el-table-column>
                    <!-- 规格 -->
                    <el-table-column :label="$t('quickOutbound.label.productSpecs')" prop="productSpecs" min-width="100">
                      <template #default="scope">
                        <div>{{ scope.row.productSpecs || '-' }}</div>
                      </template>
                    </el-table-column>
                    <!-- 商品属性 -->
                    <el-table-column :label="$t('quickOutbound.label.commodityProperty')" prop="attributeTypeName" min-width="100">
                      <template #default="scope">
                        <div>{{ scope.row.attributeTypeName || '-' }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="140">
                      <template #default="scope">
                        <el-button
                          type="success"
                          circle
                          plain
                          size="small"
                          @click="handleAdd(scope.row.productCode,scope.$index)"
                        >
                          {{ $t("quickOutbound.button.add") }}
                        </el-button>
                        <el-button
                          type="danger"
                          circle
                          plain
                          size="small"
                          @click="handleDelete(scope.$index,scope.row.isParent,scope.row.productCode)"
                        >
                          {{ $t("quickOutbound.button.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("quickOutbound.button.cancel") }}
          </el-button>
          <el-button type="primary" plain :loading="saveDraftLoading" @click="handleSaveDraft">
            {{ $t("quickOutbound.button.saveDraft") }}
          </el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            {{ $t("quickOutbound.button.submit") }}
          </el-button>
        </div>
      </div>
    </div>
    <UploadDialog
      v-model:visible="uploadDialog.visible"
      ref="uploadDialogRef"
      @on-submit="onSubmitUpload"
    />
    <AddGoods
      ref="addGoodsRef"
      v-model:visible="goodsDialog.visible"
      @on-submit="onSubmitGoods"
    />
  </div>
</template>

<script setup lang="ts">
import {parseDateTime} from "@/core/utils";

defineOptions({
  name: "AddOrEditOutbound",
  inheritAttrs: false,
});
import CommonAPI from "@/modules/wms/api/common";
import OutboundNoticeAPI from "@/modules/wms/api/outboundNotice";
import QuickOutboundApi, {confirmOutboundRequest} from "@/modules/wms/api/quickOutbound"
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
import UploadDialog from "./components/uploadDialog.vue";
import AddGoods from "./components/addProduct.vue";
import {useUserStore} from "@/core/store";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const type = route.query.type;
const uploadDialog = reactive({
  visible: false,
});
const uploadDialogRef = ref();
const goodsDialog = reactive({
  visible: false,
});
const addGoodsRef = ref();
const deliveryTypeList = ref([]);
const formRef = ref(ElForm);
const userStore = useUserStore();
// 表单
const form = reactive<confirmOutboundRequest>({
  id: "",
  fileNum:'',
  customerAreaCode:'+86',
  warehouseOutboundDetailVOList: [],
  totalOutboundQty:'',//出库量合计
  totalOutboundWeight:'',//出库转换量合计
  totalSaleAmount:'',//销售金额合计
});
const firstTableShow = ref(true)
function closeFirstTable() {
  firstTableShow.value = false
}
function openFirstTable() {
  firstTableShow.value = true
}
const saveDraftLoading = ref(false);
const loading = ref(false);
const formLoading = ref(false);
const personList = ref([]);
const areaList = ref([]);
const countryList = ref([]);
const provinceList = ref([]);
const cityList = ref([]);
const districtList = ref([]);
const showCityInput = ref(false);
const showDistrictInput = ref(false);
const outWarehouseAreaList = ref([]);
const chooseWarehouse = ref('');

/** 查询出库库区列表(启用) */
function getOutWarehouseAreaEnableList(params) {
  return new Promise((resolve, reject) => {
    CommonAPI.getOutWarehouseAreaList(params)
      .then((data) => {
        outWarehouseAreaList.value = data;
        resolve();
      })
      .catch((error) => {
        loading.value = false;
        reject(error);
      })
  });
}
function handleFilterChange(val) {
  if(val) {
    chooseWarehouse.value = val
    form.warehouseOutboundDetailVOList.forEach((list,index)=>{
      if(!list.warehouseAreaCode){
        list.warehouseAreaCode = chooseWarehouse.value
        getWarehouseAreaDetail(list.warehouseAreaCode,index)
      }
    })
  }
}
function setWarehouseAreaCode(index) {
  if(form.warehouseOutboundDetailVOList[index].docketCode){
    for(let i = 0; i< form.warehouseOutboundDetailVOList[index].productDocketCodeInfoVOList.length; i++){
      let warehouseList = []
      outWarehouseAreaList.value.forEach(areaList=>{
        warehouseList.push(areaList.areaCode)
      })
      let list = form.warehouseOutboundDetailVOList[index].productDocketCodeInfoVOList[i]
      if(warehouseList.length>0 && list.warehouseAreaCode && !warehouseList.includes(list.warehouseAreaCode)){
        //单据所在库区已禁用
        return ElMessage.warning('单据所在库区已禁用');
      }
      if(list.billNo == form.warehouseOutboundDetailVOList[index].docketCode){
        form.warehouseOutboundDetailVOList[index].warehouseAreaCode = list.warehouseAreaCode
        form.warehouseOutboundDetailVOList[index].warehouseAreaName = list.warehouseAreaName
        break
      }
    }
  }
}
// 获取区号
function getAreaList() {
  OutboundNoticeAPI.getAllCountry()
    .then((data: any) => {
      areaList.value = data;
    })
    .finally(() => {});
}
/** 获取国家列表 */
function queryAllCountry() {
  const queryParams = {
    pid: "0",
  };
  CommonAPI.getAreaList(queryParams)
    .then((data: any) => {
      countryList.value = data;
    })
    .finally(() => {});
}
function getAreaApi(val: any, tab?: any) {
  let params = {
    pid: val,
  };
  CommonAPI.getAreaList(params)
    .then((data: any) => {
      if (tab == "province") {
        provinceList.value = data;
      } else if (tab == "city") {
        if (data.length > 0) {
          cityList.value = data;
          showCityInput.value = true;
        } else {
          showCityInput.value = false;
        }
      } else {
        if (data.length > 0) {
          showDistrictInput.value = true;
          districtList.value = data;
        } else {
          showDistrictInput.value = false;
        }
      }
    })
    .finally(() => {});
}
function countryChange(val: any) {
  form.provinceId = "";
  form.provinceName = "";
  provinceList.value = [];
  form.cityId = "";
  form.cityName = "";
  cityList.value = [];
  form.districtId = "";
  form.districtName = "";
  districtList.value = [];
  showCityInput.value = false;
  showDistrictInput.value = false;
  if (form.countryId) {
    let data: any = countryList.value.find(
      (item: any) => item.id === form.countryId
    );
    if (data) {
      form.countryName = data.shortName ? data.shortName : "";
    } else {
      form.countryName = "";
    }
    getAreaApi(form.countryId, "province");
  }
}
function handleProvinceChange(val: any) {
  form.cityId = "";
  form.cityName = "";
  cityList.value = [];
  form.districtId = "";
  form.districtName = "";
  districtList.value = [];
  showCityInput.value = false;
  showDistrictInput.value = false;
  let data: any = provinceList.value.find(
    (item: any) => item.id === form.provinceId
  );
  if (data) {
    form.provinceName = data.shortName ? data.shortName : "";
  } else {
    form.provinceName = "";
  }
  if (val) {
    getAreaApi(val, "city");
  }
}

function handleCityChange(val: any) {
  form.districtId = "";
  form.districtName = "";
  districtList.value = [];
  showDistrictInput.value = false;
  let data: any = cityList.value.find(
    (item: any) => item.id === form.cityId
  );
  if (data) {
    form.cityName = data.shortName ? data.shortName : "";
  } else {
    form.cityName = "";
  }
  if (val) {
    getAreaApi(val, "district");
  }
}

function handleDistrictChange(val: any) {
  let data: any = districtList.value.find(
    (item: any) => item.id === form.districtId
  );
  if (data) {
    form.districtName = data.shortName ? data.shortName : "";
  } else {
    form.districtName = "";
  }
}
function queryPersonList() {
  QuickOutboundApi.querySalesPersonUser().then((res)=>{
    personList.value = res
  })
}
// 上传
function handleUpload() {
  uploadDialog.visible = true;
  uploadDialogRef.value.setEditType("add");
  if (form.poundAttachmentFiles) {
    uploadDialogRef.value.setFormData(JSON.parse(form.poundAttachmentFiles));
  }
}

//上传提交
function onSubmitUpload(data: any) {
  if (data) {
    form.poundAttachmentFiles = JSON.stringify(data);
    form.fileNum = data.length;
  }
}
function getSummaries(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 3) {
      sums[index] = '合计:';
      return;
    }else if(index === 4){
      sums[index] = form.totalOutboundQty;
      return;
    }else if(index === 6){
      sums[index] = form.totalSaleAmount;
      return;
    }else if(index === 9){
      sums[index] = form.totalOutboundWeight;
      return;
    }else{
      sums[index] = ' '
      return
    }
  });
  return sums;
}
//出库量合计
function getTotalOutboundQty(){
  let totalOutboundQty = '0'
  form.warehouseOutboundDetailVOList.forEach(list=>{
    if(list.outboundQty){
      totalOutboundQty = (parseFloat(totalOutboundQty) + parseFloat(list.outboundQty)).toFixed(3)
    }
    form.totalOutboundQty = totalOutboundQty
  })
}
//出库转换量合计
function getTotalOutboundWeight() {
  let totalOutboundWeight = '0'
  form.warehouseOutboundDetailVOList.forEach(list=>{
    if(list.outboundWeight){
      totalOutboundWeight = (parseFloat(totalOutboundWeight) + parseFloat(list.outboundWeight)).toFixed(3)
    }
    form.totalOutboundWeight = totalOutboundWeight
  })
}
//查询当前商品总库存大于0的库区
function getOutWarehouseAreaList(productCode,isDiscreteUnit,index) {
  form.warehouseOutboundDetailVOList[index].areaLoading = true
  let params = {
    productCode:productCode,
    isDiscreteUnit:isDiscreteUnit,
  }
  CommonAPI.queryHasStockListByReq(params).then((res)=>{
    form.warehouseOutboundDetailVOList[index].outWarehouseAreaList = res
  }).finally(()=>{
    form.warehouseOutboundDetailVOList[index].areaLoading = false
  })

}
//查询所选库区详情
function getWarehouseAreaDetail(e,index) {
  if(e){
    outWarehouseAreaList.value.forEach(list=>{
      if(list.areaCode == e){
        form.warehouseOutboundDetailVOList[index].warehouseAreaName = list.areaName
      }
    })
  }else{
    form.warehouseOutboundDetailVOList[index].warehouseAreaName = ''
  }
}

//一级单位转二级单位
function setOutboundWeight(index) {
  getTotalOutboundQty()
  if(form.warehouseOutboundDetailVOList[index].isDiscreteUnit){
    if(form.warehouseOutboundDetailVOList[index].outboundQty){
      let params = {
        convertUnitTypeEnum:'FIRST_TO_SECOND',
        originalValue:parseFloat(form.warehouseOutboundDetailVOList[index].outboundQty).toFixed(3),
        productCode:form.warehouseOutboundDetailVOList[index].productCode
      }
      CommonAPI.convertProductUnit(params).then((res)=>{
        form.warehouseOutboundDetailVOList[index].outboundWeight = res.convertedValue
        clearWarehouseAreaCodeValidate(index)
        getTotalOutboundWeight()
      })
    }
  }else{
    if(form.warehouseOutboundDetailVOList[index].outboundQty && !form.warehouseOutboundDetailVOList[index].outboundWeight){
      let params = {
        convertUnitTypeEnum:'FIRST_TO_SECOND',
        originalValue:parseFloat(form.warehouseOutboundDetailVOList[index].outboundQty).toFixed(3),
        productCode:form.warehouseOutboundDetailVOList[index].productCode
      }
      CommonAPI.convertProductUnit(params).then((res)=>{
        form.warehouseOutboundDetailVOList[index].outboundWeight = res.convertedValue
        clearWarehouseAreaCodeValidate(index)
        getTotalOutboundWeight()
      })
    }
  }
  if(!form.warehouseOutboundDetailVOList[index].outboundQty){
    clearWarehouseAreaCodeValidate(index)
  }
  setSaleAmount(index)
}
//单价*出库量计算金额
function setSaleAmount(index) {
  if(form.warehouseOutboundDetailVOList[index].outboundQty && form.warehouseOutboundDetailVOList[index].salePrice){
    form.warehouseOutboundDetailVOList[index].saleAmount = (parseFloat(form.warehouseOutboundDetailVOList[index].outboundQty)*parseFloat(form.warehouseOutboundDetailVOList[index].salePrice)).toFixed(2)
    getTotalAmount()
  }
}
//计算合计金额
function getTotalAmount() {
  let totalAmount = '0'
  form.warehouseOutboundDetailVOList.forEach(list=>{
    if(list.saleAmount){
      totalAmount = (parseFloat(totalAmount) + parseFloat(list.saleAmount)).toFixed(2)
    }
  })
  form.totalSaleAmount = totalAmount
}
//改变出库转换量联动计算
function changeWeight(index) {
  getTotalOutboundWeight()
  if(!form.warehouseOutboundDetailVOList[index].isDiscreteUnit && form.warehouseOutboundDetailVOList[index].outboundWeight && !form.warehouseOutboundDetailVOList[index].outboundQty){
    let params = {
      convertUnitTypeEnum:'SECOND_TO_FIRST',
      originalValue:parseFloat(form.warehouseOutboundDetailVOList[index].outboundWeight).toFixed(3),
      productCode:form.warehouseOutboundDetailVOList[index].productCode
    }
    CommonAPI.convertProductUnit(params).then((res)=>{
      form.warehouseOutboundDetailVOList[index].outboundQty = res.convertedValue
      clearWarehouseAreaCodeValidate(index)
      getTotalOutboundQty()
    })
  }
  if(!form.warehouseOutboundDetailVOList[index].outboundWeight){
    clearWarehouseAreaCodeValidate(index)
  }
}
function clearWarehouseAreaCodeValidate(index){
  if((!form.warehouseOutboundDetailVOList[index].outboundQty || form.warehouseOutboundDetailVOList[index].outboundQty == 0) && (!form.warehouseOutboundDetailVOList[index].outboundWeight || form.warehouseOutboundDetailVOList[index].outboundWeight == 0)){
    formRef.value?.clearValidate('warehouseOutboundDetailVOList.'+index+'.warehouseAreaCode')
  }
}
function handleGoodsSelect() {
  goodsDialog.visible = true;
  addGoodsRef.value.queryGoodList();
  addGoodsRef.value.queryManagerCategoryList();
}
function onSubmitGoods(data: any) {
  const promises = data.map((product,index) => {
    return new Promise((resolve) => {
      const existingIndex = form.warehouseOutboundDetailVOList?.findIndex(
        (item: any) => item.productCode === product.productCode
      );

      if (existingIndex !== undefined && existingIndex >= 0) {
        // 如果商品已存在，在商品下方插入一条数据
        if (form.warehouseOutboundDetailVOList && form.warehouseOutboundDetailVOList[existingIndex]) {
          handleAdd(form.warehouseOutboundDetailVOList[existingIndex].productCode,existingIndex)
        }
      } else {
        //available
        let params = {
          productCode:product.productCode,
          isDiscreteUnit:product.isDiscreteUnit,
          productUnitName:product.productUnitName,
          conversionRelSecondUnitName:product.conversionRelSecondUnitName
        }
        /*CommonAPI.queryAvailableStockInfoListByReq(params).then((res)=>{
          product.isParent = true
          product.available = res
          product.salePrice = null
          product.saleAmount = null
          resolve(product);
        })*/
        QuickOutboundApi.queryBillNoListByReq(params).then((res)=>{
          product.productDocketCodeInfoVOList = res.productDocketCodeInfoVOList
          product.isParent = true
          product.available = res.stockInfo
          product.salePrice = null
          product.saleAmount = null
          resolve(product);
        })
      }
    });
  });
  let warehouseOutboundDetailVOList = []
  Promise.all(promises).then((results) => {
    results.forEach((a) => {
      warehouseOutboundDetailVOList.push(a);
    });
    let arr = []
    warehouseOutboundDetailVOList.forEach(list=>{
      let obj = {
        productCode:list.productCode
      }
      arr.push(obj)
    })
    let uniqueArr = [
      ...new Map(arr.map((item: any) => [item.productCode, item])).values(),
    ];
    for(let i = 0; i < uniqueArr.length; i++) {
      let innerArr = warehouseOutboundDetailVOList.filter(function (list) {
        return list.productCode == uniqueArr[i].productCode
      })
      innerArr.forEach((a,index)=> {
        if (index == 0) {
          a.isParent = true
          a.index = i+1
        } else {
          a.isParent = false
        }
        form.warehouseOutboundDetailVOList.push(a)
      })
    }
  });

}
function handleAdd(productCode,index) {
  for(let i = 0; i < form.warehouseOutboundDetailVOList.length; i++){
    let item = form.warehouseOutboundDetailVOList[i]
    if(item.productCode == productCode && i == index){
      let num = i + 1
      form.warehouseOutboundDetailVOList.splice(num, 0, {
        id:item.id,
        productCode:item.productCode,
        productName:item.productName,
        firstCategoryId: item.firstCategoryId,
        firstCategoryName: item.firstCategoryName,
        productSpecs: item.productSpecs,
        productUnitId: item.productUnitId,
        productUnitName: item.productUnitName,
        conversionRelSecondUnitId: item.conversionRelSecondUnitId,
        conversionRelSecondUnitName: item.conversionRelSecondUnitName,
        secondCategoryId: item.secondCategoryId,
        secondCategoryName: item.secondCategoryName,
        thirdCategoryId: item.thirdCategoryId,
        thirdCategoryName: item.thirdCategoryName,
        attributeType:item.attributeType,
        attributeTypeName:item.attributeTypeName,
        pricingScheme:item.pricingScheme,
        isDiscreteUnit:item.isDiscreteUnit,
        available:item.available,
        remark:item.remark,
        productBrandName:item.productBrandName,
        productBrandId:item.productBrandId,
        isParent:false,
      })
      break
    }
  }
}

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.go(-1);
};

function handleDelete(index,isParent,productCode) {
  form.warehouseOutboundDetailVOList.splice(index, 1);
  if(isParent){
    let arr = form.warehouseOutboundDetailVOList.filter(function (item) {
      return item.productCode == productCode
    })
    if(arr.length > 0){
      arr[0].isParent = true
    }
  }
  if(form.warehouseOutboundDetailVOList.length === 0){
    outWarehouseAreaList.value = []
    chooseWarehouse.value = ''
  }
  getTotalOutboundQty()
  getTotalOutboundWeight()
}
// 保存草稿
function handleSaveDraft() {
  saveDraftLoading.value = true
  let fastOutboundProductInfoDTOList = []
  if(form.warehouseOutboundDetailVOList && form.warehouseOutboundDetailVOList.length > 0){
    let arr = form.warehouseOutboundDetailVOList.filter(function (list) {
      return list.isParent
    })
    arr.forEach(item => {
      let fastOutboundProductDetailDTOList = []
      let obj = {
        actualPickThisTime: item.outboundQty || 0,
        warehouseAreaActualPickWeight: item.outboundWeight || 0,
        productPackage: item.productPackage,
        warehouseAreaCode: item.warehouseAreaCode,
        warehouseAreaName: item.warehouseAreaName,
        docketCode: item.docketCode,
        salePrice: item.salePrice,
        saleAmount: item.saleAmount,
      }
      fastOutboundProductDetailDTOList.push(obj)
      form.warehouseOutboundDetailVOList.forEach(list=>{
        if(item.productCode == list.productCode && !list.isParent){
          let obj1 = {
            actualPickThisTime: list.outboundQty || 0,
            warehouseAreaActualPickWeight: list.outboundWeight || 0,
            productPackage: list.productPackage,
            warehouseAreaCode: list.warehouseAreaCode,
            warehouseAreaName: list.warehouseAreaName,
            docketCode: list.docketCode,
            salePrice: list.salePrice,
            saleAmount: list.saleAmount,
          }
          fastOutboundProductDetailDTOList.push(obj1)
        }
      })
      item.fastOutboundProductDetailDTOList = fastOutboundProductDetailDTOList
    })
    arr.forEach(item=>{
      let obj = {
        outboundProductId: item.id,
        productCode: item.productCode,
        productName: item.productName,
        firstCategoryId: item.firstCategoryId,
        firstCategoryName: item.firstCategoryName,
        productSpecs: item.productSpecs,
        productUnitId: item.productUnitId,
        productUnitName: item.productUnitName,
        conversionRelSecondUnitId: item.conversionRelSecondUnitId,
        conversionRelSecondUnitName: item.conversionRelSecondUnitName,
        secondCategoryId: item.secondCategoryId,
        secondCategoryName: item.secondCategoryName,
        thirdCategoryId: item.thirdCategoryId,
        thirdCategoryName: item.thirdCategoryName,
        pricingScheme: item.pricingScheme,
        isDiscreteUnit: item.isDiscreteUnit,
        attributeType:item.attributeType,
        attributeTypeName:item.attributeTypeName,
        productBrandId:item.productBrandId,
        productBrandName:item.productBrandName,
        fastOutboundProductDetailDTOList: item.fastOutboundProductDetailDTOList
      }
      fastOutboundProductInfoDTOList.push(obj)
    })
  }
  let params = {
    isSubmit:0,
    outboundType:form.outboundType,
    orderTheme:form.orderTheme,
    sourceOrderCode:form.sourceOrderCode,
    purchaseSalesPersonId:form.purchaseSalesPersonId ? form.purchaseSalesPersonId : userStore.user.userId,
    purchaseSalesPerson:form.purchaseSalesPerson ? form.purchaseSalesPerson : userStore.user.nickName,
    sourceCode:form.sourceCode,
    createUser:form.createUser ? form.createUser : userStore.user.userId,
    createUserName:form.createUserName ? form.createUserName : userStore.user.nickName,
    createTime:form.createTime ? new Date(form.createTime).getTime() : new Date().getTime(),
    customerName:form.customerName,
    countryId:form.countryId,
    countryName:form.countryName,
    provinceId:form.provinceId,
    provinceName:form.provinceName,
    cityId:form.cityId,
    cityName:form.cityName,
    districtId:form.districtId,
    districtName:form.districtName,
    address:form.address,
    contactPerson:form.contactPerson,
    customerAreaCode:form.customerAreaCode,
    customerMobile:form.customerMobile,
    plannedReceivedTime:form.plannedReceivedTime ? new Date(form.plannedReceivedTime).getTime() : new Date().getTime(),
    planDeliveryTime:form.plannedDeliveryTime ? new Date(form.plannedDeliveryTime).getTime() : new Date().getTime(),
    outboundTime:form.outboundTime ? new Date(form.outboundTime).getTime() : new Date().getTime(),
    deliveryType:form.actualDeliveryType,
    deliveryName:form.actualDeliveryName,
    carrier:form.carrier,
    carNumber:form.carNumber,
    poundCode:form.poundCode,
    remark:form.outboundRemark,
    poundAttachmentFiles:form.poundAttachmentFiles,
    fastOutboundProductInfoDTOList:fastOutboundProductInfoDTOList
  }
  if(route.query.type == 'edit'){
    params.deliveryNoticeId = route.query.id
    params.deliveryNoticeCode = form.outboundNoticeCode
  }
  QuickOutboundApi.addFastOutbound(params).then((res)=>{
    ElMessage.success(t("quickOutbound.message.operationSuccess"))
    handleCancel()
  }).finally(()=>{
    saveDraftLoading.value = false
  })
}
// 提交
function handleSubmit() {
  formRef.value.validate((valid) => {
    if (!valid) return;
    if(!form.warehouseOutboundDetailVOList || form.warehouseOutboundDetailVOList.length ==0){
      return  ElMessage.error(t('quickOutbound.message.noProduct'));
    }
    if((!form.totalOutboundQty || form.totalOutboundQty == 0) && (!form.totalOutboundWeight || form.totalOutboundWeight == 0)){
      return  ElMessage.error(t('quickOutbound.message.totalWarning'));
    }
    loading.value = true
    let sameArr = []
    form.warehouseOutboundDetailVOList.forEach(item=>{
      let obj = {
        productCode:item.productCode,
        warehouseAreaCode:item.warehouseAreaCode,
        docketCode:item.docketCode
      }
      sameArr.push(obj)
    })
    const uniqueItems = new Map();
    for(let i = 0;i<sameArr.length;i++){
      const key = JSON.stringify(sameArr[i]); // 将对象转换为字符串
      if (!uniqueItems.has(key)) {
        uniqueItems.set(key, sameArr[i]); // 如果尚未存在，则添加到Map中
      }else {
        loading.value = false
        return  ElMessage.error(t('quickOutbound.message.sameProduct'));
      }
    }
    /*let availableArr = form.warehouseOutboundDetailVOList.filter(function (item) {
      return (item.isDiscreteUnit && Number(item.availableQty) < 0) || (!item.isDiscreteUnit && Number(item.availableWeight) < 0)
    })
    if(availableArr.length > 0){
      loading.value = false
      return  ElMessage.error(t('quickOutbound.message.availableTip'));
    }*/
    //提交
    ElMessageBox.confirm(t("quickOutbound.message.submitWarning"), t("common.tipTitle"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }).then(() => {
      let fastOutboundProductInfoDTOList = []
      let arr = form.warehouseOutboundDetailVOList.filter(function (list) {
        return list.isParent
      })
      arr.forEach(item => {
        let fastOutboundProductDetailDTOList = []
        if((item.outboundQty && Number(item.outboundQty) > 0) || (item.outboundWeight && Number(item.outboundWeight) > 0)) {
          let obj = {
            actualPickThisTime: item.outboundQty || 0,
            warehouseAreaActualPickWeight: item.outboundWeight || 0,
            productPackage: item.productPackage,
            warehouseAreaCode: item.warehouseAreaCode,
            warehouseAreaName: item.warehouseAreaName,
            docketCode: item.docketCode,
            salePrice: item.salePrice,
            saleAmount: item.saleAmount,
          }
          fastOutboundProductDetailDTOList.push(obj)
        }
        form.warehouseOutboundDetailVOList.forEach(list=>{
          if(item.productCode == list.productCode && !list.isParent){
            if((list.outboundQty && Number(list.outboundQty) > 0)  || (list.outboundWeight && Number(list.outboundWeight) > 0)) {
              let obj1 = {
                actualPickThisTime: list.outboundQty || 0,
                warehouseAreaActualPickWeight: list.outboundWeight || 0,
                productPackage: list.productPackage,
                warehouseAreaCode: list.warehouseAreaCode,
                warehouseAreaName: list.warehouseAreaName,
                docketCode: list.docketCode,
                salePrice: list.salePrice,
                saleAmount: list.saleAmount,
              }
              fastOutboundProductDetailDTOList.push(obj1)
            }
          }
        })
        item.fastOutboundProductDetailDTOList = fastOutboundProductDetailDTOList
      })
      arr.forEach(item=>{
        if(item.fastOutboundProductDetailDTOList && item.fastOutboundProductDetailDTOList.length > 0) {
          let obj = {
            outboundProductId: item.id,
            productCode: item.productCode,
            productName: item.productName,
            firstCategoryId: item.firstCategoryId,
            firstCategoryName: item.firstCategoryName,
            productSpecs: item.productSpecs,
            productUnitId: item.productUnitId,
            productUnitName: item.productUnitName,
            conversionRelSecondUnitId: item.conversionRelSecondUnitId,
            conversionRelSecondUnitName: item.conversionRelSecondUnitName,
            secondCategoryId: item.secondCategoryId,
            secondCategoryName: item.secondCategoryName,
            thirdCategoryId: item.thirdCategoryId,
            thirdCategoryName: item.thirdCategoryName,
            pricingScheme: item.pricingScheme,
            isDiscreteUnit: item.isDiscreteUnit,
            attributeType:item.attributeType,
            attributeTypeName:item.attributeTypeName,
            productBrandId:item.productBrandId,
            productBrandName:item.productBrandName,
            fastOutboundProductDetailDTOList: item.fastOutboundProductDetailDTOList
          }
          fastOutboundProductInfoDTOList.push(obj)
        }
      })
      let params = {
        isSubmit:1,
        outboundType:form.outboundType,
        orderTheme:form.orderTheme,
        sourceOrderCode:form.sourceOrderCode,
        purchaseSalesPersonId:form.purchaseSalesPersonId ? form.purchaseSalesPersonId : userStore.user.userId,
        purchaseSalesPerson:form.purchaseSalesPerson ? form.purchaseSalesPerson : userStore.user.nickName,
        sourceCode:form.sourceCode,
        createUser:form.createUser ? form.createUser : userStore.user.userId,
        createUserName:form.createUserName ? form.createUserName : userStore.user.nickName,
        createTime:form.createTime ? new Date(form.createTime).getTime() : new Date().getTime(),
        customerName:form.customerName,
        countryId:form.countryId,
        countryName:form.countryName,
        provinceId:form.provinceId,
        provinceName:form.provinceName,
        cityId:form.cityId,
        cityName:form.cityName,
        districtId:form.districtId,
        districtName:form.districtName,
        address:form.address,
        contactPerson:form.contactPerson,
        customerAreaCode:form.customerAreaCode,
        customerMobile:form.customerMobile,
        plannedReceivedTime:form.plannedReceivedTime ? new Date(form.plannedReceivedTime).getTime() : new Date().getTime(),
        planDeliveryTime:form.plannedDeliveryTime ? new Date(form.plannedDeliveryTime).getTime() : new Date().getTime(),
        outboundTime:form.outboundTime ? new Date(form.outboundTime).getTime() : new Date().getTime(),
        deliveryType:form.actualDeliveryType,
        deliveryName:form.actualDeliveryName,
        carrier:form.carrier,
        carNumber:form.carNumber,
        poundCode:form.poundCode,
        remark:form.outboundRemark,
        poundAttachmentFiles:form.poundAttachmentFiles,
        fastOutboundProductInfoDTOList:fastOutboundProductInfoDTOList
      }
      if(route.query.type == 'edit'){
        params.deliveryNoticeId = route.query.id
        params.deliveryNoticeCode = form.outboundNoticeCode
      }
      QuickOutboundApi.addFastOutbound(params).then((res)=>{
        ElMessage.success(t("quickOutbound.message.operationSuccess"))
        handleCancel()
      }).finally(()=>{
        loading.value = false
      })
    }, () => {
      loading.value = false
      ElMessage.info(t("quickOutbound.message.cancelTip"));
    });
  })
}

function queryDetail() {
  formLoading.value = true;
  let params = {
    id: route.query.id,
  };
  QuickOutboundApi.queryFastDetail(params).then((res)=>{
    Object.assign(form,res)
    if (form.countryId) {
      getAreaApi(form.countryId, "province");
    }
    if (form.provinceId) {
      getAreaApi(form.provinceId, "city");
    }
    if (form.cityId) {
      getAreaApi(form.cityId);
    }
    form.warehouseOutboundDetailVOList = []
    if(res.outboundPickingListVOList && res.outboundPickingListVOList.length > 0){
      form.actualDeliveryType = res.outboundPickingListVOList[0].deliveryType
      form.actualDeliveryName = res.outboundPickingListVOList[0].deliveryName
      form.carrier = res.outboundPickingListVOList[0].carrier
      form.carNumber = res.outboundPickingListVOList[0].carNumber
      form.poundCode = res.outboundPickingListVOList[0].poundCode
      form.outboundRemark = res.outboundPickingListVOList[0].remark
      form.totalSaleAmount = res.outboundPickingListVOList[0].totalSaleAmount
      form.outboundTime = res.outboundPickingListVOList[0].outboundTime ? parseDateTime(res.outboundPickingListVOList[0].outboundTime,'dateTime') : null
      if(res.outboundPickingListVOList[0].poundAttachmentFiles){
        form.fileNum = JSON.parse(res.outboundPickingListVOList[0].poundAttachmentFiles).length
        form.poundAttachmentFiles = res.outboundPickingListVOList[0].poundAttachmentFiles
      }
      if(res.outboundPickingListVOList[0].outboundPickingListProductVOList && res.outboundPickingListVOList[0].outboundPickingListProductVOList.length > 0){
        let warehouseOutboundDetailVOList = []
        const promises = res.outboundPickingListVOList[0].outboundPickingListProductVOList.map((a,index) => {
          return new Promise((resolve) => {
            //available
            let params = {
              productCode:a.productCode,
              isDiscreteUnit:a.isDiscreteUnit,
              productUnitName:a.productUnitName,
              conversionRelSecondUnitName:a.conversionRelSecondUnitName
            }
            /*CommonAPI.queryAvailableStockInfoListByReq(params).then((res)=>{
              a.available = res
              a.id = a.outboundProductId
              a.outboundQty = a.actualPickThisTime
              a.outboundWeight = a.warehouseAreaActualPickWeight
              resolve(a);
            })*/
            QuickOutboundApi.queryBillNoListByReq(params).then((res)=>{
              a.productDocketCodeInfoVOList = res.productDocketCodeInfoVOList
              a.id = a.outboundProductId
              a.outboundQty = a.actualPickThisTime
              a.outboundWeight = a.warehouseAreaActualPickWeight
              a.available = res.stockInfo
              resolve(a);
            })
            /*let params = {
              productCode:a.productCode,
              isDiscreteUnit:a.isDiscreteUnit,
            }
            CommonAPI.queryHasStockListByReq(params).then((res)=>{
              a.outWarehouseAreaList = res ? res : []
              a.id = a.outboundProductId
              a.outboundQty = a.actualPickThisTime
              a.outboundWeight = a.warehouseAreaActualPickWeight
              if(a.warehouseAreaCode && a.outWarehouseAreaList.length > 0){
                a.outWarehouseAreaList.forEach(list=>{
                  if(list.areaCode == a.warehouseAreaCode){
                    a.warehouseAreaName = list.areaName
                    a.availableStockQty = list.availableStockQty
                    a.availableStockWeight = list.availableStockWeight
                  }
                })
              }else{
                a.warehouseAreaName = ''
                a.availableStockQty = ''
                a.availableStockWeight = ''
              }
              if(a.availableStockQty && a.outboundQty){
                a.availableQty = (parseFloat(a.availableStockQty) - parseFloat(a.outboundQty)).toFixed(3)
              }else if(a.availableStockQty &&!a.outboundQty){
                a.availableQty = a.availableStockQty
              }
              if(a.availableStockWeight && a.outboundWeight){
                a.availableWeight = (parseFloat(a.availableStockWeight) - parseFloat(a.outboundWeight)).toFixed(3)
              }else if(a.availableStockWeight &&!a.outboundWeight){
                a.availableWeight = a.availableStockWeight
              }
              resolve(a);
            }).catch(()=>{
              formLoading.value = false
            }).finally(()=>{
            })*/
          });
        });
        Promise.all(promises).then((results) => {
          results.forEach((a) => {
            warehouseOutboundDetailVOList.push(a);
          });
          let arr = []
          warehouseOutboundDetailVOList.forEach(list=>{
            let obj = {
              productCode:list.productCode
            }
            arr.push(obj)
          })
          let uniqueArr = [
            ...new Map(arr.map((item: any) => [item.productCode, item])).values(),
          ];
          for(let i = 0; i < uniqueArr.length; i++) {
            let innerArr = warehouseOutboundDetailVOList.filter(function (list) {
              return list.productCode == uniqueArr[i].productCode
            })
            innerArr.forEach((a,index)=> {
              if (index == 0) {
                a.isParent = true
                a.index = i+1
              } else {
                a.isParent = false
              }
              form.warehouseOutboundDetailVOList.push(a)
            })
          }
          formLoading.value = false
        });
      }else {
        formLoading.value = false
      }
    }else {
      formLoading.value = false
    }
  }).catch(()=>{
    formLoading.value = false
  }).finally(()=>{

  })

}

function queryDeliveryMethodList(){
  deliveryTypeList.value = []
  let data = {
    page:1,
    limit:100,
    enableStatus:1
  }
  QuickOutboundApi.queryDeliveryMethodList(data).then((res)=>{
    if(res.records && res.records.length > 0){
      res.records.forEach(item=>{
        let obj = {
          code:item.id,
          name:item.deliveryMethodsCategoryVO.deliveryMethodsCategoryName + '/' + item.methodName,
          methodName:item.methodName
        }
        deliveryTypeList.value.push(obj)
      })
    }else {
      deliveryTypeList.value = []
    }
  })
}
function setName(){
  if(form.actualDeliveryType){
    deliveryTypeList.value.forEach(list=>{
      if(list.code == form.actualDeliveryType){
        form.actualDeliveryName = list.name
      }
    })
  }else{
    form.actualDeliveryName = ''
  }
}
function setPurchaseSalesPerson(){
  if(form.purchaseSalesPersonId){
    personList.value.forEach(list=>{
      if(list.userId == form.purchaseSalesPersonId){
        form.purchaseSalesPerson = list.nickName
      }
    })
  }else{
    form.purchaseSalesPerson = ''
  }
}
function setCreateUserName(){
  if(form.createUser){
    personList.value.forEach(list=>{
      if(list.userId == form.createUser){
        form.createUserName = list.nickName
      }
    })
  }else{
    form.createUserName = ''
  }
}

onMounted(() => {
  queryDeliveryMethodList()
  queryPersonList()
  getAreaList()
  getOutWarehouseAreaEnableList({status:1})
  queryAllCountry()
  if(route.query.type == 'add'){
    form.outboundType = 8
    form.plannedDeliveryTime = parseDateTime(new Date().getTime(),'dateTime')
    form.createTime = parseDateTime(new Date().getTime(),'dateTime')
    form.plannedReceivedTime = parseDateTime(new Date().getTime(),'dateTime')
    form.outboundTime = parseDateTime(new Date().getTime(),'dateTime')
    form.purchaseSalesPersonId = userStore.user.userId
    form.purchaseSalesPerson = userStore.user.nickName
    form.createUser = userStore.user.userId
    form.createUserName = userStore.user.nickName
  }
  if(route.query.type == 'edit'){
    queryDetail();
  }
});
</script>
<style scoped lang="scss">
.addPage {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 13px 21px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 20px 9px 20px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 9px 20px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .mt15px {
    margin-bottom: 15px !important;
  }

}
:deep(.el-dropdown){
  line-height: 23px;
  width: 100%;
}
:deep(.el-dropdown.is-disabled){
  color: #52585f;
}

.dropdown-trigger {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
:deep(.el-dropdown-menu__item.is-selected) {
  color: #409EFF;
  background-color: #f5f7fa;
}
</style>
<style lang="scss">
::v-deep .custom-select .el-select__wrapper {
  background: #f8f9fc !important;
}
  .quickOutbound-address-area-detail-content{
    .el-cascader-node{
      .el-radio{
        display: none;
      }
    }
  }
.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}
</style>
