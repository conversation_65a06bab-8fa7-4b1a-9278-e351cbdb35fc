<template>
  <div class="app-container">
    <div class="addPage">
      <div class="page-title">
        <div @click="handleCancel" class="display_block">
          <el-icon><Back /></el-icon>
          <span v-if="type == 'add'">
            {{ $t("outboundNotice.title.addTitle") }}
          </span>
          <span v-else>
            {{ $t("outboundNotice.title.outboundNoticeCode") }}:{{
              form.outboundNoticeCode
            }}
          </span>
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="112px"
          label-position="right"
        >
          <div class="basicInformation item_content">
            <div class="title">
              {{ $t("outboundNotice.label.basicInformation") }}
            </div>
            <el-row>
              <el-col :span="8">
                <!-- 出库通知单 -->
                <el-form-item
                  :label="$t('outboundNotice.label.outboundNoticeCode')"
                  prop="outboundNoticeCode"
                >
                  <el-input
                    v-model="form.outboundNoticeCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 出库类型 -->
                <el-form-item
                  :label="$t('outboundNotice.label.outboundType')"
                  prop="outboundType"
                >
                  <el-select
                    v-model="form.outboundType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    :disabled="type == 'edit'"
                    @change="outboundTypeChange"
                  >
                    <el-option
                      v-for="(item, index) in outboundTypeList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 客户下单时间 -->
                <el-form-item
                  :label="$t('outboundNotice.label.orderCreateTime')"
                  prop="orderCreateTime"
                >
                  <el-date-picker
                    v-model="form.orderCreateTime"
                    type="datetime"
                    style="width: 100%"
                    :placeholder="$t('common.placeholder.selectTips')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <!-- 采购员 -->
                <el-form-item
                  :label="$t('outboundNotice.label.purchaseSalesPerson')"
                  prop="purchaseSalesPerson"
                >
                  <el-input
                    v-model="form.purchaseSalesPerson"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 提货单号 -->
                <el-form-item
                  :label="$t('outboundNotice.label.sourceOrderCode')"
                  prop="sourceOrderCode"
                >
                  <el-input
                    v-model="form.sourceOrderCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 要求到货时间 -->
                <el-form-item
                  :label="$t('outboundNotice.label.plannedReceivedTime')"
                  prop="plannedReceivedTime"
                >
                  <el-date-picker
                    v-model="form.plannedReceivedTime"
                    type="datetime"
                    style="width: 100%"
                    :placeholder="$t('common.placeholder.selectTips')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <!-- 配送方式 -->
                <el-form-item
                  :label="$t('outboundNotice.label.deliveryType')"
                  prop="deliveryType"
                >
                  <el-select
                    v-model="form.deliveryType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in deliveryTypeList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 供应商/客户 出库类型:1:销售出库（客户）、2:采购退货(供应商)-->
                <span v-if="form.outboundType == 1">
                  <el-form-item
                    :label="$t('outboundNotice.label.customerName')"
                    prop="customerName"
                  >
                    <el-input
                      v-model="form.customerName"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="50"
                      clearable
                    />
                  </el-form-item>
                </span>
                <span v-else>
                  <el-form-item
                    :label="$t('outboundNotice.label.supplierName')"
                    prop="supplierName"
                  >
                    <el-input
                      v-model="form.supplierName"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="50"
                      clearable
                    />
                  </el-form-item>
                </span>
              </el-col>
              <el-col :span="8">
                <!-- 联系人 -->
                <el-form-item
                  :label="$t('outboundNotice.label.contactPerson')"
                  prop="contactPerson"
                >
                  <el-input
                    v-model="form.contactPerson"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-row>
                  <el-col :span="10">
                    <!-- 手机号码 -->
                    <el-form-item
                      :label="$t('outboundNotice.label.customerMobile')"
                      prop="customerAreaCode"
                      class="custom-select"
                    >
                      <el-select
                        v-model="form.customerAreaCode"
                        :placeholder="$t('outboundNotice.label.areaCode')"
                        clearable
                      >
                        <el-option
                          v-for="(item, index) in areaList"
                          :key="index"
                          :label="item.internationalCode"
                          :value="item.internationalCode"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item prop="customerMobile" label-width="0">
                      <el-input
                        v-model="form.customerMobile"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                        maxlength="30"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="8">
                <!-- 国家 -->
                <el-form-item
                  :label="$t('outboundNotice.label.country')"
                  prop="countryId"
                >
                  <el-select
                    v-model="form.countryId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="countryChange"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in countryList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 地区 -->
                <el-form-item
                  :label="$t('outboundNotice.label.area')"
                  prop="areaInfo"
                >
                  <!-- 省 -->
                  <el-select
                    :disabled="!form.countryId"
                    v-model="form.provinceId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleProvinceChange"
                    style="width: 33%"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in provinceList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- 市 -->
                  <el-select
                    v-if="showCityInput"
                    v-model="form.cityId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleCityChange"
                    style="width: 33%"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in cityList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- 区 -->
                  <el-select
                    v-if="showDistrictInput"
                    v-model="form.districtId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleDistrictChange"
                    style="width: 33%"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in districtList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!-- 详细地址 -->
                <el-form-item
                  :label="$t('outboundNotice.label.detailAddress')"
                  prop="address"
                >
                  <el-input
                    v-model="form.address"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="100"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!-- 备注 -->
                <el-form-item
                  :label="$t('outboundNotice.label.remark')"
                  prop="remark"
                >
                  <el-input
                    v-model="form.remark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    type="textarea"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              <div class="flex_style">
                <div>
                  {{ $t("outboundNotice.label.goodsDetails") }}
                </div>
                <div>
                  <el-button
                    type="primary"
                    key="primary"
                    text
                    @click="handleGoodsSelect"
                  >
                    <span class="required_style">
                      {{ $t("outboundNotice.button.goodsAdd") }}
                    </span>
                  </el-button>
                </div>
              </div>
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 商品列表 -->
                <el-form-item label-width="0" prop="warehouseIds">
                  <el-table
                    :data="form.warehouseOutboundDetailVOList"
                    v-loading="formLoading"
                    stripe
                    highlight-current-row
                    v-if="
                      form.warehouseOutboundDetailVOList &&
                      form.warehouseOutboundDetailVOList.length > 0
                    "
                  >
                    <el-table-column
                      type="index"
                      :label="$t('common.sort')"
                      width="60"
                      align="center"
                    />
                    <el-table-column
                      :label="$t('outboundNotice.label.goodsInfor')"
                      min-width="150"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        <div class="product-code">
                          {{ $t("outboundNotice.label.productCode") }}：
                          {{ scope.row.productCode }}
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('outboundNotice.label.goodsCategory')"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{ scope.row.firstCategoryName }}/
                        {{ scope.row.secondCategoryName }}/
                        {{ scope.row.thirdCategoryName }}
                      </template>
                    </el-table-column>
                    <!-- 规格 -->
                    <el-table-column
                      :label="$t('outboundNotice.label.productSpecs')"
                      prop="productSpecs"
                      min-width="100"
                    />
                    <!-- 计划数量 -->
                    <el-table-column
                      prop="planProductQty"
                      min-width="130"
                      :label="'*' + $t('outboundNotice.label.productPlanQty')"
                    >
                      <template #default="scope">
                        <el-form-item
                          class="mt15px"
                          label-width="0px"
                          :prop="
                            'warehouseOutboundDetailVOList.' +
                            scope.$index +
                            '.planProductQty'
                          "
                          :rules="
                            goodsDialog.visible
                              ? []
                              : [
                                  {
                                    required: true,
                                    message: t(
                                      'outboundNotice.rules.productPlanQty'
                                    ),
                                    trigger: 'blur',
                                  },
                                  {
                                    pattern:
                                      /(^[1-9](\d{0,3})$)/,
                                    message: t(
                                      'outboundNotice.rules.productPlanQtyFomart'
                                    ),
                                    trigger: ['blur', 'change'],
                                  },
                                ]
                          "
                        >
                          <el-input
                            v-model="scope.row.planProductQty"
                            :placeholder="$t('common.placeholder.inputTips')"
                            clearable
                            @change="changeWeight(scope.$index,scope.row)"
                          >
                            <template #append>
                              {{ scope.row.productUnitName }}
                            </template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 计划重量 -->
                    <el-table-column
                      prop="planProductWeight"
                      min-width="130"
                      :label="'*' + $t('outboundNotice.label.productPlanWeight')"
                    >
                      <template #default="scope">
                        <el-form-item
                          class="mt15px"
                          label-width="0px"
                          :prop="
                            'warehouseOutboundDetailVOList.' +
                            scope.$index +
                            '.planProductWeight'
                          "
                          :rules="
                            goodsDialog.visible
                              ? []
                              : [
                                  {
                                    required: true,
                                    message: t(
                                      'outboundNotice.rules.productPlanWeight'
                                    ),
                                    trigger: 'blur',
                                  },
                                  {
                                    pattern:
                                      /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                                    message: t(
                                      'outboundNotice.rules.productPlanWeightFomart'
                                    ),
                                    trigger: ['blur', 'change'],
                                  },
                                ]
                          "
                        >
                          <el-input
                            v-model="scope.row.planProductWeight"
                            :placeholder="$t('common.placeholder.inputTips')"
                            clearable
                          >
                            <template #append>kg</template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')">
                      <template #default="scope">
                        <el-button
                          type="danger"
                          size="small"
                          link
                          @click="handleDelete(scope.$index)"
                        >
                          {{ $t("common.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("outboundNotice.button.cancel") }}
          </el-button>
          <el-button
            type="primary"
            plain
            :loading="saveDraftLoading"
            @click="handleSubmit(false)"
          >
            {{ $t("outboundNotice.button.saveDraft") }}
          </el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit(true)"
          >
            {{ $t("outboundNotice.button.submit") }}
          </el-button>
        </div>
      </div>
    </div>

    <AddGoods
      ref="addGoodsRef"
      v-model:visible="goodsDialog.visible"
      @on-submit="onSubmitGoods"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "AddOutboundNotice",
  inheritAttrs: false,
});
import CommonAPI from "@/modules/wms/api/common";
import AddGoods from "./components/addProduct.vue";
import OutboundNoticeAPI, {
  addFormData,
} from "@/modules/wms/api/outboundNotice";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const type = route.query.type;
const outboundTypeList = ref([
  {
    code: 1,
    name: t("outboundNotice.outboundTypeList.procurementOutbound"),
  },
  {
    code: 2,
    name: t("outboundNotice.outboundTypeList.returnOutbound"),
  },
]);
const deliveryTypeList = ref([
  {
    code: 1,
    name: t("outboundNotice.label.delivery"),
  },
  {
    code: 2,
    name: t("outboundNotice.label.selfPickup"),
  },
]);
const areaList = ref([]);
const countryList = ref([]);
const provinceList = ref([]);
const cityList = ref([]);
const districtList = ref([]);

const isEdit = route.query.isEdit;
const addGoodsRef = ref();
const formRef = ref(ElForm);

const loading = ref(false);
const saveDraftLoading = ref(false);
const formLoading = ref(false);

const showCityInput = ref(false);
const showDistrictInput = ref(false);

const goodsDialog = reactive({
  visible: false,
});

// 角色表单
const form = reactive<addFormData>({
  id: "",
  warehouseOutboundDetailVOList: [],
  areaInfo: [],
  customerAreaCode: "+86",
});

const rules = reactive({
  outboundType: [
    {
      required: true,
      message: t("outboundNotice.rules.outboundTypeTip"),
      trigger: "change",
    },
  ],
  plannedReceivedTime: [
    {
      required: true,
      message: t("outboundNotice.rules.plannedReceivedTimeTip"),
      trigger: "change",
    },
  ],
  customerName: [
    {
      required: true,
      message: t("outboundNotice.rules.customerNameTip"),
      trigger: "blur",
    },
  ],
  supplierName: [
    {
      required: true,
      message: t("outboundNotice.rules.supplierNameTip"),
      trigger: "blur",
    },
  ],
  contactPerson: [
    {
      required: true,
      message: t("outboundNotice.rules.contactPersonTip"),
      trigger: "blur",
    },
  ],
  deliveryType: [
    {
      required: true,
      message: t("outboundNotice.rules.deliveryTypeTip"),
      trigger: "change",
    },
  ],
  customerAreaCode: [
    {
      required: true,
      message: t("outboundNotice.rules.customerAreaCodeTip"),
      trigger: "change",
    },
  ],
  customerMobile: [
    {
      required: true,
      message: t("outboundNotice.rules.customerMobileTip"),
      trigger: "blur",
    },
  ],
  countryId: [
    {
      required: true,
      message: t("outboundNotice.rules.countryTip"),
      trigger: "change",
    },
  ],
  areaInfo: [
    {
      required: true,
      message: t("outboundNotice.rules.areaTip"),
      trigger: "blur",
    },
  ],
  address: [
    {
      required: true,
      message: t("outboundNotice.rules.addressTip"),
      trigger: "blur",
    },
  ],
});

// 获取区号
function getAreaList() {
  OutboundNoticeAPI.getAllCountry()
    .then((data: any) => {
      areaList.value = data;
    })
    .finally(() => {});
}

/** 获取国家列表 */
function queryAllCountry() {
  const queryParams = {
    pid: "0",
  };
  CommonAPI.getAreaList(queryParams)
    .then((data: any) => {
      countryList.value = data;
    })
    .finally(() => {});
}

function countryChange(val: any) {
  if (type == "add") {
    form.provinceId = "";
    provinceList.value = [];
    form.cityId = "";
    cityList.value = [];
    form.districtId = "";
    districtList.value = [];
    showCityInput.value = false;
    showDistrictInput.value = false;
    form.areaInfo = [];
    form.provinceName = "";
    form.cityName = "";
    form.districtName = "";
    if (form.countryId) {
      let data: any = countryList.value.find(
        (item: any) => item.id === form.countryId
      );
      if (form.countryId) {
        form.countryName = data.shortName ? data.shortName : "";
      } else {
        form.countryName = "";
      }
      if (val) {
        getAreaApi(val, "province");
      }
    }
  }
}

function handleProvinceChange(val: any) {
  if (type == "add") {
    form.cityId = "";
    cityList.value = [];
    form.districtId = "";
    districtList.value = [];
    showCityInput.value = false;
    showDistrictInput.value = false;
    form.areaInfo = [];
    let data: any = provinceList.value.find(
      (item: any) => item.id === form.provinceId
    );
    if (form.provinceId) {
      form.provinceName = data.shortName ? data.shortName : "";
    } else {
      form.provinceName = "";
    }
    if (val) {
      getAreaApi(val, "city");
    }
  }
}

function handleCityChange(val: any) {
  if (type == "add") {
    form.districtId = "";
    districtList.value = [];
    showDistrictInput.value = false;
    form.areaInfo[1] = "";
    let data: any = cityList.value.find((item: any) => item.id === form.cityId);
    if (form.cityId) {
      form.cityName = data.shortName ? data.shortName : "";
    } else {
      form.cityName = "";
    }
    if (val) {
      getAreaApi(val, "district");
    }
  }
}

function handleDistrictChange(val: any) {
  let data: any = districtList.value.find(
    (item: any) => item.id === form.districtId
  );
  if (form.districtId) {
    form.districtName = data.shortName ? data.shortName : "";
  } else {
    form.districtName = "";
  }
}

function getAreaApi(val: any, tab?: any) {
  let params = {
    pid: val,
  };
  CommonAPI.getAreaList(params)
    .then((data: any) => {
      if (tab == "province") {
        provinceList.value = data;
        form.areaInfo[0] = form.provinceId;
      } else if (tab == "city") {
        if (data.length > 0) {
          cityList.value = data;
          showCityInput.value = true;
          form.areaInfo[1] = form.cityId;
        } else {
          showCityInput.value = false;
        }
      } else {
        if (data.length > 0) {
          showDistrictInput.value = true;
          districtList.value = data;
          form.areaInfo[2] = form.districtId;
        } else {
          showDistrictInput.value = false;
        }
      }
    })
    .finally(() => {});
}

function checkAddress() {
  if (form.provinceId && showCityInput.value == false) {
    form.areaInfo[0] = form.provinceId;
  } else if (
    form.provinceId &&
    form.cityId &&
    showDistrictInput.value == false
  ) {
    form.areaInfo[0] = form.provinceId;
    form.areaInfo[1] = form.cityId;
  } else if (form.provinceId && form.cityId && form.districtId) {
    form.areaInfo[0] = form.provinceId;
    form.areaInfo[1] = form.cityId;
    form.areaInfo[2] = form.districtId;
  } else {
    form.areaInfo = [];
  }
}

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.push({
    path: "/wms/outboundManagement/outboundNotice",
  });
};

function handleGoodsSelect() {
  goodsDialog.visible = true;
  addGoodsRef.value.queryGoodList();
  addGoodsRef.value.queryManagerCategoryList();
}
function changeWeight(index,row) {
  if(row.planProductQty && row.weight){
    form.warehouseOutboundDetailVOList[index].planProductWeight = (parseInt(row.planProductQty) * parseFloat(row.weight)).toFixed(3)
  }
}
function onSubmitGoods(data: any) {
  let arr = data.concat(form.warehouseOutboundDetailVOList);
  let uniqueArr = [
    ...new Map(arr.map((item: any) => [item.productCode, item])).values(),
  ];
  form.warehouseOutboundDetailVOList = uniqueArr;
}
function handleDelete(index?: number) {
  form.warehouseOutboundDetailVOList.splice(index, 1);
}

function outboundTypeChange() {
  form.customerName = "";
  form.supplierName = "";
}

function addOutboundNotice(params: any) {
  OutboundNoticeAPI.addOutboundNotice(params)
    .then((data: any) => {
      ElMessage.success(t("outboundNotice.message.addSucess"));
      loading.value = false;
      saveDraftLoading.value = false;
      handleCancel();
    })
    .finally(() => {
      loading.value = false;
      saveDraftLoading.value = false;
    });
}

function editOutboundNotice(params: any) {
  OutboundNoticeAPI.editOutboundNotice(params)
    .then((data: any) => {
      ElMessage.success(t("outboundNotice.message.editSucess"));
      loading.value = false;
      saveDraftLoading.value = false;
      handleCancel();
    })
    .finally(() => {
      loading.value = false;
      saveDraftLoading.value = false;
    });
}

// 提交
function handleSubmit(flag: boolean) {
  if (
    form.warehouseOutboundDetailVOList &&
    form.warehouseOutboundDetailVOList.length == 0
  ) {
    return ElMessage.error(t("outboundNotice.message.addOrEditGoodsTips"));
  }
  checkAddress();
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    if (flag == true) {
      loading.value = true;
    } else {
      saveDraftLoading.value = true;
    }

    let params: any = {
      ...form,
    };
    params.sourceSystem = "WMS";
    params.approveFlag = flag;
    params.orderCreateTime = new Date(params.orderCreateTime).getTime();
    params.plannedReceivedTime = new Date(params.plannedReceivedTime).getTime();
    delete params.areaInfo;
    if (type == "add") {
      delete params.id;
      if (flag == true) {
        //提交
        ElMessageBox.confirm(
          t("outboundNotice.message.submitTips"),
          t("common.tipTitle"),
          {
            confirmButtonText: t("common.confirm"),
            cancelButtonText: t("common.cancel"),
            type: "warning",
          }
        ).then(
          () => {
            addOutboundNotice(params);
          },
          () => {
            loading.value = false;
            saveDraftLoading.value = false;
            ElMessage.info(t("outboundNotice.message.submitCancel"));
          }
        );
      } else {
        addOutboundNotice(params);
      }
    } else {
      if (flag == true) {
        //提交
        ElMessageBox.confirm(
          t("outboundNotice.message.submitTips"),
          t("common.tipTitle"),
          {
            confirmButtonText: t("common.confirm"),
            cancelButtonText: t("common.cancel"),
            type: "warning",
          }
        ).then(
          () => {
            editOutboundNotice(params);
          },
          () => {
            loading.value = false;
            saveDraftLoading.value = false;
            ElMessage.info(t("outboundNotice.message.submitCancel"));
          }
        );
      } else {
        editOutboundNotice(params);
      }
    }
  });
}

function queryDetail() {
  formLoading.value = true;
  let params = {
    id: route.query.id,
  };
  OutboundNoticeAPI.queryDetail(params)
    .then((data: any) => {
      Object.assign(form, data);
      countryChange(data.countryId);
      form.areaInfo[0] = form.provinceId ? form.provinceId : "";
      form.areaInfo[1] = form.cityId ? form.cityId : "";
      form.areaInfo[2] = form.districtId ? form.districtId : "";
      if (form.countryId) {
        getAreaApi(form.countryId, "province");
      }
      if (form.provinceId) {
        getAreaApi(form.provinceId, "city");
      }
      if (form.cityId) {
        getAreaApi(form.cityId);
      }
    })
    .finally(() => {
      formLoading.value = false;
    });
}

onMounted(() => {
  queryAllCountry();
  getAreaList();
  if (type == "edit" || type == "details") {
    queryDetail();
  }
});
</script>
<style scoped lang="scss">
.addPage {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 13px 21px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 20px 9px 20px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 9px 20px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .mt15px {
    margin-bottom: 15px !important;
  }
}
</style>
<style scoped>
::v-deep .custom-select .el-select__wrapper {
  background: #f8f9fc !important;
}
</style>
