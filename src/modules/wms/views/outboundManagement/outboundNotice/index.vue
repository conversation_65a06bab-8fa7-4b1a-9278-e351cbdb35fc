<template>
  <div class="app-container">
    <div class="outboundNotice">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form
            ref="queryFormRef"
            :model="queryParams"
            :inline="true"
            label-width="96px"
          >
            <!-- 出库通知单 -->
            <el-form-item
              prop="outboundNoticeCode"
              :label="$t('outboundNotice.label.outboundNoticeCode')"
            >
              <el-input
                v-model="queryParams.outboundNoticeCode"
                :placeholder="$t('outboundNotice.placeholder.inputLimtTips')"
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <!-- 出库类型 -->
            <el-form-item
              prop="outboundType"
              :label="$t('outboundNotice.label.outboundType')"
            >
              <el-select
                v-model="queryParams.outboundType"
                multiple
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                class="!w-[256px]"
              >
                <el-option
                  v-for="item in outboundTypeList"
                  :key="item.outboundTypeId"
                  :label="item.outboundTypeName"
                  :value="item.outboundTypeId"
                />
              </el-select>
            </el-form-item>
            <!-- 状态 -->
            <el-form-item
              :label="$t('outboundNotice.label.status')"
              prop="outboundNoticeStatus"
            >
              <el-select
                v-model="queryParams.outboundNoticeStatus"
                multiple
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                class="!w-[256px]"
              >
                <el-option
                  v-for="item in outboundNoticeStatusList"
                  :key="item.statusId"
                  :label="item.statusName"
                  :value="item.statusId"
                />
              </el-select>
            </el-form-item>
            <!-- 提货单号 -->
            <el-form-item
              prop="sourceOrderCode"
              :label="$t('outboundNotice.label.sourceOrderCode')"
            >
              <el-input
                v-model="queryParams.sourceOrderCode"
                :placeholder="$t('outboundNotice.placeholder.inputLimtTips')"
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <!-- 审核状态 -->
            <el-form-item
              :label="$t('outboundNotice.label.approveStatus')"
              prop="approveStatus"
            >
              <el-select
                v-model="queryParams.approveStatus"
                multiple
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                class="!w-[256px]"
              >
                <el-option
                  v-for="item in approveStatusList"
                  :key="item.statusId"
                  :label="item.statusName"
                  :value="item.statusId"
                />
              </el-select>
            </el-form-item>
            <!-- 时间 -->
            <el-form-item prop="dateRange">
              <el-select
                v-model="queryParams.dateType"
                :placeholder="$t('common.placeholder.selectTips')"
                class="!w-[200px] ml5px multipleSelect"
              >
                <el-option
                  v-for="item in dateTypeList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
              <el-date-picker
                :editable="false"
                class="!w-[370px]"
                v-model="dateRange"
                type="datetimerange"
                :range-separator="$t('outboundNotice.label.to')"
                :start-placeholder="$t('outboundNotice.label.startTime')"
                :end-placeholder="$t('outboundNotice.label.endTime')"
                :default-time="defaultTime"
                :placeholder="$t('common.placeholder.selectTips')"
              />
              <span
                class="ml16px mr14px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="handleChangeDateRange(1)"
              >
                {{ $t("outboundNotice.label.today") }}
              </span>
              <span
                class="mr14px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="handleChangeDateRange(2)"
              >
                {{ $t("outboundNotice.label.yesterday") }}
              </span>
              <span
                class="mr16px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="handleChangeDateRange(3)"
              >
                {{ $t("outboundNotice.label.weekday") }}
              </span>
            </el-form-item>
            <el-form-item>
              <el-button
                v-hasPerm="['wms:outboundManagement:outboundNotice:search']"
                type="primary"
                @click="handleQuery"
              >
                {{ $t("common.search") }}
              </el-button>
              <el-button
                v-hasPerm="['wms:outboundManagement:outboundNotice:reset']"
                @click="handleResetQuery"
              >
                {{ $t("common.reset") }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <el-card class="content-card">
        <div class="action-bar">
          <el-button
            v-hasPerm="['wms:outboundManagement:outboundNotice:add']"
            type="primary"
            @click="addOutboundNotice(null, 'add')"
          >
            {{ $t("outboundNotice.button.addOutboundNotice") }}
          </el-button>
          <el-button
            type="primary"
            plain
            @click="handleApprove"
            v-hasPerm="['wms:outboundManagement:outboundNotice:approve']"
          >
            {{ $t("outboundNotice.button.approve") }}
          </el-button>
          <el-button
            type="primary"
            plain
            @click="handleExApprove"
            v-hasPerm="['wms:outboundManagement:outboundNotice:exApprove']"
          >
            {{ $t("outboundNotice.button.exApprove") }}
          </el-button>
        </div>

        <el-table
          v-loading="loading"
          :data="outboundNoticeList"
          @selection-change="handleSelectionChange"
          highlight-current-row
          stripe
        >
          <template #empty>
            <Empty />
          </template>
          <el-table-column fixed="left" type="selection" width="55" align="center" />
          <el-table-column fixed="left" type="index" :label="$t('common.sort')" width="60" />
          <!-- 出库通知单 -->
          <el-table-column
            :label="$t('outboundNotice.label.outboundNoticeCode')"
            prop="outboundNoticeCode"
            show-overflow-tooltip
            width="160"
          />
          <!-- 提货通知单 -->
          <el-table-column
            :label="$t('outboundNotice.label.sourceOrderCode')"
            prop="sourceOrderCode"
            show-overflow-tooltip
            width="160"
          />
          <!-- 计划发货时间 -->
          <el-table-column
            :label="$t('outboundNotice.label.plannedDeliveryTime')"
            prop="plannedDeliveryTime"
            show-overflow-tooltip
            width="160"
          >
            <template #default="scope">
              <span v-if="scope.row.plannedDeliveryTime">
                {{
                  parseTime(
                    scope.row.plannedDeliveryTime,
                    "{y}-{m}-{d} {h}:{i}:{s}"
                  )
                }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 要求到货时间 -->
          <el-table-column
            :label="$t('outboundNotice.label.plannedReceivedTime')"
            prop="plannedReceivedTime"
            show-overflow-tooltip
            width="160"
          >
            <template #default="scope">
              <span v-if="scope.row.plannedReceivedTime">
                {{
                  parseTime(
                    scope.row.plannedReceivedTime,
                    "{y}-{m}-{d} {h}:{i}:{s}"
                  )
                }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!--计划量-->
          <el-table-column :label="$t('outboundNotice.label.plannedQty')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <div>{{$t('outboundNotice.label.count')}}：<span style="color: #90979E ">{{scope.row.productQty == null ? '-' : scope.row.productQty}}</span></div>
              <div>{{$t('outboundNotice.label.plannedTotalQty')}}：<span style="color: #90979E ">{{scope.row.totalPlanProductQty == null ? '-' : scope.row.totalPlanProductQty}}</span></div>
              <div>{{$t('outboundNotice.label.plannedTotalWeight')}}：<span style="color: #90979E ">{{scope.row.totalPlanProductWeight == null ? '-' : scope.row.totalPlanProductWeight}}</span></div>
            </template>
          </el-table-column>
          <!-- 审核人 -->
          <el-table-column :label="$t('outboundNotice.label.approveUserName')" min-width="220" show-overflow-tooltip>
            <template #default="scope">
              <div>{{$t('outboundNotice.label.approveStatus')}}：
                <span style="color: #90979E " v-if="scope.row.approveStatus == 0">
                  {{ $t("outboundNotice.approveStatusList.Unaudited") }}
                </span>
                <span style="color: #90979E " v-else-if="scope.row.approveStatus == 1">
                  {{ $t("outboundNotice.approveStatusList.success") }}
                </span>
                <span style="color: #90979E " v-else-if="scope.row.approveStatus == 2">
                  {{ $t("outboundNotice.approveStatusList.fail") }}
                </span>
                <span style="color: #90979E " v-else>-</span>
              </div>
              <div>{{$t('outboundNotice.label.approveUserName')}}：<span style="color: #90979E ">{{scope.row.approveUserName?scope.row.approveUserName : '-'}}</span></div>
              <div>{{$t('outboundNotice.label.approveTime')}}：<span style="color: #90979E ">{{scope.row.approveTime?parseTime(scope.row.approveTime, "{y}-{m}-{d} {h}:{i}:{s}") : '-'}}</span></div>
            </template>
          </el-table-column>
          <!-- 配送方式 -->
          <el-table-column
            :label="$t('outboundNotice.label.deliveryType')"
            prop="deliveryType"
            show-overflow-tooltip
          >
            <template #default="scope">
              <!-- 配送类型:1:配送、2:自提 -->
              <span v-if="scope.row.deliveryType">
                {{
                  scope.row.deliveryType == 1
                    ? $t("outboundNotice.label.delivery")
                    : $t("outboundNotice.label.selfPickup")
                }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 销售出库--客户  采购退货--供应商 -->
          <!-- 客户 -->
          <el-table-column
            :label="$t('outboundNotice.label.customerName')"
            prop="customerName"
            show-overflow-tooltip
            width="120"
          />
          <!-- 供应商  -->
          <el-table-column
            :label="$t('outboundNotice.label.supplierName')"
            prop="supplierName"
            show-overflow-tooltip
            width="120"
          />
          <!-- 地址 -->
          <el-table-column
            :label="$t('outboundNotice.label.address')"
            width="160"
          >
            <template #default="scope">
              <span class="encryptBox">
                <span v-if="scope.row.addressShow">
                  {{ scope.row.addressFormat }}
                  <el-icon
                    v-if="scope.row.fullAddress"
                    @click="
                      scope.row.addressShow ? getRealAddress(scope.$index) : ''
                    "
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:outboundNotice:eye']"
                  >
                    <component :is="scope.row.addressShow ? 'View' : ''" />
                  </el-icon>
                </span>
                <span v-else>
                  {{ scope.row.fullAddress ? scope.row.fullAddress : "-" }}
                </span>
              </span>
            </template>
          </el-table-column>
          <!-- 联系人 -->
          <el-table-column :label="$t('outboundNotice.label.contactPerson')">
            <template #default="scope">
              {{
              scope.row.nameShow
              ? scope.row.contactPerson
              : encryptName(scope.row.contactPerson)
              }}
              <el-icon
                v-if="scope.row.contactPerson"
                @click="
                  scope.row.contactPerson ? getRealName(scope.$index) : ''
                "
                class="encryptBox-icon"
                color="#762ADB "
                size="16"
                v-hasPermEye="['wms:outboundManagement:outboundNotice:eye']"
              >
                <component :is="scope.row.nameShow ? '' : 'View'" />
              </el-icon>
            </template>
          </el-table-column>
          <!-- 联系电话 -->
          <el-table-column
            :label="$t('outboundNotice.label.customerMobile')"
            width="180"
          >
            <template #default="scope">
              <span class="encryptBox">
                <span v-if="scope.row.customerMobile">
                  {{ scope.row.customerAreaCode + " " }}
                </span>
                <span
                  v-if="
                    scope.row.customerMobile &&
                    scope.row.customerMobile.length <= 4
                  "
                >
                  {{ scope.row.customerMobile }}
                </span>
                <span
                  v-else-if="
                    scope.row.customerMobile &&
                    scope.row.customerMobile.length > 4
                  "
                >
                  {{ scope.row.customerMobile }}
                  <el-icon
                    v-if="scope.row.customerMobile"
                    @click="
                      scope.row.mobilePhoneShow
                        ? getRealPhone(scope.row.id, scope.$index)
                        : ''
                    "
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:outboundNotice:eye']"
                  >
                    <component :is="scope.row.mobilePhoneShow ? 'View' : ''" />
                  </el-icon>
                </span>
                <span v-else>-</span>
              </span>
            </template>
          </el-table-column>
          <!-- 客户下单时间 -->
          <el-table-column
            :label="$t('outboundNotice.label.orderCreateTime')"
            prop="orderCreateTime"
            show-overflow-tooltip
            width="180"
          >
            <template #default="scope">
              <span v-if="scope.row.orderCreateTime">
                {{
                  parseTime(
                    scope.row.orderCreateTime,
                    "{y}-{m}-{d} {h}:{i}:{s}"
                  )
                }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 采购/销售员 -->
          <el-table-column
            :label="$t('outboundNotice.label.purchaseSalesPerson')"
            prop="purchaseSalesPerson"
            show-overflow-tooltip
            width="130"
          />
          <!-- 出库类型:1:采购出库、2:退货出库 -->
          <el-table-column
            :label="$t('outboundNotice.label.outboundType')"
            prop="outboundType"
            show-overflow-tooltip
            width="130"
          >
            <template #default="scope">
              <span v-if="scope.row.outboundType == 1">
                {{ $t("outboundNotice.outboundTypeList.procurementOutbound") }}
              </span>
              <span v-else-if="scope.row.outboundType == 2">
                {{ $t("outboundNotice.outboundTypeList.returnOutbound") }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 来源-->
          <el-table-column
            :label="$t('outboundNotice.label.source')"
            prop="source"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span v-if="scope.row.source == 1">
                {{ $t("outboundNotice.sourceList.creat") }}
              </span>
              <span v-else-if="scope.row.source == 2">
                {{ $t("outboundNotice.sourceList.synchronous") }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 来源系统 -->
          <el-table-column
            :label="$t('outboundNotice.label.sourceSystem')"
            prop="sourceSystem"
            show-overflow-tooltip
          />
          <!-- 创建人 -->
          <el-table-column
            :label="$t('outboundNotice.label.createUserName')"
            prop="createUserName"
            show-overflow-tooltip
            width="120"
          />
          <!-- 创建时间 -->
          <el-table-column
            :label="$t('outboundNotice.label.createTime')"
            prop="createTime"
            show-overflow-tooltip
            width="180"
          >
            <template #default="scope">
              <span v-if="scope.row.createTime">
                {{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 最后操作人 -->
          <el-table-column
            :label="$t('outboundNotice.label.updateUserName')"
            prop="updateUserName"
            show-overflow-tooltip
            width="120"
          />
          <!-- 操作时间 -->
          <el-table-column
            :label="$t('outboundNotice.label.updateTime')"
            prop="updateTime"
            show-overflow-tooltip
            width="180"
          >
            <template #default="scope">
              <span v-if="scope.row.updateTime">
                {{ parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 状态 -->
          <el-table-column
            :label="$t('outboundNotice.label.status')"
            prop="outboundNoticeStatus"
            show-overflow-tooltip
            width="120"
            fixed="right"
          >
            <template #default="scope">
              <div class="purchase">
                <!-- 	状态:0:草稿、1:初始 2拣货中 3 完成 4 取消 -->
                <div
                  v-if="scope.row.outboundNoticeStatus == 0"
                  type="success"
                  class="purchase-status purchase-status-color0"
                >
                  {{ t("outboundNotice.outboundNoticeStatusList.draft") }}
                </div>
                <div
                  v-else-if="scope.row.outboundNoticeStatus == 1"
                  type="info"
                  class="purchase-status purchase-status-color1"
                >
                  {{ t("outboundNotice.outboundNoticeStatusList.initial") }}
                </div>
                <div
                  v-else-if="scope.row.outboundNoticeStatus == 2"
                  type="info"
                  class="purchase-status purchase-status-color2"
                >
                  {{ t("outboundNotice.outboundNoticeStatusList.picking") }}
                </div>
                <div
                  v-else-if="scope.row.outboundNoticeStatus == 3"
                  type="info"
                  class="purchase-status purchase-status-color3"
                >
                  {{ t("outboundNotice.outboundNoticeStatusList.finish") }}
                </div>
                <div
                  v-else-if="scope.row.outboundNoticeStatus == 4"
                  type="info"
                  class="purchase-status purchase-status-color4"
                >
                  {{ t("outboundNotice.outboundNoticeStatusList.cancel") }}
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column
            fixed="right"
            :label="$t('common.handle')"
            width="240"
          >
            <template #default="scope">
              <el-button
                v-hasPerm="['wms:outboundManagement:outboundNotice:weightEdit']"
                v-if="
                  scope.row.outboundNoticeStatus == 1 && scope.row.approveStatus == 2
                "
                type="primary"
                link
                @click="weightEdit(scope.row.id)"
              >
                {{ $t("outboundNotice.button.weightEdit") }}
              </el-button>
              <el-button
                v-hasPerm="['wms:outboundManagement:outboundNotice:update']"
                v-if="
                  scope.row.outboundNoticeStatus == 0 && scope.row.source == 1
                "
                type="primary"
                link
                @click="addOutboundNotice(scope.row.id, 'edit')"
              >
                {{ $t("common.edit") }}
              </el-button>
              <el-button
                v-hasPerm="['wms:outboundManagement:outboundNotice:detail']"
                v-if="
                  scope.row.outboundNoticeStatus == 1 || scope.row.outboundNoticeStatus == 2 || scope.row.outboundNoticeStatus == 3
                "
                type="primary"
                link
                @click="detailOutboundNotice(scope.row.id)"
              >
                {{ $t("common.detailBtn") }}
              </el-button>
              <el-button
                v-hasPerm="['wms:outboundManagement:outboundNotice:cancel']"
                v-if="
                  scope.row.outboundNoticeStatus == 1 && scope.row.source == 1
                "
                type="danger"
                link
                @click="handleCancel(scope.row.id)"
              >
                {{ $t("common.cancel") }}
              </el-button>
              <el-button
                v-hasPerm="['wms:outboundManagement:outboundNotice:delete']"
                v-if="
                  scope.row.outboundNoticeStatus == 0 && scope.row.source == 1
                "
                type="danger"
                link
                @click="handleDelete(scope.row.id)"
              >
                {{ $t("common.delete") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.limit"
            @pagination="handleQuery"
          />
        </div>
      </el-card>
    </div>
    <!-- 审核弹框 -->
    <el-dialog :title="$t('outboundNotice.title.approve')" v-model="approveDialog" class="approve-popup" width="560" :close-on-click-modal="false" @close="close">
      <div style="padding: 0px 30px 16px 30px;">
        <div style="margin-bottom: 40px;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 16px;color: #151719;line-height: 22px;font-style: normal;">{{$t('outboundNotice.message.haveTip')}}<span style="color: #762ADB;">{{multipleSelection.length}}</span>{{$t('outboundNotice.message.approveTips')}}</div>
        <div>
          <span style="color: #f56c6c">*</span>
          <span style="margin:0px 6px">{{$t('outboundNotice.label.plannedDeliveryTime')}}</span>
          <el-date-picker
            v-model="plannedDeliveryDate"
            type="date"
            @change="setDate"
            :placeholder="$t('outboundNotice.placeholder.defaultNowDate')">
          </el-date-picker>
        </div>
        <div class="time-button">
          <el-time-picker
            v-model="plannedDeliveryTime"
            format="HH:mm"
            value-format="HH:mm"
            :placeholder="$t('outboundNotice.placeholder.chooseTime')">
          </el-time-picker>
        </div>
        <div class="time-button">
          <el-button style="width: 10%" @click="setTime(9)">9:00</el-button>
          <el-button style="width: 10%" @click="setTime(12)">12:00</el-button>
          <el-button style="width: 10%" @click="setTime(14)">14:00</el-button>
          <el-button style="width: 10%" @click="setTime(18)">18:00</el-button>
          <el-button style="width: 10%" @click="setTime(21)">21:00</el-button>
          <el-button style="width: 18%" @click="setTime(92)">明天9:00</el-button>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer" style="padding: 12px 0px;margin-right: 30px">
          <el-button @click="closeApproveDialog">
            {{ $t("outboundNotice.button.cancel") }}
          </el-button>
          <el-button type="primary" @click="submitApprove" :loading="loading">{{ $t("common.confirm") }}</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 审核提示弹框 -->
    <el-dialog v-model="dialogVisible" width="500">
      <div>
        {{ $t("outboundNotice.label.success") }}{{ approveResult.successQty
        }}{{ $t("outboundNotice.label.approveResultUnit") }}
      </div>
      <div>
        {{ $t("outboundNotice.label.fail") }}{{ approveResult.failQty
        }}{{ $t("outboundNotice.label.approveResultUnit") }}
      </div>
      <div v-if="approveResult.failQty">
        {{ $t("outboundNotice.label.failReason") }}：{{
        approveResult.failReason.join(";")
        }}
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            {{ $t("outboundNotice.button.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 重量调整弹框 -->
    <el-drawer title="调整最小发货量" v-model="weightEditDialog" :close-on-click-modal="false" size="1000px" @close="close">
      <div>
        <el-form :model="form" ref="formRef" label-width="112px" label-position="right">
          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="$t('outboundNotice.label.outboundNoticeCode')"
              >
                {{ form.outboundNoticeCode ? form.outboundNoticeCode : "-" }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$t('outboundNotice.label.sourceOrderCode')"
              >
                {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- 销售出库--客户  采购退货--供应商 -->
            <el-col :span="12" v-if="form.outboundType == 1">
              <el-form-item
                :label="$t('outboundNotice.label.customerName')"
              >
                {{ form.customerName ? form.customerName : "-" }}
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.outboundType == 2">
              <el-form-item
                :label="$t('outboundNotice.label.supplierName')"
              >
                {{ form.supplierName ? form.supplierName : "-" }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$t('outboundNotice.label.plannedReceivedTime')"
              >
                  <span v-if="form.plannedReceivedTime">
                    {{
                      parseTime(
                        form.plannedReceivedTime,
                        "{y}-{m}-{d} {h}:{i}:{s}"
                      )
                    }}
                  </span>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
          </el-row>
          <div>
            <el-table
              ref="tableSumRef1"
              :data="form.warehouseOutboundDetailVOList"
              highlight-current-row
              stripe
            >
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
                align="center"
              />
              <el-table-column
                :label="$t('outboundNotice.label.goodsInfor')"
                min-width="150"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div class="product-code">
                    {{ $t("outboundNotice.label.productCode") }}：
                    {{ scope.row.productCode }}
                  </div>
                  <div class="product-name">
                    {{ scope.row.productName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('outboundNotice.label.productSpecs')"
                prop="productSpecs"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('outboundNotice.label.productUnit')"
                prop="productUnitName"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('outboundNotice.label.productPlanQty')"
                prop="planProductQty"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span v-if="scope.row.planProductQty">{{ scope.row.planProductQty }}{{ scope.row.productUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('outboundNotice.label.productPlanWeight')"
                prop="planProductWeight"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span v-if="scope.row.planProductWeight">{{ scope.row.planProductWeight }}kg</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="'*' + $t('outboundNotice.label.productMiniWeight')"
                prop="productMiniWeight"
                min-width="250"
              >
                <template #default="scope">
                  <el-form-item
                    class="mt15px"
                    label-width="0px"
                    :prop="
                            'warehouseOutboundDetailVOList.' +
                            scope.$index +
                            '.productMiniWeight'
                          "
                    :rules="
                            [
                              {
                                required: true,
                                message:t('outboundNotice.rules.productMiniWeight'),
                                trigger: 'blur',
                              },
                              {
                                pattern:
                                  /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                                message: t(
                                  'outboundNotice.rules.productMiniWeightFomart'
                                ),
                                trigger: 'blur',
                              },
                            ]
                          "
                  >
                    <el-input
                      v-model="scope.row.productMiniWeight"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                    >
                      <template #append>
                        kg
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" @click="submitForm">{{ $t("common.confirm") }}</el-button>
          </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import {nextTick} from "vue";

defineOptions({
  name: "OutboundNotice",
  inheritAttrs: false,
});

import { convertToTimestamp, parseDateTime } from "@/core/utils/index.js";
import OutboundNoticeAPI, {
  OutboundNoticePageVO,
  OutboundNoticePageQuery,
} from "@/modules/wms/api/outboundNotice";
import { IObject } from "@/core/components/CURD/types";
import { useRouter } from "vue-router";
import { parseTime, changeDateRange } from "@/core/utils/index.js";
import moment from "moment";
import { emitter } from "@/core/utils/eventBus";

const router = useRouter();
const { t } = useI18n();
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const dateRange = ref([
  moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
  moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
]);
const weightEditDialog = ref(false);
const form = ref({
  warehouseOutboundDetailVOList: [],
});
const formRef = ref(ElForm);
const approveDialog = ref(false);
const plannedDeliveryDate = ref('');
const plannedDeliveryTime = ref('');
const dialogVisible = ref(false);
const multipleSelection = ref([]);
const approveResult = ref({
  failQty: 0,
  successQty: 0,
  failReason: [],
});
const outboundTypeList = ref([
  {
    outboundTypeId: 1,
    outboundTypeName: t("outboundNotice.outboundTypeList.procurementOutbound"),
  },
  {
    outboundTypeId: 2,
    outboundTypeName: t("outboundNotice.outboundTypeList.returnOutbound"),
  },
]);

const outboundNoticeStatusList = ref([
  {
    statusId: 0,
    statusName: t("outboundNotice.outboundNoticeStatusList.draft"),
  },
  {
    statusId: 1,
    statusName: t("outboundNotice.outboundNoticeStatusList.initial"),
  },
  {
    statusId: 2,
    statusName: t("outboundNotice.outboundNoticeStatusList.picking"),
  },
  {
    statusId: 3,
    statusName: t("outboundNotice.outboundNoticeStatusList.finish"),
  },
  {
    statusId: 4,
    statusName: t("outboundNotice.outboundNoticeStatusList.cancel"),
  },
]);

const approveStatusList = ref([
  {
    statusId: 0,
    statusName: t("outboundNotice.approveStatusList.Unaudited"),
  },
  {
    statusId: 1,
    statusName: t("outboundNotice.approveStatusList.success"),
  },
  {
    statusId: 2,
    statusName: t("outboundNotice.approveStatusList.fail"),
  },
]);
const dateTypeList = ref([
  {
    key: 1,
    value: t("outboundNotice.dateTypeList.createDate"),
  },
  {
    key: 2,
    value: t("outboundNotice.dateTypeList.plannedDate"),
  },
]);
const queryParams = reactive<OutboundNoticePageQuery>({
  dateType: 1,
  page: 1,
  limit: 20,
});
const outboundNoticeList = ref<OutboundNoticePageVO[]>();

const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];

/** 时间转换 */
function handleChangeDateRange(val: any) {
  dateRange.value = changeDateRange(val);
}
/** 查询 */
function handleQuery() {
  if (
    queryParams.outboundNoticeCode &&
    queryParams.outboundNoticeCode.length < 4
  ) {
    return ElMessage.error(t("warehouseEntryNotice.message.codeValideTips"));
  }
  if (queryParams.sourceOrderCode && queryParams.sourceOrderCode.length < 4) {
    return ElMessage.error(t("warehouseEntryNotice.message.codeValideTips"));
  }
  loading.value = true;
  let params = {
    ...queryParams,
  };
  if (queryParams.dateType == 1 && dateRange && dateRange.value.length > 0) {
    params.startCreateTime = new Date(dateRange.value[0]).getTime();
    params.endCreateTime = new Date(dateRange.value[1]).getTime();
  }
  if (queryParams.dateType == 2 && dateRange && dateRange.value.length > 0) {
    params.startPlannedReceivedTime = new Date(dateRange.value[0]).getTime();
    params.endPlannedReceivedTime = new Date(dateRange.value[1]).getTime();
  }
  delete params.dateType;
  OutboundNoticeAPI.getOutboundNoticePage(params)
    .then((data: any) => {
      //   outboundNoticeList.value = data.records;
      outboundNoticeList.value = data.records.map((item: any, index: any) => {
        item.mobilePhoneShow = true;
        if (item.contactPerson?.length > 1) {
          item.nameShow = false;
        } else {
          item.nameShow = true;
        }

        item.fullAddress = ` ${item.countryName}${item.provinceName}${item.cityName}${item.districtName}${item.address}`;
        if (item.fullAddress && containsNumber(item.fullAddress)) {
          item.addressShow = true;
          item.addressFormat = replaceNumbersWithAsterisk(item.fullAddress);
        } else {
          //不包含数字
          item.addressShow = false;
        }

        return { ...item };
      });
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}
function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}
function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}

function getRealAddress(index: any) {
  outboundNoticeList.value[index].addressShow = false;
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  queryParams.dateType = 1;
  queryParams.approveStatus = [];
  queryParams.outboundNoticeStatus = [];
  queryParams.outboundType = [];
  dateRange.value = [
    moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
    moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
  ];
  handleQuery();
}

// 审核
function handleApprove() {
  if (multipleSelection.value.length == 0) {
    return ElMessage.error(t("outboundNotice.message.selectApproveTips"));
  }
  let flag = multipleSelection.value.some(
    (item: any) => item.outboundNoticeStatus != 1
  );
  if (flag) {
    return ElMessage.error(t("outboundNotice.message.generateNotApproveTips"));
  }
  plannedDeliveryDate.value = parseDateTime(new Date().getTime(),'date')
  plannedDeliveryTime.value = ''
  approveDialog.value = true
}
function setDate(val) {
  if(val){
    plannedDeliveryDate.value = parseDateTime(new Date(val).getTime(),'date')
  }else {
    plannedDeliveryDate.value = ''
  }
}

function submitApprove() {
  if(plannedDeliveryDate.value == '' || plannedDeliveryDate.value == null || plannedDeliveryDate.value == undefined || plannedDeliveryTime.value == '' || plannedDeliveryTime.value == null || plannedDeliveryTime.value == undefined){
    return ElMessage.error(t("outboundNotice.rules.plannedDeliveryTimeRules"));
  }
  loading.value = true;
  let params: any = {
    ids: [],
    plannedDeliveryTime:new Date(plannedDeliveryDate.value + ' ' + plannedDeliveryTime.value + ':00').getTime()
  };
  params.ids = multipleSelection.value.map((item: any) => item.id);
  OutboundNoticeAPI.approveOutboundNotice(params)
    .then((data: any) => {
      approveDialog.value = false
      dialogVisible.value = true;
      approveResult.value = data;
      handleResetQuery();
    })
    .finally(() => (loading.value = false));
}

function setTime(num) {
  if(num == 92){
    plannedDeliveryDate.value = parseDateTime((new Date().setDate(new Date().getDate()+1)),'date')
    plannedDeliveryTime.value = '09:00'
  }else {
    plannedDeliveryTime.value = num > 10 ? num.toString() + ':00' : '0' + num.toString() + ':00'
  }
}

function closeApproveDialog() {
  approveDialog.value = false
  plannedDeliveryTime.value = ''
  ElMessage.info(t("outboundNotice.message.ApproveCancel"));
}

// 反审
function handleExApprove() {
  if (multipleSelection.value.length == 0) {
    return ElMessage.error(t("outboundNotice.message.selectExApproveTips"));
  }
  let flag = multipleSelection.value.some(
    (item: any) => item.outboundNoticeStatus != 2
  );
  if (flag) {
    return ElMessage.error(
      t("outboundNotice.message.generateNotExApproveTips")
    );
  }
  ElMessageBox.confirm(
    t("outboundNotice.message.exApproveTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      let params: any = {
        ids: [],
      };
      params.ids = multipleSelection.value.map((item: any) => item.id);
      OutboundNoticeAPI.exApproveOutboundNotice(params)
        .then((data: any) => {
          /*ElMessage.success(t("outboundNotice.message.exApproveSucess"));
          handleResetQuery();*/
          // approveDialog.value = false
          dialogVisible.value = true;
          approveResult.value = data;
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("outboundNotice.message.exApproveCancel"));
    }
  );
}

/* 姓名加密 */
function encryptName(name: any) {
  if (!name?.trim()) return "-";

  const firstChar = name[0]; // 获取第一位
  const shouldShowFourStars = name.length > 5;

  return shouldShowFourStars
    ? `${firstChar}****`
    : `${firstChar}${"*".repeat(name.length - 1)}`;
}

//删除
function handleDelete(id?: string) {
  ElMessageBox.confirm(
    t("outboundNotice.message.deleteTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      let params: any = {
        id: id,
      };
      OutboundNoticeAPI.delete(params)
        .then(() => {
          ElMessage.success(t("outboundNotice.message.deleteSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("outboundNotice.message.deleteCancel"));
    }
  );
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(val: any[]) {
  multipleSelection.value = val;
}

/** 取消入库通知单 */
function handleCancel(id?: string) {
  ElMessageBox.confirm(
    t("outboundNotice.message.cancelTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      let data = {
        id: id,
      };
      OutboundNoticeAPI.concelOutboundNotice(data)
        .then(() => {
          ElMessage.success(t("outboundNotice.message.cancelSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("outboundNotice.message.cancelConcel"));
    }
  );
}

// 获取真实电话号码
function getRealPhone(id: any, index: any) {
  OutboundNoticeAPI.queryRealPhone({ id: id })
    .then((data: any) => {
      outboundNoticeList.value[index].customerMobile = data.mobile;
      outboundNoticeList.value[index].mobilePhoneShow = false;
    })
    .finally(() => {});
}

function getRealName(index: any) {
  outboundNoticeList.value[index].nameShow = true;
}

/** 新增/编辑*/
function addOutboundNotice(id?: string, type?: string) {
  let title;
  if (type == "edit") {
    title = t("outboundNotice.title.editTitle");
  } else {
    title = t("outboundNotice.title.addTitle");
  }
  router.push({
    path: "/wms/outboundManagement/addOutboundNotice",
    query: { title: title, id: id, type: type },
  });
}

/** 详情*/
function detailOutboundNotice(id?: string) {
  router.push({
    path: "/wms/outboundManagement/detailOutboundNotice",
    query: { id: id },
  });
}
/** 查询出库通知单详情 */
function queryDetail(id) {
  loading.value = true;
  let params = {
    id:id,
  };
  OutboundNoticeAPI.queryDetailMobileEncrypt(params)
    .then((data: any) => {
      form.value = data
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重量调整 */
function weightEdit(id?: string){
  queryDetail(id)
  nextTick(() => {
    formRef.value?.clearValidate();
  })
  weightEditDialog.value = true
}
function submitForm() {
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    let params = {
      ...form.value
    }
    OutboundNoticeAPI.changeWeight(params).then((res)=>{
      close()
      ElMessage.success(t("outboundNotice.message.changeSuccess"));
    })
  })
}
function close(){
  weightEditDialog.value = false
}

onActivated(() => {
  handleQuery();
});
emitter.on("reloadListByWarehouseId", (e) => {
  nextTick(() => {
    handleQuery();
  });
});
</script>

<style lang="scss" scoped>
  :deep(.el-button--primary.el-button--default.is-link) {
    color: #762adb;
  }
  :deep(.el-button--danger.el-button--default.is-link) {
    color: #c00c1d;
  }
  .outboundNotice {
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }

    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .action-bar {
        margin-bottom: 12px;
        flex-shrink: 0;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
  :deep(.multipleSelect .el-select__selected-item) {
    color: #151719 !important;
  }
</style>
<style lang="scss">
  .approve-popup{
    padding: 0px;
    .el-dialog__header {
      padding: 16px 30px;
      border-bottom: 1px solid #E5E7F3;
      margin-bottom: 45px;
    }
    .el-dialog__footer{
      border-top: 1px solid #E5E7F3;
    }
    .el-dialog__headerbtn{
      width: 58px;
      height: 58px;
      .el-icon{
        width: 22px;
        height: 22px;
      }
      .el-icon svg{
        width: 22px;
        height: 22px;
      }
    }
    .el-date-editor.el-input, .el-date-editor.el-input__wrapper{
      width: calc(100% - 101.45px) !important;
    }
    .time-button{
      margin-top: 9px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
</style>
