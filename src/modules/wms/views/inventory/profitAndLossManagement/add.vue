<template>
    <div class="app-container">
      <div class="addProfitAndLoss">
        <div class="page-title">
          <div @click="handleClose()" class="cursor-pointer mr8px">
            <el-icon>
              <Back />
            </el-icon>
          </div>
          <div class="purchase-title">
            <span v-if="type == 'add'">
              {{ $t("profitAndLossManagement.button.addMaterial") }}
            </span>
            <span v-else>
              {{ $t("profitAndLossManagement.label.profitLossCode") }}：{{
                form.profitLossCode
              }}
            </span>
          </div>
          <div class="purchase" v-if="type == 'detail'">
            <div class="purchase">
              <div class="purchase-status purchase-status-color0" v-if="form.status == 0">
                {{ t("profitAndLossManagement.statusList.draft") }}
              </div>
              <div class="purchase-status purchase-status-color2" v-if="form.status == 1">
                {{ t("profitAndLossManagement.statusList.dealWidth") }}
              </div>
              <div class="purchase-status purchase-status-color3" v-if="form.status == 2">
                {{ t("profitAndLossManagement.statusList.finish") }}
              </div>
            </div>
          </div>
        </div>
        <div class="grad-row" style="position: absolute; top: 15px; right: 30px" v-if="type == 'edit'">
          <span class="el-form-item__label">
            {{ $t("profitAndLossManagement.label.createUserName") }}：
            <span class="el-form-item__content">{{ form.createUserName }}</span>
          </span>
          <span class="el-form-item__label">
            {{ t("profitAndLossManagement.label.createTime") }}：
            <span class="el-form-item__content">
              {{ parseDateTime(form.createTime, "dateTime") }}
            </span>
          </span>
        </div>
        <div class="page-content">
          <el-form :model="form" ref="formRef" label-width="110px" label-position="right">
            <div class="title-lable">
              <div class="title-content">
                {{ $t("profitAndLossManagement.label.basicInformation") }}
              </div>
            </div>
            <div>
              <template v-if="type == 'add' || type == 'edit'">
                <el-row>
                  <el-col :span="8">
                    <el-form-item :label="$t('profitAndLossManagement.label.profitLossType')" prop="profitLossType"
                      :rules="[
                        {
                          required: true,
                          message: t(
                            'profitAndLossManagement.rules.profitLossType'
                          ),
                          trigger: ['change', 'blur'],
                        },
                      ]">
                      <!--  disabled="form.status === 0" 从pda创建的损益单，不允许修改损益类型 和盘点单号 -->
                      <el-select v-model="form.profitLossType" :placeholder="$t('common.placeholder.selectTips')"
                        readonly>
                        <el-option v-for="item in profitAndLossTypeList" :key="item.typeId" :label="item.typeName"
                          :value="item.typeId"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="form.profitLossType == 0">
                    <el-form-item :label="$t('profitAndLossManagement.label.checkCode')" :rules="[
                      {
                        required: true,
                        message: t('profitAndLossManagement.rules.checkCode'),
                        trigger: ['change', 'blur'],
                      },
                    ]" prop="checkCode">
                      <el-select v-model="form.checkCode" :placeholder="$t('common.placeholder.selectTips')" clearable
                        filterable @change="changeCheckCode(null)" class="!w-[256px]">
                        <el-option v-for="item in checkCodeList" :key="item.checkCode" :label="item.checkCode"
                          :value="item.checkCode"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="form.profitLossType == 0">
                    <el-form-item :label="$t('profitAndLossManagement.label.warehouseAreaCode')
                      " prop="warehouseAreaCode">
                      <el-select v-model="form.warehouseAreaCode" :placeholder="$t('common.placeholder.selectTips')"
                        clearable filterable disabled class="!w-[256px]">
                        <el-option v-for="item in outWarehouseAreaListAll" :key="item.areaCode"
                          :label="`${item.areaName} | ${item.areaCode}`" :value="item.areaCode"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- <el-row v-if="type == 'edit'">
                  <el-col :span="8">
                    <el-form-item
                      :label="$t('profitAndLossManagement.label.receiptStatus')"
                    >
                      <span v-if="form.receiptStatus == 0">
                        {{ $t("profitAndLossManagement.claimTypeList.no") }}
                      </span>
                      <span v-if="form.receiptStatus == 1">
                        {{ $t("profitAndLossManagement.claimTypeList.yes") }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      :label="
                        $t('profitAndLossManagement.label.handleUserNameCopy')
                      "
                    >
                      <span>{{ form.handleUserName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      :label="$t('profitAndLossManagement.label.handleTimeCopy')"
                    >
                      <span>
                        {{ parseDateTime(form.handleTime, "dateTime") }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row> -->
                <el-row>
                  <el-col :span="16">
                    <el-form-item :label="$t('profitAndLossManagement.label.remark')" prop="remark">
                      <el-input :rows="2" type="textarea" show-word-limit v-model="form.remark"
                        :placeholder="$t('common.placeholder.inputTips')" maxlength="200" clearable />
                    </el-form-item>
                  </el-col>
  
                </el-row>
                <!-- 快速损益附件上传 -->
                <el-row v-if="form.profitLossType == 3">
                  <el-col :span="16">
                    <el-form-item :label="'上传附件'" prop="imagesUrls">
                      <el-badge :value="getAttachmentBadgeValue()" :offset="[5, 10]">
                        <el-button type="primary" link @click="handleUploadDialog">
                          上传
                        </el-button>
                      </el-badge>
                      <!--  <upload-multiple :listType="`text`" :tips="''" :fileSize="10"
                        :fileType="['rar', 'zip', 'docx', 'xls', 'xlsx', 'pdf', 'jpg', 'png', 'jpeg']"
                        :modelValue="form.imagesUrls" @update:model-value="onFileChange" ref="uploadRef" :limit="6"
                        :formRef="formRef" class="modify-multipleUpload" name="attachment" /> -->
                    </el-form-item>
                  </el-col>
                </el-row>
              </template>
              <template v-if="type == 'detail'">
                <el-row>
                  <el-col :span="6">
                    <!-- 损益类型 -->
                    <el-form-item :label="$t('profitAndLossManagement.label.profitLossType')" prop="profitLossType">
                      <span v-if="form.profitLossType == 0">
                        {{
                          $t(
                            "profitAndLossManagement.profitAndLossTypeList.inventoryCheck"
                          )
                        }}
                      </span>
                      <span v-if="form.profitLossType == 1">
                        {{
                          $t(
                            "profitAndLossManagement.profitAndLossTypeList.singleProduct"
                          )
                        }}
                      </span>
                      <span v-if="form.profitLossType == 2">
                        {{
                          $t(
                            "profitAndLossManagement.profitAndLossTypeList.ysnCode"
                          )
                        }}
                      </span>
                      <span v-if="form.profitLossType == 3">
                        {{
                          $t(
                            "profitAndLossManagement.profitAndLossTypeList.fastProfitAndLoss"
                          )
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <!-- 盘点单号 -->
                  <el-col :span="6" v-if="form.profitLossType == 0">
                    <el-form-item :label="$t('profitAndLossManagement.label.checkCode')" prop="checkCode">
                      <span>{{ form.checkCode || "-" }}</span>
                    </el-form-item>
                  </el-col>
  
                  <!-- YSN损益 -->
                  <el-col :span="6" v-if="form.profitLossType == 2">
                    <el-form-item :label="$t('profitAndLossManagement.label.warehouseAreaName')
                      ">
                      <span>
                        {{ form.warehouseAreaCode }} |
                        {{ form.warehouseAreaName }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <!-- 创建人 -->
                  <el-col :span="6">
                    <el-form-item :label="$t('profitAndLossManagement.label.createUserName')" prop="createUserName">
                      <span>{{ form.createUserName }}</span>
                    </el-form-item>
                  </el-col>
                  <!-- 创建时间 -->
                  <el-col :span="6">
                    <el-form-item :label="$t('profitAndLossManagement.label.createTime')">
                      <span>
                        {{ parseDateTime(form.createTime, "dateTime") }}
                      </span>
                    </el-form-item>
                  </el-col>
  
                  <!--  0=盘点单损益 1=单包装商品损益 2=YSN损益：盘点单损益 -->
                  <!--  <el-col :span="6" v-if="form.profitLossType == 0">
                    <el-form-item
                      :label="
                        $t('profitAndLossManagement.label.warehouseAreaName')
                      "
                    >
                      <span>
                        {{ form.warehouseAreaCode }} |
                        {{ form.warehouseAreaName }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" v-if="form.profitLossType == 1">
                    <el-form-item
                      :label="
                        $t('profitAndLossManagement.label.warehouseAreaName')
                      "
                    >
                      <span>
                        {{ form.warehouseAreaCode }} |
                        {{ form.warehouseAreaName }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" v-if="form.profitLossType == 3">
                    <el-form-item
                      :label="
                        $t(
                          'profitAndLossManagement.label.beforeProfitLossTotalQtyCopy'
                        )
                      "
                    >
                      <span>{{ form.beforeProfitLossTotalQty }}</span>
                    </el-form-item>
                  </el-col> -->
                  <!-- </el-row> -->
                  <!--  0=盘点单损益 1=单包装商品损益 2=YSN损益：盘点单损益 -->
                  <template v-if="form.profitLossType == 0">
                    <el-col :span="6">
                      <el-form-item :label="$t('profitAndLossManagement.label.beforeProfitLossQty')
                        ">
                        <span>{{ form.beforeProfitLossQty }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item :label="$t(
                        'profitAndLossManagement.label.afterProfitLossQtyCopy'
                      )
                        ">
                        <span>{{ form.afterProfitLossQty }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item :label="$t(
                        'profitAndLossManagement.label.beforeProfitLossTotalQty'
                      )
                        ">
                        <span>{{ form.beforeProfitLossTotalQty }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item :label="$t(
                        'profitAndLossManagement.label.afterProfitLossTotalQty'
                      )
                        ">
                        <span>{{ form.afterProfitLossTotalQty }}</span>
                      </el-form-item>
                    </el-col>
                  </template>
                  <!--  0=盘点单损益 1=单包装商品损益 2=YSN损益： 盘点单损益、单包装商品损益 -->
                  <!-- <template> -->
                  <!--  <el-col
                    :span="6"
                    v-if="form.profitLossType == 0 || form.profitLossType == 2"
                  >
                    <el-form-item
                      :label="
                        $t('profitAndLossManagement.label.increaseProfitLossQty')
                      "
                    >
                      <span>{{ form.increaseProfitLossQty }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col
                    :span="6"
                    v-if="form.profitLossType == 0 || form.profitLossType == 2"
                  >
                    <el-form-item
                      :label="
                        $t('profitAndLossManagement.label.decreaseProfitLossQty')
                      "
                    >
                      <span>{{ form.decreaseProfitLossQty }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" v-if="form.profitLossType == 3">
                    <el-form-item
                      :label="
                        $t(
                          'profitAndLossManagement.label.afterProfitLossTotalQtyCopy'
                        )
                      "
                    >
                      <span>{{ form.afterProfitLossTotalQty }}</span>
                    </el-form-item>
                  </el-col> -->
                  <!--  <el-col :span="6">
                    <el-form-item
                      :label="$t('profitAndLossManagement.label.receiptStatus')"
                      prop="receiptStatus"
                    >
                      <span v-if="form.receiptStatus == 0">
                        {{ $t("profitAndLossManagement.claimTypeList.no") }}
                      </span>
                      <span v-if="form.receiptStatus == 1">
                        {{ $t("profitAndLossManagement.claimTypeList.yes") }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      :label="
                        $t('profitAndLossManagement.label.handleUserNameCopy')
                      "
                      prop="handleUserName"
                    >
                      <span>{{ form.handleUserName }}</span>
                    </el-form-item>
                  </el-col> -->
  
                  <!--  0=盘点单损益 1=单包装商品损益 2=YSN损益 -->
                  <el-col :span="6" v-if="
                    form.profitLossType == 3 ||
                    form.profitLossType == 2 ||
                    form.profitLossType == 1
                  ">
                    <el-form-item :label="$t('profitAndLossManagement.label.handleTimeCopy')">
                      <span>
                        {{ parseDateTime(form.handleTime, "dateTime") }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" v-if="form.profitLossType == 2 || form.profitLossType == 1">
                    <el-form-item :label="$t('profitAndLossManagement.label.remark')" prop="remark">
                      <span style="word-break: break-all">{{ form.remark }}</span>
                    </el-form-item>
                  </el-col>
                  <!-- </template> -->
                  <!-- <template> -->
                  <el-col :span="6" v-if="form.profitLossType == 3">
                    <el-form-item :label="$t(
                      'profitAndLossManagement.label.beforeProfitLossWeightCopy'
                    )
                      ">
                      <span>{{ form.beforeProfitLossWeight }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" v-if="form.profitLossType == 3">
                    <el-form-item :label="$t(
                      'profitAndLossManagement.label.afterProfitLossWeightCopy'
                    )
                      ">
                      <span>{{ form.afterProfitLossWeight }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" v-if="form.profitLossType == 3">
                    <el-form-item :label="$t('profitAndLossManagement.label.remark')" prop="remark">
                      <span style="word-break: break-all">{{ form.remark }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" v-if="form.profitLossType == 0">
                    <el-form-item :label="$t('profitAndLossManagement.label.handleTimeCopy')">
                      <span>
                        {{ parseDateTime(form.handleTime, "dateTime") }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <!-- 状态 -->
                  <el-col :span="6">
                    <el-form-item :label="$t('profitAndLossManagement.label.status')">
                      <!-- 损益状态 0 草稿 1处理中 2完成 -->
  
                      <div class="">
                        {{ statusMap(form.status) }}
                        <!-- <span :class="['purchase-status',statusClassMap(form.status)]">{{ statusMap(form.status)}}</span> -->
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" v-if="form.profitLossType == 0">
                    <el-form-item :label="$t('profitAndLossManagement.label.remark')" prop="remark">
                      <span style="word-break: break-all">{{ form.remark }}</span>
                    </el-form-item>
                  </el-col>
                  <!-- </template> -->
                </el-row>
              </template>
            </div>
            <div class="title-lable" style="justify-content: space-between">
              <div class="title-content">
                {{ $t("profitAndLossManagement.label.lossDetail") }}
              </div>
              <div class="button-add cursor-pointer" @click="addProduct()"
                v-if="type != 'detail' && form.profitLossType == 3">
                {{ $t("profitAndLossManagement.button.addBtn") }}
              </div>
            </div>
            <div>
              <!-- 按盘点结果损益|盘点单损益:损益明细 -->
              <el-table v-if="form.profitLossType == 0" v-loading="loading" :data="form.profitLossDetailList"
                highlight-current-row stripe>
                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                <el-table-column :label="$t('profitAndLossManagement.label.productInformation')" min-width="230">
                  <template #default="scope">
                    <div class="product-div">
                      <div class="product">
                        <div>
                          <span class="product-key">
                            {{
                              $t("profitAndLossManagement.label.productCode")
                            }}：
                          </span>
                          <span class="product-value">
                            {{ scope.row.productCode }}
                          </span>
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('profitAndLossManagement.label.productSpec')" prop="productSpec"
                  min-width="100" show-overflow-tooltip />
                <el-table-column :label="$t('profitAndLossManagement.label.beforeCheckTotalQty')"
                  prop="beforeCheckTotalQty" min-width="100" align="right" show-overflow-tooltip>
                  <template #default="scope">
                    <span style="color: #52585f" v-if="
                      scope.row.beforeCheckTotalQty !== undefined &&
                      scope.row.beforeCheckTotalQty !== null &&
                      scope.row.beforeCheckTotalQty !== ''
                    ">
                      {{ scope.row.beforeCheckTotalQty }}
                    </span>
                    <span style="color: #52585f" v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('profitAndLossManagement.label.afterCheckTotalQty')" prop="afterCheckTotalQty"
                  min-width="100" align="right" show-overflow-tooltip>
                  <template #default="scope">
                    <span style="color: #52585f" v-if="
                      scope.row.afterCheckTotalQty !== undefined &&
                      scope.row.afterCheckTotalQty !== null &&
                      scope.row.afterCheckTotalQty !== ''
                    ">
                      {{ scope.row.afterCheckTotalQty }}
                    </span>
                    <span style="color: #52585f" v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('profitAndLossManagement.label.lossQty')" prop="lossQty" align="right"
                  min-width="120" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.lossQty >= 0" style="color: #29b610">
                      +{{ scope.row.lossQty }}
                    </span>
                    <span v-if="scope.row.lossQty < 0" style="color: #52585f">
                      {{ scope.row.lossQty }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('profitAndLossManagement.label.lossInfo')" min-width="120">
                  <template #default="scope">
                    <el-button link type="primary" @click="openDetailLossInfo(scope.row)">
                      {{ $t("profitAndLossManagement.button.openDetail") }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 单包装商品损益:损益明细 -->
              <el-table v-loading="loading" v-if="form.profitLossType == 1" :data="form.profitLossDetailList"
                highlight-current-row stripe>
                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                <el-table-column :label="$t('profitAndLossManagement.label.ysnCode')" min-width="200"
                  show-overflow-tooltip>
                  <template #default="scope">
                    <span>{{ scope.row.profitLossDetailYsnVO.ysnCode }}</span>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('profitAndLossManagement.label.beforeProfitLossWeight')
                  " prop="beforeProfitLossWeight" show-overflow-tooltip />
                <el-table-column :label="$t('profitAndLossManagement.label.afterProfitLossWeight')
                  " prop="afterProfitLossWeight" show-overflow-tooltip />
              </el-table>
              <!-- YSN损益:损益明细 -->
              <template v-if="form.profitLossType == 2">
                <div class="title-lable">
                  <div class="title-content">
                    {{
                      $t("profitAndLossManagement.label.profitLossDetailYsnAdd")
                    }}
                  </div>
                </div>
                <el-table v-loading="loading" :data="form.profitLossDetailYsnAddVOList" highlight-current-row stripe>
                  <el-table-column type="index" :label="$t('common.sort')" width="60" />
                  <el-table-column :label="$t('profitAndLossManagement.label.ysnCode')" prop="ysnCode" min-width="200"
                    show-overflow-tooltip />
                  <el-table-column :label="$t('profitAndLossManagement.label.productInformation')
                    " min-width="230" show-overflow-tooltip>
                    <template #default="scope">
                      <div class="product-div">
                        <div class="product">
                          <div>
                            <span class="product-key">
                              {{
                                $t("profitAndLossManagement.label.productCode")
                              }}：
                            </span>
                            <span class="product-value">
                              {{ scope.row.productCode }}
                            </span>
                          </div>
                          <div class="product-name">
                            {{ scope.row.productName }}
                          </div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('profitAndLossManagement.label.weight')" prop="weight"
                    show-overflow-tooltip />
                </el-table>
                <div class="title-lable">
                  <div class="title-content">
                    {{
                      $t("profitAndLossManagement.label.profitLossDetailYsnSub")
                    }}
                  </div>
                </div>
                <el-table v-loading="loading" :data="form.profitLossDetailYsnSubVOList" highlight-current-row stripe>
                  <el-table-column type="index" :label="$t('common.sort')" width="60" />
                  <el-table-column :label="$t('profitAndLossManagement.label.ysnCode')" prop="ysnCode" min-width="200"
                    show-overflow-tooltip />
                  <el-table-column :label="$t('profitAndLossManagement.label.productInformation')
                    " min-width="230" show-overflow-tooltip>
                    <template #default="scope">
                      <div class="product-div">
                        <div class="product">
                          <div>
                            <span class="product-key">
                              {{
                                $t("profitAndLossManagement.label.productCode")
                              }}：
                            </span>
                            <span class="product-value">
                              {{ scope.row.productCode }}
                            </span>
                          </div>
                          <div class="product-name">
                            {{ scope.row.productName }}
                          </div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('profitAndLossManagement.label.weight')" prop="weight"
                    show-overflow-tooltip />
                </el-table>
              </template>
              <!-- 快速损益:损益明细 -->
              <el-table v-if="form.profitLossType == 3" v-loading="loading" :data="form.profitLossDetailList"
                highlight-current-row stripe>
                <!-- 序号 -->
                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                <!-- 商品信息 -->
                <el-table-column :label="$t('profitAndLossManagement.label.productInformation')" min-width="150">
                  <template #default="scope">
                    <div class="product-div">
                      <div class="product">
                        <div>
                          <span class="product-key">
                            {{
                              $t("profitAndLossManagement.label.productCode")
                            }}：
                          </span>
                          <span class="product-value">
                            {{ scope.row.productCode }}
                          </span>
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <!--  <el-table-column
                  v-if="form.profitLossType == 0"
                  :label="$t('profitAndLossManagement.label.productSpec')"
                  prop="productSpec"
                  min-width="100"
                  show-overflow-tooltip
                /> -->
                <!-- 库区 -->
                <el-table-column :label="'*' + $t('profitAndLossManagement.label.warehouseAreaCode')
                  " prop="warehouseAreaCode" show-overflow-tooltip min-width="200">
                  <template #default="scope">
                    <el-form-item class="mt15px" label-width="0px" :prop="'profitLossDetailList.' +
                      scope.$index +
                      '.warehouseAreaCode'
                      " :rules="[
                        {
                          required: true,
                          message: t(
                            'profitAndLossManagement.rules.warehouseAreaCode'
                          ),
                          trigger: ['blur', 'change'],
                        },
                      ]">
                      <el-select v-model="scope.row.warehouseAreaCode" :placeholder="$t('common.placeholder.selectTips')"
                        @change="calculateWeightedAveragePrice($event, scope.$index, scope.row); queryProductStockQty(scope.row)"
                        clearable v-if="type != 'detail'">
                        <el-option v-for="item in outWarehouseAreaList" :key="item.areaCode"
                          :label="`${item.areaName} | ${item.areaCode}`" :value="item.areaCode" />
                      </el-select>
                      <el-input v-if="type == 'detail'" v-model="scope.row.warehouseAreaCode" disabled></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 库区总库存 -->
                <el-table-column :label="$t('profitAndLossManagement.label.beforeInventoryQty')" min-width="150"
                  show-overflow-tooltip align="left">
                  <template #default="scope">
                    <div>
                      总库存量:{{
                        scope.row.beforeInventoryQty || "-"
                      }}
                    </div>
                    <div>
                      转换量:{{
                        scope.row.beforeInventoryWeight || "-"
                      }}
                    </div>
                  </template>
                </el-table-column>
  
                <!-- 单价 -->
                <el-table-column :label="$t('profitAndLossManagement.label.unitPrice')" prop="unitPrice" min-width="150"
                  show-overflow-tooltip>
                  <template #default="scope">
                    <el-form-item class="mt15px" label-width="0px" :prop="'profitLossDetailList.' +
                      scope.$index +
                      '.unitPrice'
                      " :rules="[
                        {
                          required: true,
                          message: '单价不能为空',
                          trigger: ['blur', 'change'],
                        },
                        {
                          pattern: /^(-?[0-9]{0,7}(?:\.\d{1,4})?)$/,
                          message: '整数位限长7位，小数后4位',
                          trigger: ['blur', 'change'],
                        },
                      ]">
                      <el-input v-model="scope.row.unitPrice" :placeholder="$t('common.placeholder.inputTips')" clearable
                        @change="calculateAmount(scope.row, 'afterInventoryQty', 'afterInventoryWeight', 'unitPrice')" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 损益后库存总数量|损益后库存总量 -->
                <el-table-column :label="'*' + $t('profitAndLossManagement.label.afterInventoryQty')
                  " prop="afterInventoryQty" min-width="260" v-if="type != 'detail'">
                  <template #default="scope">
                    <el-form-item class="mt15px" label-width="0px" :prop="'profitLossDetailList.' +
                      scope.$index +
                      '.afterInventoryQty'
                      " :rules="[
                        {
                          required: true,
                          message: t(
                            'profitAndLossManagement.rules.afterInventoryQtyNull'
                          ),
                          trigger: ['blur', 'change'],
                        },
                        {
                          pattern: /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                          message: t(
                            '整数位限长8位，小数后3位'
                          ),
                          trigger: ['blur', 'change'],
                        },
                      ]">
                      <el-input v-model="scope.row.afterInventoryQty"
                        @change="setLossQty(scope.$index, 1, 'FIRST_TO_SECOND')"
                        :placeholder="$t('common.placeholder.inputTips')" clearable maxLength="8">
                        <template #append>{{ scope.row.productUnit }}</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- 损益后库存总重量|损益后库存转换量 -->
                <el-table-column :label="'*' + $t('profitAndLossManagement.label.inventoryConversionAfterProfitAndLoss')
                  " prop="afterInventoryWeight" min-width="220" v-if="type != 'detail'">
                  <template #default="scope">
                    <el-form-item class="mt15px" label-width="0px" :prop="'profitLossDetailList.' +
                      scope.$index +
                      '.afterInventoryWeight'
                      " :rules="[
                        {
                          required: true,
                          message: t(
                            'profitAndLossManagement.rules.afterInventoryWeightNull'
                          ),
                          trigger: ['blur', 'change'],
                        },
                        {
                          pattern:
                            /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                          message: t(
                            '整数位限长8位，小数后3位'
                          ),
                          trigger: ['blur', 'change'],
                        },
                      ]">
                      <el-input v-model="scope.row.afterInventoryWeight"
                        @change="setLossQty(scope.$index, 2, 'SECOND_TO_FIRST')"
                        :placeholder="$t('common.placeholder.inputTips')" clearable>
                        <template #append>
                          {{
                            scope.row.conversionRelSecondUnitName
                          }}
                        </template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
  
                <!-- 金额 -->
                <el-table-column :label="$t('profitAndLossManagement.label.amount')" prop="amount" align="left"
                  show-overflow-tooltip min-width="150">
                  <template #default="scope">
                    <el-form-item class="mt15px" label-width="0px" :prop="'profitLossDetailList.' +
                      scope.$index +
                      '.amount'
                      " :rules="[
                        {
                          required: true,
                          message: '金额不能为空',
                          trigger: ['blur', 'change'],
                        },
                        {
                          pattern:
                            /^-?(0(?:\.\d{1,2})?|[1-9]\d{0,8}(?:\.\d{1,2})?)$/,
                          message: '整数位限长9位，小数后2位',
                          trigger: ['blur', 'change'],
                        },
                      ]">
                      <el-input v-model="scope.row.amount" :placeholder="$t('common.placeholder.inputTips')" clearable />
                    </el-form-item>
                  </template>
                </el-table-column>
               <!--  <el-table-column :label="$t('profitAndLossManagement.label.afterInventoryQty')" prop="afterInventoryQty"
                  min-width="200" align="right" v-if="type == 'detail'" show-overflow-tooltip /> -->
                <!--损益后库存转换量 -->
               <!--  <el-table-column :label="$t('profitAndLossManagement.label.inventoryConversionAfterProfitAndLoss')"
                  prop="afterInventoryWeight" min-width="200" align="right" v-if="type == 'detail'"
                  show-overflow-tooltip /> -->
                <!-- 损益后库存总量 -->
                <!-- <el-table-column
                  :label="
                    $t('profitAndLossManagement.label.afterInventoryWeight') +
                    '(' +
                    $t('profitAndLossManagement.label.afterInventoryWeightUnit') +
                    ')'
                  "
                  prop="afterInventoryWeight"
                  min-width="200"
                  align="right"
                  v-if="type == 'detail'"
                  show-overflow-tooltip
                /> -->
                <!-- 损益数量 -->
                <el-table-column :label="$t('profitAndLossManagement.label.lossQty')" prop="lossQty" align="left"
                  min-width="120" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.lossQty >= 0" style="color: #29b610">
                      +{{ scope.row.lossQty }}
                    </span>
                    <span v-if="scope.row.lossQty < 0" style="color: #52585f">
                      {{ scope.row.lossQty }}
                    </span>
                  </template>
                </el-table-column>
                <!-- 损益重量/损益转换量 -->
                <el-table-column :label="$t('profitAndLossManagement.label.lossConversion')" prop="lossWeight"
                  align="left" min-width="120" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.lossWeight >= 0" style="color: #29b610">
                      +{{ scope.row.lossWeight }}
                    </span>
                    <span v-if="scope.row.lossWeight < 0" style="color: #52585f">
                      {{ scope.row.lossWeight }}
                    </span>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  :label="
                    $t('profitAndLossManagement.label.lossWeight') +
                    '(' +
                    $t('profitAndLossManagement.label.afterInventoryWeightUnit') +
                    ')'
                  "
                  prop="lossWeight"
                  align="right"
                  min-width="120"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <span v-if="scope.row.lossWeight >= 0" style="color: #29b610">
                      +{{ scope.row.lossWeight }}
                    </span>
                    <span v-if="scope.row.lossWeight < 0" style="color: #52585f">
                      {{ scope.row.lossWeight }}
                    </span>
                  </template>
                </el-table-column> -->
                <el-table-column v-if="type != 'detail'" :label="$t('common.handle')" width="160">
                  <template #default="scope">
                    <el-button type="danger" size="small" link @click="handleDelete(scope.$index)">
                      {{ $t("common.delete") }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form>
        </div>
        <div class="page-footer">
          <!-- status	损益状态 0 草稿 1处理中 2完成 -->
          <el-button @click="handleClose">{{ $t("common.reback") }}</el-button>
          <el-button v-if="type == 'add' || type == 'edit'" plain @click="handleSubmit(0)" :loading="submitLoading">
            {{ $t("profitAndLossManagement.button.saveDraft") }}
          </el-button>
          <el-button v-if="type == 'add' || type == 'edit'" type="primary" :loading="submitLoading"
            @click="handleSubmit(2)">
            {{ $t("common.confirm") }}
          </el-button>
        </div>
        <AddProduct ref="addProductRef" v-model:visible="dialog.visible" :title="dialog.title" @onSubmit="onSubmit" />
        <LossInfoListDialog ref="lossInfoListRef" v-model:visible="lossDialog.visible" :title="lossDialog.title" />
        <UploadDialog ref="uploadRef" v-model:visible="uploadDialog.visible" @onSubmit="onSubmitUpload" />
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import profitAndLossManagementApi from "@/modules/wms/api/profitAndLossManagement";
  import ProductMgAPI from "@/modules/wms/api/productManagement";
  import { useRoute, useRouter } from "vue-router";
  import { useTagsViewStore } from "@/core/store";
  import AddProduct from "./components/addProduct.vue";
  import LossInfoListDialog from "./components/lossInfoListDialog.vue";
  import { parseDateTime } from "@/core/utils/index.js";
  import UploadDialog from "./components/uploadDialog.vue";
  import ProfitAndLossManagementApi, {
    InventoryLossFrom,
  } from "@/modules/wms/api/profitAndLossManagement";
  import CommonAPI, { ProductAllPageVO } from "@/modules/wms/api/common";
  import UploadMultiple from "@/core/components/Upload/UploadMultiple.vue";
  import { useConvertProductUnit } from "@/modules/wms/composables";
  const { convertProductUnitStragery, calculateAmount } = useConvertProductUnit();
  
  defineOptions({
    name: "ProfitAndLossForm",
    inheritAttrs: false,
  });
  
  const route = useRoute();
  const router = useRouter();
  const tagsViewStore = useTagsViewStore();
  const { t } = useI18n();
  const submitLoading = ref(false);
  const formRef = ref(ElForm);
  const productTotal = ref(0);
  const lossInfoTotal = ref(0);
  const lossInfoList = ref<ProductAllPageVO[]>();
  const productAllList = ref<ProductAllPageVO[]>();
  const confirmDisabled = ref(false);
  const loading = ref(false);
  const id = route.query.id;
  const type = route.query.type;
  const outWarehouseAreaListAll = ref([]);
  const outWarehouseAreaList = ref([]);
  const dialog = reactive({
    title: "",
    visible: false,
  });
  const lossDialog = reactive({
    title: "",
    visible: false,
  });
  const lossInfoListRef = ref();
  const addProductRef = ref();
  const checkCodeList = ref([]);
  const form = reactive({
    profitLossDetailList: [],
    profitLossDetailYsnAddVOList: [],
    profitLossDetailYsnSubVOList: [],
    profitLossType: 3, // 快速损益
    imagesUrls: [],
    remark: "",
  } as InventoryLossFrom & { imagesUrls?: any[] });
  const profitAndLossTypeList = ref([
    // 损益类型 0=盘点单损益 1=单包装商品损益 2=YSN损益 3=快速损益
    /*  {
       typeId: 0,
       typeName: t("profitAndLossManagement.profitAndLossTypeList.inventoryCheck"),
     }, */
  
    {
      typeId: 3,
      typeName: t(
        "profitAndLossManagement.profitAndLossTypeList.fastProfitAndLoss"
      ),
    },
  ]);
  
  function getImagesUrls() {
    if (form.imagesUrls && typeof form.imagesUrls === 'string' && form.imagesUrls.length > 0) {
      return JSON.parse(form.imagesUrls);
    }
    else if (form.imagesUrls && Array.isArray(form.imagesUrls) && form.imagesUrls.length > 0) {
      return form.imagesUrls;
    }
    return [];
  }
  
  // 获取附件徽章显示值的函数
  function getAttachmentBadgeValue() {
    const images = getImagesUrls();
    return images.length > 0 ? images.length : '';
  }
  const formUpdateRef = ref(null);
  const uploadRef = ref();
  const show = ref(false);
  
  // 上传弹窗状态
  const uploadDialog = reactive({
    visible: false,
  });
  
  // const addProductRef = ref();
  const uploadDialogRef = ref();
  function handleUploadDialog() {
    uploadDialog.visible = true;
    uploadDialogRef.value?.setEditType("add");
    if (form.imagesUrls) {
      nextTick(() => {
        uploadRef.value?.setFormData(form.imagesUrls);
      })
    }
  }
  function onSubmitUpload(val: any) {
    form.imagesUrls = val;
  }
  // 处理文件上传
  function onFileChange(val: any) {
    form.imagesUrls = val;
  }
  async function handleClose() {
    await tagsViewStore.delView(route);
    router.go(-1);
  }
  /** 查询当前仓库的所有库区下拉数据源 */
  function getOutWarehouseAreaList() {
    CommonAPI.getOutWarehouseAreaList({}).then((data: any) => {
      outWarehouseAreaListAll.value = data;
    });
  }
  /** 获取盘点单号下拉列表 */
  function queryCheckCodeList() {
    profitAndLossManagementApi.queryCheckCodeList().then((data: any) => {
      checkCodeList.value = data;
    });
  }
  
  // 原损益模块逻辑代码，需要优化
  /* function getWarehouseAreaDetail(event: any, index: number) {
    if (event && event != "") {
      outWarehouseAreaList.value.forEach((item: any) => {
        if (item.areaCode == event) {
          let params = {
            productCode: form.profitLossDetailList[index].productCode,
            warehouseAreaCode: event,
          };
          //查所选库区当前商品的总库存
          profitAndLossManagementApi
            .queryAreaProductStockQty(params)
            .then((data) => {
              form.profitLossDetailList[index].beforeInventoryQty =
                data && data.totalStockQty ? data.totalStockQty : "";
              form.profitLossDetailList[index].beforeInventoryWeight =
                data && data.totalStockWeight ? data.totalStockWeight : "";
              form.profitLossDetailList[index].warehouseAreaName = item.areaName;
              if (
                form.profitLossDetailList[index].afterInventoryQty !== "" &&
                form.profitLossDetailList[index].afterInventoryQty != null &&
                form.profitLossDetailList[index].afterInventoryQty != undefined
              ) {
                if (form.profitLossDetailList[index].beforeInventoryQty !== "") {
                  form.profitLossDetailList[index].lossQty = Number(
                    form.profitLossDetailList[index].afterInventoryQty -
                    form.profitLossDetailList[index].beforeInventoryQty
                  ).toFixed(3);
                } else {
                  form.profitLossDetailList[index].lossQty = Number(
                    form.profitLossDetailList[index].afterInventoryQty
                  ).toFixed(3);
                }
              } else {
                form.profitLossDetailList[index].lossQty = "-";
              }
              if (
                form.profitLossDetailList[index].afterInventoryWeight !== "" &&
                form.profitLossDetailList[index].afterInventoryWeight != null &&
                form.profitLossDetailList[index].afterInventoryWeight != undefined
              ) {
                if (
                  form.profitLossDetailList[index].beforeInventoryWeight !== ""
                ) {
                  form.profitLossDetailList[index].lossWeight = Number(
                    form.profitLossDetailList[index].afterInventoryWeight -
                    form.profitLossDetailList[index].beforeInventoryWeight
                  ).toFixed(3);
                } else {
                  form.profitLossDetailList[index].lossWeight = Number(
                    form.profitLossDetailList[index].afterInventoryWeight
                  ).toFixed(3);
                }
              } else {
                form.profitLossDetailList[index].lossWeight = "-";
              }
            })
            .finally(() => {
              return false;
            });
        }
      });
    } else {
      form.profitLossDetailList[index].beforeInventoryQty = "";
      form.profitLossDetailList[index].warehouseAreaName = "";
      form.profitLossDetailList[index].lossQty = "";
      form.profitLossDetailList[index].lossWeight = "";
    }
  } */
  
  /** 查询前仓库的库区列表(库存大于0的库区) ---废弃 */
  // 根据当前仓库获取有库存的库区列表(可选0:停用 1:正常) ---废弃
  
  // 查询已启用库区
  function getOutWarehouseAreaNumberList() {
    return new Promise((resolve, reject) => {
      // CommonAPI.getOutWarehouseAreaNumberList({ status: 1 })
      CommonAPI.getOutWarehouseAreaList({ status: 1 })
        .then((data) => {
          outWarehouseAreaList.value = data;
          if (
            outWarehouseAreaList.value &&
            outWarehouseAreaList.value.length > 0
          ) {
            outWarehouseAreaList.value.map((item) => {
              item.warehouseArea = item.warehouseName + "|" + item.areaName;
              return item;
            });
          }
          resolve();
        })
        .catch((error) => {
          loading.value = false;
          reject(error);
        });
    });
  }
  
  function changeCheckCode(val) {
    if (val == 1) {
      form.checkCode = undefined;
      form.warehouseAreaCode = undefined;
    }
    if (form.profitLossType == 0 && form.checkCode !== undefined) {
      show.value = true;
      queryCheckCodeDetail();
    } else {
      show.value = false;
      form.profitLossDetailList = [];
    }
  }
  
  /** 获取盘点单详情 */
  function queryCheckCodeDetail() {
    let params = {
      checkCode: form.checkCode,
    };
    profitAndLossManagementApi.queryCheckCodeDetail(params).then((data) => {
      if (data) {
        form.warehouseAreaCode = data.warehouseAreaCode;
        if (data.detailList && data.detailList.length > 0) {
          form.profitLossDetailList = data.detailList;
          form.profitLossDetailList.forEach((item) => {
            if (
              item.afterCheckTotalQty !== "" &&
              item.afterCheckTotalQty != null &&
              item.afterCheckTotalQty != undefined
            ) {
              if (item.beforeCheckTotalQty !== "") {
                item.lossQty = Number(
                  item.afterCheckTotalQty - item.beforeCheckTotalQty
                ).toFixed(3);
              } else {
                item.lossQty = Number(item.afterCheckTotalQty).toFixed(3);
              }
            } else {
              item.lossQty = Number(item.beforeCheckTotalQty).toFixed(3);
            }
          });
        }
      }
    });
  }
  
  /** 查询所有可选的商品列表 */
  function queryProductAll(type) {
    return new Promise<void>((resolve, reject) => {
      loading.value = true;
      let params = {
        limit: 20,
        page: 1,
        isSku: 1,
      };
      ProductMgAPI.getPageList(params)
        .then((data) => {
          productAllList.value = [];
          if (data.records && data.records.length > 0) {
            data.records.forEach((data) => {
              let obj = {
                productName: data.productName,
                productCode: data.productCode,
                productSpec: data.productSpec,
                fullCategoryName: data.fullCategoryName,
                productUnit: data.productUnitName,
                isDiscreteUnit: data.isDiscreteUnit, // 一级单位增减->1:开启；0:关闭
                pricingScheme: data.pricingScheme, // 计价模式->0:一级单位;1:二级单位
                productId: data.id,
                productCode: data.productCode,
                isSku: data.isSku,
                conversionRelSecondUnitName: data.conversionRelSecondUnitName,
              };
              productAllList.value.push(obj);
            });
          }
          productTotal.value = parseInt(data.total);
          resolve();
        })
        .catch(() => {
          reject();
        })
        .finally(() => {
          loading.value = false;
        });
      // })
    });
  }
  
  async function queryProductStockQty(row: any) {
    try {
      const data = await profitAndLossManagementApi.queryProductStockQty({ productCode: row.productCode, warehouseAreaCode: row.warehouseAreaCode });
      if (data) {
        row.beforeInventoryQty = data?.totalStockQty;
        row.beforeInventoryWeight = data?.totalStockWeight;
      }
      else {
        row.beforeInventoryQty = "";
        row.beforeInventoryWeight = "";
      }
    } catch (error) {
      console.log(error);
    }
  }
  // 计算加权平均价接口（实时）
  async function calculateWeightedAveragePrice(event: any, index: number, row: any) {
    const data = {
      isDiscreteUnit: row.isDiscreteUnit,
      productCode: row.productCode,
      warehouseAreaCode: row.warehouseAreaCode,
      warehouseLocationCode: row.warehouseLocationCode,
      pricingScheme: row.pricingScheme,
    }
    const res = await CommonAPI.calculateWeightedAveragePrice(data);
    row.unitPrice = res;
    calculateAmount(row, 'afterInventoryQty', 'afterInventoryWeight', 'unitPrice');
  }
  async function setLossQty(index: number, val: number, stragetry: string) {
    const row: any = form.profitLossDetailList && form.profitLossDetailList.length > 0 ? form.profitLossDetailList[index] : {};
    /* if (val == 1) {
      if (
        row.beforeInventoryQty ||
        row.beforeInventoryQty === 0
      ) {
        if (!isNull(row.afterInventoryQty)) {
          row.lossQty = Number(
            row.afterInventoryQty -
            row.beforeInventoryQty
          );
        } else {
          row.lossQty = "-";
        }
      } else {
        if (
          row.afterInventoryQty !== "" &&
          row.afterInventoryQty != null &&
          row.afterInventoryQty != undefined
        ) {
          row.lossQty = Number(
            row.afterInventoryQty
          );
        } else {
          row.lossQty = "-";
        }
      }
    }
    else {
      if (
        row.beforeInventoryWeight ||
        row.beforeInventoryWeight === 0
      ) {
        if (
          row.afterInventoryWeight !== "" &&
          row.afterInventoryWeight != null &&
          row.afterInventoryWeight != undefined
        ) {
          row.lossWeight = Number(
            row.afterInventoryWeight -
            row.beforeInventoryWeight
          ).toFixed(3);
        } else {
          row.lossWeight = "-";
        }
      } else {
        if (
          row.afterInventoryWeight !== "" &&
          row.afterInventoryWeight != null &&
          row.afterInventoryWeight != undefined
        ) {
          row.lossWeight = Number(
            row.afterInventoryWeight
          ).toFixed(3);
        } else {
          row.lossWeight = "-";
        }
      }
    } */
    // 一级单位、二级单位的互相转化
    await convertProductUnitStragery(row, stragetry, 'afterInventoryQty', 'afterInventoryWeight');
    // 损益转换量计算：损益后库存转换量 - 转换量
    // 损益数量：损益后库存总量 - 总库存量
    if(!isNull(row.afterInventoryQty)){
      const beforeInventoryQty = isNull(row.beforeInventoryQty) ? 0 : Number(row.beforeInventoryQty);
      row.lossQty = Number(row.afterInventoryQty) - beforeInventoryQty;
    }
    else {
      row.lossQty = "-";
    }
    if(!isNull(row.afterInventoryWeight)){
      const beforeInventoryWeight = isNull(row.beforeInventoryWeight) ? 0 : Number(row.beforeInventoryWeight);
      row.lossWeight = (Number(row.afterInventoryWeight) - beforeInventoryWeight).toFixed(3);
    } 
    else {
      row.lossWeight = "-";
    }
  }
  // 商品一级单位转二级单位
  /* async function convertProductUnit(data: any) {
    const res = await CommonAPI.convertProductUnit(data);
    return res?.convertedValue;
  } */
  
  // isDiscreteUnit: 1开启，0关闭
  /*输入若商品的【一级单位增减isDiscreteUnit】为【是】      
    输入数量时，转换量自动转换      
    输入转换量时，数量不管，用户手动填写
  若商品的【一级单位增减】为【否】       
    输入数量时，转换量内无值时自动转换，有值时不转换       
    输入转换量时，数量内无值时自动转换，且数量向上取整，有值时不转换 */
  /* async function convertProductUnitStrageryBak (row: any, stragetry: string) {
    const data = {
      convertUnitTypeEnum: stragetry,
      originalValue: row.afterInventoryQty,
      productId: row.productId || row.id,
      productCode: row.productCode,
    }
   if(stragetry == "FIRST_TO_SECOND"){
    if(row.isDiscreteUnit == 1){
      const val = await convertProductUnit(data);
      row.afterInventoryWeight = parseFloat(val).toFixed(3);
      calculateAmount(row, 'afterInventoryQty', 'afterInventoryWeight', 'unitPrice');
    }
    else if(row.isDiscreteUnit == 0){
      if(isNull(row.afterInventoryWeight)){ // 输入数量时，转换量内无值时自动转换，有值时不转换
        const val = await convertProductUnit(data);
        row.afterInventoryWeight = parseFloat(val).toFixed(3);
        calculateAmount(row, 'afterInventoryQty', 'afterInventoryWeight', 'unitPrice');
      }
    }
   }
   else if(stragetry == "SECOND_TO_FIRST"){
    if(row.isDiscreteUnit == 0){
      if(isNull(row.afterInventoryQty)){ // 输入数量时，转换量内无值时自动转换，有值时不转换
        const val = await convertProductUnit(data);
        row.afterInventoryQty = parseIntInt(val);
        calculateAmount( row, 'afterInventoryQty', 'afterInventoryWeight', 'unitPrice');
      }
    }
   }
  } */
  
  
  // 判断值部位null，undefined，''
  function isNull(val: any) {
    return val === null || val === undefined || val === '';
  }
  // 单价+量+转换量:计算金额
  /* async function calculateAmountBak(row: any) {
    if(isNull(row.afterInventoryWeight) || isNull(row.afterInventoryQty) || isNull(row.unitPrice)) return;
    const data = {
      convertedQty: row.afterInventoryWeight,
      productId: row.productId,
      qty: row.afterInventoryQty,
      unitPrice: row.unitPrice,
      productCode: row.productCode,
    }
    const res = await CommonAPI.calculateAmount(data);
    row.amount = res?.amount;
  } */
  
  
  /** 损益明细查看 */
  async function openDetailLossInfo(row) {
    lossDialog.title =
      t("profitAndLossManagement.title.lossInfo") +
      row.productCode +
      " | " +
      row.productName;
    let data = {
      checkDetailId: row.id,
      productCode: row.productCode,
      productName: row.productName,
      checkCode: form.checkCode,
    };
    lossInfoListRef.value.setFormData({
      lossInfoList: lossInfoList.value,
      lossInfoTotal: lossInfoTotal.value,
      queryParams: data,
    });
    lossDialog.visible = true;
  }
  
  /** 添加商品 */
  async function addProduct() {
    await queryProductAll(null);
    addProductRef.value.queryManagerCategoryList();
    dialog.title = t("profitAndLossManagement.title.addProduct");
    let data = {
      outWarehouseArea: form.outWarehouseArea,
      isSku: 1,
    };
    addProductRef.value.setFormData({
      productAllList: productAllList.value,
      productTotal: productTotal.value,
      queryParams: data,
    });
    dialog.visible = true;
  }
  
  function handleSubmit(val: number) {
    if (
      form.profitLossType == 3 &&
      form.profitLossDetailList &&
      form.profitLossDetailList.length == 0
    ) {
      return ElMessage.error(
        t("profitAndLossManagement.message.addOrEditInventoryLossTips")
      );
    }
    // 商品编码productCode+库区warehouseAreaCode不唯一时，提示
    if (form.profitLossDetailList && form.profitLossDetailList.length > 0) {
      const uniqueItems = new Map();
      for (let i = 0; i < form.profitLossDetailList.length; i++) {
        const key = form.profitLossDetailList[i].productCode + form.profitLossDetailList[i].warehouseAreaCode;
        if (uniqueItems.has(key)) {
          return ElMessage.error(t("profitAndLossManagement.message.sameDataTips"));
        }
        uniqueItems.set(key, form.profitLossDetailList[i]);
      }
    }
    formRef.value.validate((valid: boolean) => {
      if (!valid) return;
      let params = {
        profitLossType: form.profitLossType,
        remark: form.remark,
        status: val,
        imagesUrls: JSON.stringify(form.imagesUrls)
      };
  
      if (form.profitLossType == 3) {
        let profitLossDetailList: any[] = [];
        if (
          form.profitLossType == 3 &&
          form.profitLossDetailList &&
          form.profitLossDetailList.length > 0
        ) {
          params.profitLossDetailList = form.profitLossDetailList;
        }
      }
      if (type !== "add") {
        params.id = id;
      }
      submitLoading.value = true;
      if (val === 0) { // 草稿
        ProfitAndLossManagementApi.saveInventoryLoss(params)
          .then((data) => {
            ElMessage.success(t("profitAndLossManagement.message.addSuccess"));
            handleClose();
          })
          .finally(() => {
            submitLoading.value = false;
          });
      }
      else if (val === 2) { // 保存
        ElMessageBox.confirm(
          t("profitAndLossManagement.message.saveTips"),
          t("common.tipTitle"),
          {
            confirmButtonText: t("common.confirm"),
            cancelButtonText: t("common.cancel"),
            type: "warning",
          }
        ).then(
          () => {
            ProfitAndLossManagementApi.saveInventoryLoss(params)
              .then((data) => {
                ElMessage.success(t("profitAndLossManagement.message.addSuccess"));
                handleClose();
              })
              .finally(() => {
                submitLoading.value = false;
              });
          },
          () => {
            submitLoading.value = false;
            ElMessage.info(t("profitAndLossManagement.message.saveCancel"));
          }
        );
      }
  
    });
  }
  /** 假删除*/
  function handleDelete(index?: number) {
    form.profitLossDetailList.splice(index, 1);
    ElMessage.success(t("profitAndLossManagement.message.deleteSuccess"));
  }
  function queryInventoryLossDetail() {
    loading.value = true;
    let params = {
      profitLossId: id,
    };
    ProfitAndLossManagementApi.queryInventoryLossDetail(params)
      .then((data) => {
        Object.assign(form, data);
        if (type == "edit") {
          const set = new Set();
          for (let i = 0; i < outWarehouseAreaList.value.length; i++) {
            const key = outWarehouseAreaList.value[i].areaCode;
            set.add(key);
          }
          for (let i = 0; i < form.profitLossDetailList.length; i++) {
            if (!set.has(form.profitLossDetailList[i].warehouseAreaCode)) {
              form.profitLossDetailList[i].warehouseAreaCode = "";
              form.profitLossDetailList[i].warehouseAreaName = "";
            }
          }
        }
        if (type == "detail") {
          for (let i = 0; i < form.profitLossDetailList.length; i++) {
            form.profitLossDetailList[i].warehouseAreaCode =
              form.profitLossDetailList[i].warehouseAreaName +
              "|" +
              form.profitLossDetailList[i].warehouseAreaCode;
          }
        }
        form.imagesUrls = typeof form.imagesUrls === 'string' ? JSON.parse(form.imagesUrls) : form.imagesUrls;
      })
      .finally(() => {
        loading.value = false;
      });
  }
  function onSubmit(data) {
  
    let arr = data.concat(form.profitLossDetailList);
    form.profitLossDetailList = arr;
  }
  
  const statusMap = (status: string | number) => {
    const obj = {
      0: "草稿",
      1: "处理中",
      2: "完成",
    };
    return obj[status] || "-";
  };
  const statusClassMap = (status: string | number) => {
    const obj = {
      0: "purchase-status-color0",
      1: "purchase-status-color1",
      2: "purchase-status-color2",
    };
    return obj[status] || "";
  };
  onMounted(async () => {
    getOutWarehouseAreaList();
    await getOutWarehouseAreaNumberList();
    queryCheckCodeList();
    if (type !== "add") {
      confirmDisabled.value = true;
      queryInventoryLossDetail();
    }
  });
  </script>
  <style scoped lang="scss">
  .addProfitAndLoss {
    background: #ffffff;
    border-radius: 4px;
  
    .page-content {
      .equal {
        height: 32px;
        line-height: 32px;
        padding: 0px 8px;
        margin-bottom: 18px;
      }
  
      .button-add {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-weight: 600;
        font-size: 14px;
        color: var(--el-color-primary);
      }
  
      .default-supplier {
        margin-left: 8px;
        padding: 2px 7px;
        background: #fe8200;
        border-radius: 4px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #ffffff;
        line-height: 16px;
        text-align: left;
        font-style: normal;
      }
    }
  }
  </style>
