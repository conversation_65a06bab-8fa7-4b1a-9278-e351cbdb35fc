<template>
  <div class="app-container">
    <div class="permissionManager-container">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form ref="headFormRef" :model="searchForm" :inline="true">
            <el-form-item prop="mobile" :label="$t('userWareHouse.label.mobile')">
              <el-input
                oninput="value=value.replace(/^0|[^0-9]/g, '')"
                v-model="searchForm.mobile"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                @keyup.enter="onSearchHandler"
              />
            </el-form-item>
            <el-form-item
              prop="nickName"
              :label="$t('userWareHouse.label.nickName')"
            >
              <el-input
                v-model="searchForm.nickName"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                @keyup.enter="onSearchHandler"
              />
            </el-form-item>
            <el-form-item prop="deptIds" :label="$t('userWareHouse.label.affiliatedDepartment')">
              <el-cascader
                ref="searchDeptRef"
                v-model="searchForm.deptIds"
                :options="departmentList"
                :props="{ value: 'id', label: 'deptName',  multiple: true, checkStrictly: true }"
                collapse-tags
                clearable
                @change="setDeptIds"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearchHandler">
                {{ $t("common.search") }}
              </el-button>
              <el-button @click="onResetHandler">
                {{ $t("common.reset") }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="content-card">
        <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="tableData"
          highlight-current-row
          border
        >
          <el-table-column
            type="index"
            :label="$t('userWareHouse.label.sort')"
            width="55"
          />
          <el-table-column
            :label="$t('userWareHouse.label.userName')"
            prop="userName"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('userWareHouse.label.nickName')"
            prop="nickName"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('userWareHouse.label.mobile')"
            prop="mobile"
            show-overflow-tooltip
            min-width="160"
          >
            <template #default="scope">
              <EncryptPhone :phone="scope.row.mobile" />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('userWareHouse.label.roleName')"
            prop="roleName"
            show-overflow-tooltip
            min-width="160"
          >
            <template #default="scope">
            <span v-for="(role, index) in scope.row.baseRoleVOList" :key="role.id">
             {{ index > 0 ? ',' : '' }} {{ role.roleName }}
            </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('userWareHouse.label.deptName')"
            prop="deptNames"
            show-overflow-tooltip
            min-width="160"
          ></el-table-column>
          <el-table-column
            prop="mobile"
            :label="$t('userWareHouse.label.status')"
            width="120"
            align="center"
          >
            <template #default="scope">
              <el-switch
                disabled
                :active-text="$t('common.activeBtn')"
                :inactive-text="$t('common.inactiveBtn')"
                inline-prompt
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                @change="
                onStatusChangeHandler(scope.row.status, 'ids', [scope.row.id])
              "
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('storeArea.label.operate')"
            fixed="right"
            min-width="200"
          >
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                link
                @click="onEditHandler(scope.row.userId)"
              >
                {{ $t("userWareHouse.button.assignAuth") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="paginationInfo.pageNo"
            v-model:limit="paginationInfo.pageSize"
            @pagination="onPaginationChangeHandler"
          />
        </div>
      </el-card>
    </div>
    <el-drawer
      v-model="showDialog"
      :title="$t('userWareHouse.title.selWareHouse')"
      :close-on-click-modal="false"
      @close="onCloseHandler"
    >
      <div class="normal14-font">
        已选择
        <span class="primary20-font" style="padding: 5px">{{ selCount }}</span>
      </div>
      <el-table
        ref="userWareHouseTableRef"
        :data="warehouseData"
        style="margin-top: 10px; width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="drawerLoading"
        @select="userSelAction"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          :label="$t('userWareHouse.label.sort')"
          width="60"
          align="center"
        />
        <el-table-column
          property="warehouseCode"
          :label="$t('userWareHouse.label.warehouseCoding')"
          width="110"
          align="center"
        />
        <el-table-column
          property="warehouseName"
          :label="$t('userWareHouse.label.warehouseName')"
          show-overflow-tooltip
          align="center"
        />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseHandler()">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button type="primary" @click="onSaveHandler">
            {{ $t("common.confirm") }}
          </el-button>
        </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import tableMixin from "@/modules/wms/mixins/table";
import UserWarehouseAPI, {
  userWarehouseInfo,
} from "@/modules/wms/api/userWarehouse";
// import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/core/api/role";
import type { TableInstance } from "element-plus";
import type { CascaderProps } from "element-plus";
import { useUserStore } from "@/core/store";
const userStore = useUserStore();

const departmentList = ref([]);
const searchDeptRef = ref()

const { t } = useI18n();
const multipleSelection = ref<userWarehouseInfo[]>([]);
const showDialog = ref(false);
const warehouseData = ref([]);
const selCount = ref(0);
const drawerLoading = ref(false);
const userWareHouseTableRef = ref<TableInstance>();
const selUserId = ref("");
const searchForm = reactive({
  mobile: "",
  nickName: "",
  deptIds:[],//部门
});
const {
  loading,
  tableData,
  total,
  paginationInfo,
  headFormRef,
  router,
  path,
  onSearchHandler,
  onResetHandler,
  onPaginationChangeHandler,
  onDeleteHandler,
  onStatusChangeHandler,
} = tableMixin({
  searchForm,
  tableGetApi: UserWarehouseAPI.getUserWarehouse,
});
function onCloseHandler() {
  selCount.value = 0;
  showDialog.value = false;
}
function queryDepartmentList() {
  UserWarehouseAPI.allDeptList().then((data) => {
    departmentList.value = data;
  });
}
function setDeptIds() {
  searchForm.deptIds = []
  // 获取选中的nodeList
  let nodeList = searchDeptRef.value.getCheckedNodes()
  if(nodeList && nodeList.length > 0){
    nodeList.forEach(list=>{
      searchForm.deptIds.push(list.value)
    })
  }
}
function onSaveHandler() {
  let params = {
    userId: selUserId.value,
    warehouseCodos: multipleSelection.value,
  };
  UserWarehouseAPI.editStoreArea(params)
    .then((data) => {
      onCloseHandler();
      ElMessage.success("操作成功");
    })
    .finally(() => {});
}
function onEditHandler(id: string) {
  drawerLoading.value = true;
  showDialog.value = true;
  selUserId.value = id;
  let params = {
    userId: id,
  };
  UserWarehouseAPI.checkedUserById(params)
    .then((data) => {
      warehouseData.value = data;
    })
    .finally(() => {
      drawerLoading.value = false;
      warehouseData.value.forEach((item) => {
        if (item.checked) {
          userWareHouseTableRef.value!.toggleRowSelection(item);
          userSelAction();
        }
      });
    });
}
function userSelAction() {
  selCount.value = userWareHouseTableRef.value!.getSelectionRows().length;
}
const handleSelectionChange = (val: userWarehouseInfo[]) => {
  multipleSelection.value = val.map((item) => {
    return item.warehouseCode;
  });
  userSelAction()
};
onMounted(() => {
  searchForm.deptIds = []
  if(userStore.user.baseDeptVOList && userStore.user.baseDeptVOList.length > 0){
    userStore.user.baseDeptVOList.forEach(list=>{
      searchForm.deptIds.push(list.id)
    })
  }
  onSearchHandler();
  queryDepartmentList()
});
</script>

<style lang="scss" scoped>
  .permissionManager-container{
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }
    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .action-bar {
        margin-bottom: 12px;
        flex-shrink: 0;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
</style>
