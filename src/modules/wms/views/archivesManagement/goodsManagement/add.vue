<template>
  <div class="app-container">
    <div class="addPurchase">
      <div class="page-title">
        <div @click="handleClose()" class="cursor-pointer mr8px">
          <el-icon><Back /></el-icon>
        </div>
        <div>
          <span v-if="type == 'add'">
            {{ $t("purchase.button.addGoods") }}
          </span>
          <span v-else>{{ $t("purchase.button.editGoods") }}</span>
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="82px"
          label-position="right"
        >
          <div class="item_content">
            <div class="title">
              {{ $t("purchase.label.basicInformation") }}
            </div>
            <el-row :gutter="20">
              <!-- 商品编码 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.productCode')"
                  prop="productCode"
                >
                  <el-input
                    v-model="form.productCode"
                    :placeholder="$t('purchase.placeholder.systemGenerated')"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 商品名称 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.productName')"
                  prop="productName"
                >
                  <el-input
                    v-model="form.productName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    maxlength="120"
                  />
                </el-form-item>
              </el-col>
              <!-- 商品分类 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.productCategory')"
                  prop="productCategory"
                >
                  <el-cascader
                    v-model="form.productCategory"
                    :options="categoryList"
                    :props="propsCategory"
                    @change="handleChange"
                    ref="cascaderRef"
                    filterable
                    :placeholder="$t('purchase.placeholder.productCategory')"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <!-- 商品描述  -->
              <el-col :span="24" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.productDesc')"
                  prop="productDesc"
                  style="margin-bottom: 18px"
                >
                  <el-input
                    v-model="form.productDesc"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    maxlength="200"
                  />
                </el-form-item>
              </el-col>
              <!-- 一级单位 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.unit')"
                  prop="productUnitId"
                >
                  <el-select
                    v-model="form.productUnitId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    :disabled="productInventoryAndTemplateDisabled"
                    @change="conversionRelSecondUnitChange"
                  >
                    <el-option
                      v-for="item in unitList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 换算关系 -->
              <el-col :span="16" class="flex-center-start mb-18px">
                <el-form-item
                  :label="$t('purchase.label.changeRelationship')"
                  prop="conversionRelFirstNum"
                >
                  <el-input
                    v-model="form.conversionRelFirstNum"
                    :placeholder="$t('common.placeholder.inputTips')"
                    disabled
                    class="!w-[62px]"
                  />
                </el-form-item>
                <el-form-item label-width="0px" prop="productUnitId">
                  <el-select
                    v-model="form.productUnitId"
                    :placeholder="$t('purchase.label.unit')"
                    clearable
                    disabled
                    class="!w-[100px]"
                  >
                    <el-option
                      v-for="item in unitList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <div class="equal">=</div>
                <el-form-item label-width="0px" prop="conversionRelSecondNum">
                  <el-input
                    v-model="form.conversionRelSecondNum"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    class="!w-[120px]"
                    :disabled="isNumDisabled || productInventoryAndTemplateDisabled"
                    @blur="conversionRelSecondUnitChange"
                  />
                </el-form-item>
                <el-form-item
                  label-width="0px"
                  prop="conversionRelSecondUnitId"
                >
                  <el-select
                    v-model="form.conversionRelSecondUnitId"
                    :placeholder="$t('purchase.label.unitNameCopy')"
                    :disabled="productInventoryAndTemplateDisabled"
                    clearable
                    class="!w-[100px]"
                    @change="conversionRelSecondUnitChange"
                  >
                    <el-option
                      v-for="item in unitList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 是否标品 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.isStandardCopy')"
                  prop="isStandard"
                >
                  <el-select
                    v-model="form.isStandard"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="item in standardList"
                      :key="item.key"
                      :label="item.value"
                      :value="item.key"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 商品属性 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.attributeType')"
                  prop="attributeType"
                >
                  <el-select
                    v-model="form.attributeType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="(item,index) in attributeTypeList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 规格 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.productSpec')"
                  prop="productSpec"
                >
                  <el-input
                    v-model="form.productSpec"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <!-- 损耗比例 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.lossRatio')"
                  prop="lossRatio"
                >
                  <el-input
                    v-model="form.lossRatio"
                    :placeholder="$t('purchase.placeholder.lossRatio')"
                    maxlength="50"
                    clearable
                  >
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <!-- 商品长宽高 -->
              <el-col :span="16" class="flex-center-start mb-18px">
                <el-form-item
                  :label="$t('purchase.label.productlwhv')"
                  prop="length"
                >
                  <el-input
                    v-model="form.length"
                    :placeholder="$t('common.placeholder.inputTips')"
                    @blur="handleVolume"
                    class="!w-[65px]"
                  />
                </el-form-item>
                <div class="equal">(cm)*</div>
                <el-form-item label-width="0px" prop="width">
                  <el-input
                    v-model="form.width"
                    :placeholder="$t('common.placeholder.inputTips')"
                    @blur="handleVolume"
                    class="!w-[65px]"
                  />
                </el-form-item>
                <div class="equal">(cm)*</div>
                <el-form-item label-width="0px" prop="height">
                  <el-input
                    v-model="form.height"
                    :placeholder="$t('common.placeholder.inputTips')"
                    @blur="handleVolume"
                    class="!w-[65px]"
                  />
                </el-form-item>
                <div class="equal">(cm)=</div>
                <el-form-item label-width="0px" prop="volume">
                  <el-input
                    v-model="form.volume"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    class="!w-[80px]"
                  />
                </el-form-item>
                <div class="equal">(m³)</div>
              </el-col>
              <!-- 重量 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.weight')"
                  prop="weight"
                  :rules="
                    form.isStandard == 1 || isRequired
                      ? [
                          {
                            required: false,
                            message: $t('purchase.rules.weight'),
                            trigger: 'blur',
                          },
                        ]
                      : []
                  "
                >
                  <el-input
                    v-model="form.weight"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    :disabled="isWeightDisabled"
                  >
                    <template #append>Kg</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <!-- 商品品牌 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.brand')"
                  prop="productBrandId"
                >
                  <el-select
                    v-model="form.productBrandId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="item in brandList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 条码信息 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.barcode')"
                  prop="barcode"
                >
                  <el-input
                    v-model="form.barcode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
                <!-- 型号 -->
                <el-col :span="8" class="mb-18px">
                    <el-form-item
                      :label="$t('purchase.label.modelNumber')"
                      prop="modelNumber"
                    >
                        <el-input
                          v-model="form.modelNumber"
                          :placeholder="$t('common.placeholder.inputTips')"
                          clearable
                          maxlength="30"
                        />
                    </el-form-item>
                </el-col>
                <!-- 外部编码 -->
                <el-col :span="8" class="mb-18px">
                    <el-form-item
                      :label="$t('purchase.label.outerProductCode')"
                      prop="outerProductCode"
                    >
                        <el-input
                          v-model="form.outerProductCode"
                          :placeholder="$t('common.placeholder.inputTips')"
                          clearable
                          maxlength="30"
                        />
                    </el-form-item>
                </el-col>
                <!-- 颜色 -->
                <el-col :span="8" class="mb-18px">
                    <el-form-item
                      :label="$t('purchase.label.color')"
                      prop="color"
                    >
                        <el-select
                          v-model="form.color"
                          :placeholder="$t('common.placeholder.selectTips')"
                          clearable
                        >
                            <el-option
                              v-for="(item,index) in colorOption"
                              :key="index"
                              :label="item.label"
                              :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <!-- 尺码 -->
                <el-col :span="8" class="mb-18px">
                    <el-form-item
                      :label="$t('purchase.label.size')"
                      prop="size"
                    >
                        <el-select
                          v-model="form.size"
                          :placeholder="$t('common.placeholder.selectTips')"
                          clearable
                        >
                            <el-option
                              v-for="(item,index) in sizeOption"
                              :key="index"
                              :label="item.label"
                              :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
              <!-- 是否SKU -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.isSku')"
                  prop="isSku"
                >
                  <el-select
                    v-model="form.isSku"
                    :disabled="productInventoryDisabled"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="item in isSKUOptionList"
                      :key="item.key"
                      :label="item.value"
                      :value="item.key"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 备注 -->
              <el-col :span="8" class="mb-18px">
                <el-form-item
                  :label="$t('purchase.label.remark')"
                  prop="remark"
                >
                  <el-input
                    v-model="form.remark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    maxlength="200"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="item_content">
            <div class="title">
              {{ $t("purchase.label.saleInformation") }}
            </div>
            <el-row :gutter="20" class="mb-20px">
              <!-- 是否可售卖 -->
              <el-col :span="4">
                <el-form-item
                  :label="$t('purchase.label.isSalable')"
                  prop="isSalable"
                >
                  <el-switch
                    :active-text="$t('common.statusYesOrNo.yes')"
                    :inactive-text="$t('common.statusYesOrNo.no')"
                    inline-prompt
                    style="
                      --el-switch-on-color: #762adb;
                      --el-switch-off-color: #cccfd5;
                    "
                    v-model="form.isSalable"
                    :active-value="1"
                    :inactive-value="0"
                    @change="changeSaleStatus()"
                  />
                </el-form-item>
              </el-col>
              <!-- 销售价格 -->
              <el-col :span="8" v-if="form.isSalable == 1">
                <el-form-item
                  :label="$t('purchase.label.saleAmount')"
                  prop="saleAmount"
                >
                  <el-input
                    v-model="form.saleAmount"
                    :placeholder="$t('common.placeholder.inputTips')"
                  >
                    <template #append>￥</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <!-- 浮动区间 -->
              <el-col :span="8" v-if="form.isSalable == 1">
                <el-form-item
                  :label="$t('purchase.label.saleAmountRadio')"
                  prop="saleAmountRadio"
                >
                  <el-input
                    v-model="form.saleAmountRadio"
                    :placeholder="$t('purchase.placeholder.saleAmountRadio')"
                    clearable
                    maxlength="120"
                  >
                    <template #append>%</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
            <div class="item_content">
                <div class="title">
                    {{ $t("purchase.label.repertoryInformation") }}
                </div>
                <el-row :gutter="20" class="mb-20px">
                    <!-- 所属仓库 -->
                    <el-col :span="8" class="mb-18px">
                      <el-form-item
                        :label="$t('purchase.label.warehouseName')"
                      >
                        {{ warehouseName }}
                      </el-form-item>
                    </el-col>
                    <!-- 存储条件 -->
                    <el-col :span="8" class="mb-18px">
                        <el-form-item
                          :label="$t('purchase.label.storageCondition')"
                          prop="storageCondition"
                        >
                            <el-input
                              v-model="form.storageCondition"
                              :placeholder="$t('common.placeholder.inputTips')"
                              maxlength="50"
                              clearable
                            />
                        </el-form-item>
                    </el-col>
                    <!-- 保质期 -->
                    <el-col :span="8" class="flex-center-start mb-18px">
                        <el-form-item
                          :label="$t('purchase.label.shelfLife')"
                          prop="shelfLife"
                        >
                            <el-input
                              v-model="form.shelfLife"
                              :placeholder="$t('common.placeholder.inputTips')"
                              clearable
                              maxlength="50"
                            />
                        </el-form-item>
                        <el-form-item label-width="0px" prop="shelfLifeUnit">
                            <el-select
                              v-model="form.shelfLifeUnit"
                              :placeholder="$t('common.placeholder.selectTips')"
                              class="!w-[100px]"
                            >
                                <el-option
                                  v-for="item in shelfLifeUnitList"
                                  :key="item.key"
                                  :label="item.value"
                                  :value="item.key"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                  <!-- 安全库存 -->
                  <el-col :span="8" class="mb-18px">
                    <el-form-item
                      :label="$t('purchase.label.safetyStock')"
                      prop="safetyStock"
                      :rules="[{pattern:/^(-?0(?:\.\d{0,3})?|[1-9]\d{0,7}(?:\.\d{0,3})?)$/,message: t('purchase.rules.safetyStockFormat'),trigger: ['blur', 'change'],},]"
                    >
                      <el-input
                        v-model="form.safetyStock"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                      >
                        <template #append>{{safetyStockUnit}}</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <!-- 库存上线 -->
                  <el-col :span="8" class="mb-18px">
                    <el-form-item
                      :label="$t('purchase.label.stockUpperLimit')"
                      prop="stockUpperLimit"
                      :rules="[{pattern:/^(-?0(?:\.\d{0,3})?|[1-9]\d{0,7}(?:\.\d{0,3})?)$/,message: t('purchase.rules.safetyStockFormat'),trigger: ['blur', 'change'],},]"
                    >
                      <el-input
                        v-model="form.stockUpperLimit"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                        >
                        <template #append>{{safetyStockUnit}}</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <!-- 库存下线 -->
                  <el-col :span="8" class="mb-18px">
                    <el-form-item
                      :label="$t('purchase.label.stockLowerLimit')"
                      prop="stockLowerLimit"
                      :rules="[{pattern:/^(-?0(?:\.\d{0,3})?|[1-9]\d{0,7}(?:\.\d{0,3})?)$/,message: t('purchase.rules.safetyStockFormat'),trigger: ['blur', 'change'],},]"
                    >
                      <el-input
                        v-model="form.stockLowerLimit"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                      >
                      <template #append>{{safetyStockUnit}}</template>
                      </el-input>
                    </el-form-item>
                  </el-col>

                    <!-- 一级单位增减 -->
                    <el-col :span="8">
                        <el-form-item
                          :label="$t('purchase.label.isDiscreteUnit')"
                          prop="isDiscreteUnit"
                          label-width="120px"
                        >
                            <el-switch
                              :active-text="$t('common.statusYesOrNo.yes')"
                              :inactive-text="$t('common.statusYesOrNo.no')"
                              inline-prompt
                              style="
                      --el-switch-on-color: #762adb;
                      --el-switch-off-color: #cccfd5;
                    "
                              v-model="form.isDiscreteUnit"
                              :active-value="1"
                              :inactive-value="0"
                              :disabled="productInventoryAndTemplateDisabled"
                            />
                        </el-form-item>
                    </el-col>
                  <!-- 计价模式 -->
                  <el-col :span="8">
                    <el-form-item
                      :label="$t('purchase.label.pricingScheme')"
                      prop="pricingScheme">
                      <el-select
                        v-model="form.pricingScheme"
                        :disabled="productInventoryDisabled"
                        :placeholder="$t('common.placeholder.selectTips')"
                        clearable
                      >
                        <el-option
                          v-for="item in pricingSchemeOptionList"
                          :key="item.key"
                          :label="item.value"
                          :value="item.key"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
            </div>
          <div class="item_content">
            <div class="title">
              {{ $t("purchase.label.graphicInformation") }}
            </div>
            <el-row>
              <!-- 商品图片 -->
              <el-col :span="24">
                <el-form-item
                  :label="$t('purchase.label.imagesUrls')"
                  prop="imagesUrls"
                >
                  <upload-multiple
                    :tips="$t('purchase.message.pictureTip')"
                    :fileSize="10"
                    :isPrivate="`public-read`"
                    :modelValue="form.imagesUrls"
                    @update:model-value="onChangeMultiple"
                    ref="detailPicsRef"
                    :limit="6"
                    :formRef="formUpdateRef"
                    class="modify-multipleUpload"
                    name="detailPic"
                  >
                    <template #default="{ file }">点击上传</template>
                  </upload-multiple>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="item_content">
            <div class="title">
              <div class="flex_style">
                <div>{{ $t("purchase.label.supplyChainInformation") }}</div>
                <div>
                  <el-button
                    type="primary"
                    key="primary"
                    text
                    @click="setSuppliers()"
                  >
                    <span class="required_style">
                      {{ $t("purchase.button.selectSuppliers") }}
                    </span>
                  </el-button>
                </div>
              </div>
            </div>

            <el-row>
              <el-table
                v-loading="loading"
                :data="form.supplierList"
                highlight-current-row
                stripe
              >
                <el-table-column
                  :label="$t('purchase.label.supplierName')"
                  min-width="150"
                >
                  <template #default="scope">
                    <el-tooltip
                      :content="scope.row.supplierName"
                      placement="top"
                      effect="dark"
                    >
                      <span>{{ scope.row.supplierName }}</span>
                    </el-tooltip>
                    <span
                      v-if="scope.row.isDefault == 1"
                      class="default-supplier"
                    >
                      {{ $t("purchase.label.defaults") }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('purchase.label.supplierCode')"
                  prop="supplierCode"
                  show-overflow-tooltip
                />
                <el-table-column
                  :label="$t('purchase.label.supplierWarehouseName')"
                  prop="supplierWarehouseName"
                  show-overflow-tooltip
                />
                <el-table-column
                  fixed="right"
                  :label="$t('common.handle')"
                  width="160"
                >
                  <template #default="scope">
                    <el-button
                      v-if="scope.row.isDefault == 0"
                      type="primary"
                      size="small"
                      link
                      @click="defaultSet(scope.$index)"
                    >
                      {{ $t("purchase.button.setDefaultSuppliers") }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      link
                      @click="handleDelete(scope.$index)"
                    >
                      {{ $t("common.delete") }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
          </div>
          <div class="item_content" style="border-bottom: unset !important">
            <div class="title">
              {{ $t("purchase.label.productStatusInformation") }}
            </div>
            <el-row>
              <el-col :span="8">
                <el-form-item
                  :label="$t('purchase.label.statusCopy')"
                  prop="status"
                >
                  <el-select
                    v-model="form.status"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    class="!w-[256px]"
                  >
                    <el-option
                      v-for="item in statusList"
                      :key="item.statusId"
                      :label="item.statusName"
                      :value="item.statusId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ $t("common.confirm") }}
        </el-button>
      </div>
      <SetSupplier
        ref="setSuppliersRef"
        v-model:visible="dialog.visible"
        :title="dialog.title"
        @on-submit="onSubmit"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "GoodsAddPurchase",
  inheritAttrs: false,
});

import type { CascaderProps } from "element-plus";
import productCategoryAPI from "@/modules/goods/api/productCategory";
import productBrandAPI from "@/modules/goods/api/productBrand";
import unitAPI from "@/modules/goods/api/unit";
import PurchaseAPI, { PurchaseFrom } from "@/modules/wms/api/purchase";
import SetSupplier from "./components/setSupplier.vue";
// import supplierAPI from "@/modules/pms/api/supplier";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore, useWarehouseStore } from "@/core/store";
import Decimal from "decimal.js";

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const submitLoading = ref(false);
const formRef = ref(ElForm);
const loading = ref(false);
const id = route.query.id;
const type = route.query.type;
const setSuppliersRef = ref();
const isRequired = ref(false);
const isNumDisabled = ref(false);
const isWeightDisabled = ref(false);
const warehouseStore = useWarehouseStore();

const warehouseName = computed(() => {
  return warehouseStore.getSelectedWarehouseName;
});
const hasProductInventoryAndAssembleTemplate = ref(false);//是否有商品库存并且有拆装模板
const hasProductInventory = ref(false);//是否有商品库存

const dialog = reactive({
  title: "",
  visible: false,
});
const formUpdateRef = ref(null);
const cascaderRef = ref();
const categoryList = ref([]);
const propsCategory: CascaderProps = {
  checkStrictly: false,
  value: "id",
  label: "categoryName",
  children: "children",
};
const standardList = ref([
  {
    key: 1,
    value: t("common.statusYesOrNo.yes"),
  },
  {
    key: 0,
    value: t("common.statusYesOrNo.no"),
  },
]);
const unitList = ref([]);
const brandList = ref([]);
const shelfLifeUnitList = ref([
  {
    key: 1,
    value: t("purchase.shelfLifeUnitList.day"),
  },
  {
    key: 2,
    value: t("purchase.shelfLifeUnitList.month"),
  },
  {
    key: 3,
    value: t("purchase.shelfLifeUnitList.year"),
  },
]);
const statusList = ref([
  {
    statusId: 1,
    statusName: t("purchase.statusList.haveBeenPutOnShelves"),
  },
  {
    statusId: 2,
    statusName: t("purchase.statusList.haveBeenGetOffShelves"),
  },
]);

const pricingSchemeOptionList = ref([
  {
    key: 0,
    value: t("purchase.pricingSchemeOptionList.firstLevelUnit"),
  },
  {
    key: 1,
    value: t("purchase.pricingSchemeOptionList.secondLevelUnit"),
  },
])
const isSKUOptionList = ref([
  {
    key: 1,
    value: t("purchase.isSKUOptionList.yes"),
  },
  {
    key: 0,
    value: t("purchase.isSKUOptionList.no"),
  },
])

const attributeTypeList = ref([
  // {
  //   id: 1,
  //   name: t("purchase.attributeTypeList.finishedGoods"),
  // },
  // {
  //   id: 2,
  //   name: t("purchase.attributeTypeList.auxiliaryMaterials"),
  // },
  // {
  //   id: 2,
  //   name: t("purchase.attributeTypeList.packagingMaterials"),
  // },
]);
const colorOption= ref([])
const sizeOption= ref([])


// 角色表单
let form = reactive<PurchaseFrom>({
  imagesUrls: [],
  conversionRelFirstNum: 1,
  shelfLifeUnit: 2,
  isDiscreteUnit: 1,
  pricingScheme: 0,
  isSku: 1
});

const rules = reactive({
  productCategory: [
    {
      required: true,
      message: t("purchase.rules.productCategory"),
      trigger: ["change", "blur"],
    },
  ],
  productName: [
    {
      required: true,
      message: t("purchase.rules.productName"),
      trigger: "blur",
    },
    {
      min: 2,
      max: 120,
      message: t("purchase.rules.productNameFormat"),
      trigger: "blur",
    },
  ],
  // isStandard: [
  //   {
  //     required: true,
  //     message: t("purchase.rules.isStandard"),
  //     trigger: ["change", "blur"],
  //   },
  // ],
  productUnitId: [
    {
      required: true,
      message: t("purchase.rules.productUnit"),
      trigger: ["change", "blur"],
    },
  ],
  conversionRelFirstNum: [
    {
      required: true,
      message: t("purchase.rules.conversionRelFirstNum"),
      trigger: "blur",
    },
    {
      pattern: /(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/,
      message: t("purchase.rules.conversionRelFirstNumFormat"),
      trigger: "blur",
    },
  ],
  conversionRelSecondNum: [
    {
      required: true,
      message: t("purchase.rules.conversionRelSecondNum"),
      trigger: ["blur", "change"],
    },
    {
      pattern: /^[0-9]\d{0,13}(\.\d{1,9})?$/,
      message: t("purchase.rules.conversionRelSecondNumFormat"),
      trigger: "blur",
    },
  ],
  conversionRelSecondUnitId: [
    {
      required: true,
      message: t("purchase.rules.unitNameCopy"),
      trigger: ["change", "blur"],
    },
  ],
  length: [
    {
      pattern: /(^[1-9]\d{0,7}$)/,
      message: t("purchase.rules.lengthFormat"),
      trigger: "blur",
    },
  ],
  width: [
    {
      pattern: /(^[1-9]\d{0,7}$)/,
      message: t("purchase.rules.widthFormat"),
      trigger: "blur",
    },
  ],
  height: [
    {
      pattern: /(^[1-9]\d{0,7}$)/,
      message: t("purchase.rules.heightFormat"),
      trigger: "blur",
    },
  ],
  volume: [
    {
      pattern: /^[0-9]\d{0,7}(\.\d{1,6})?$/,
      message: t("purchase.rules.volumeFormat"),
      trigger: "blur",
    },
  ],
  weight: [
    {
      required: false,
      message: t("purchase.rules.weight"),
      trigger: ["change", "blur"],
    },
    {
      pattern: /^(?!0\d)(\d{1,14}(\.\d{1,3})?|0\.\d{1,3})$/,
      message: t("purchase.rules.weightFormat"),
      trigger: "blur",
    },
  ],
  isSku: [
    {
      required: true,
      message: t("purchase.rules.isSku"),
      trigger: ["change", "blur"],
    },
  ],
  lossRatio: [
    {
      required: false,
      message: t("purchase.rules.lossRatio"),
      trigger: ["change", "blur"],
    },
    {
      pattern: /^(([1-9]?\d{0,1}(\.\d{1,2})?)|100|100\.(0){0,2})$/,
      message: t("purchase.rules.lossRatioFormat"),
      trigger: "blur",
    },
  ],
  shelfLife: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{0,50}$/,
      message: t("purchase.rules.shelfLifeFormat"),
      trigger: "blur",
    },
  ],
  storageCondition: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{0,50}$/,
      message: t("purchase.rules.storageConditionFormat"),
      trigger: "blur",
    },
  ],
  status: [
    {
      required: true,
      message: t("purchase.rules.status"),
      trigger: ["change", "blur"],
    },
  ],
  imagesUrls: [
    {
      required: false,
      message: t("purchase.rules.imagesUrls"),
      trigger: ["change", "blur"],
    },
  ],
  saleAmount: [
    {
      required: true,
      message: t("purchase.rules.saleAmount"),
      trigger: "blur",
    },
    {
      pattern: /^(-?0(?:\.\d{1,2})?|[1-9]\d{0,7}(?:\.\d{1,2})?)$/,
        // /(^[1-9](\d{1,7})?(\.\d{1,2})?$)|(^0\.[1-9]\d{0,1}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
      message: t("purchase.rules.saleAmountFormat"),
      trigger: "blur",
    },
  ],
  saleAmountRadio: [
    {
      required: true,
      message: t("purchase.rules.saleAmountRadio"),
      trigger: "blur",
    },
    {
      pattern: /^(([1-9]?\d{0,1}(\.\d{1,2})?)|100|100\.(0){0,2})$/,
      message: t("purchase.rules.saleAmountRadioFormat"),
      trigger: "blur",
    },
  ],
  isDiscreteUnit: [
    {
      required: true,
      message: t("purchase.rules.isDiscreteUnit"),
      trigger: ["change"],
    },
  ],
  pricingScheme: [
    {
      required: true,
      message: t("purchase.rules.pricingScheme"),
      trigger: ["change", "blur"],
    },
  ]
});

function onChangeMultiple(val: any) {
  form.imagesUrls = val ? val : "";
  if (val && val[0] && val[0].fileName) {
    form.mainImageUrl = val[0]?.fileName;
  } else {
    form.mainImageUrl = "";
  }

  if (form.imagesUrls && form.imagesUrls.length > 0) {
    formRef.value.clearValidate("imagesUrls"); //清除图片校验文字
  }
}

const firstLevelUnitName = ref('')
const secondLevelUnitName = ref('')

// 单位变化时重量 数量的变化
function conversionRelSecondUnitChange() {
  //转换关系--获取单位name
  const selectedOption: any = unitList.value.find(
    (item: any) => item.id === form.conversionRelSecondUnitId
  );
  const productUnitOption: any = unitList.value.find(
    (item: any) => item.id === form.productUnitId
  );
  const selectedLabel = selectedOption?.name || ""; //最小单位
  const productUnitName = productUnitOption?.name || ""; //单位
  firstLevelUnitName.value = productUnitName;
  secondLevelUnitName.value = selectedLabel;

  // 单位变化时重量变化
  if (productUnitName) {
    // 如果单位是吨
    if (["吨", "t", "T"].includes(productUnitName)) {
      form.weight = 1000;
      isWeightDisabled.value = true;
    } else if (["kg", "KG", "Kg", "千克", "公斤"].includes(productUnitName)) {
      //如果单位是kg
      form.weight = 1;
      isWeightDisabled.value = true;
    } else if (["斤"].includes(productUnitName)) {
      //如果单位是斤
      form.weight = 0.5;
      isWeightDisabled.value = true;
    } else if (["g", "克", "G"].includes(productUnitName)) {
      //如果单位是g
      form.weight = 0.001;
      isWeightDisabled.value = true;
    } else {
      form.weight = undefined;
      isWeightDisabled.value = false;
    }
  }

  // 单位与最小单位的转换关系为重量类型时：1顿=1000kg=2000斤=1000000g
  if (selectedLabel && productUnitName) {
    // 如果单位是吨
    if (["吨", "t", "T"].includes(productUnitName)) {
      if (["kg", "KG", "Kg", "千克", "公斤"].includes(selectedLabel)) {
        //最小单位是kg
        form.conversionRelSecondNum = 1000;
        isNumDisabled.value = true;
      } else if (["斤"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 2000;
        isNumDisabled.value = true;
      } else if (["g", "克", "G"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 1000000;
        isNumDisabled.value = true;
      } else if (["吨", "t", "T"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 1;
        isNumDisabled.value = true;
      } else {
        isNumDisabled.value = false;
      }
    } else if (["kg", "KG", "Kg", "千克", "公斤"].includes(productUnitName)) {
      //如果单位是kg
      if (["吨", "t", "T"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 0.001;
        isNumDisabled.value = true;
      } else if (["斤"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 2;
        isNumDisabled.value = true;
      } else if (["g", "克", "G"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 1000;
        isNumDisabled.value = true;
      } else if (["kg", "KG", "Kg", "千克", "公斤"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 1;
        isNumDisabled.value = true;
      } else {
        isNumDisabled.value = false;
      }
    } else if (["斤"].includes(productUnitName)) {
      //如果单位是斤

      if (["吨", "t", "T"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 0.0005;
        isNumDisabled.value = true;
      } else if (["kg", "KG", "Kg", "千克", "公斤"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 0.5;
        isNumDisabled.value = true;
      } else if (["g", "克", "G"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 500;
        isNumDisabled.value = true;
      } else if (["斤"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 1;
        isNumDisabled.value = true;
      } else {
        isNumDisabled.value = false;
      }
    } else if (["g", "克", "G"].includes(productUnitName)) {
      //如果单位是g
      if (["吨", "t", "T"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 0.000001;
        isNumDisabled.value = true;
      } else if (["kg", "KG", "Kg", "千克", "公斤"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 0.001;
        isNumDisabled.value = true;
      } else if (["斤"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 0.002;
        isNumDisabled.value = true;
      } else if (["g", "克", "G"].includes(selectedLabel)) {
        form.conversionRelSecondNum = 1;
        isNumDisabled.value = true;
      } else {
        isNumDisabled.value = false;
      }
    } else {
      isNumDisabled.value = false;
    }
  }

  //规格=数量最小单位/单位
  if (selectedLabel && form.conversionRelSecondNum && form.productUnitId) {
    form.productSpec = `${form.conversionRelSecondNum}${selectedLabel}/${productUnitName}`; //规格
  }

  //最小单位和数量改变时，重量的变化
  if (selectedLabel && form.conversionRelSecondNum) {
    if (["kg", "KG", "Kg", "千克", "公斤"].includes(selectedLabel)) {
      form.weight = parseFloat(
        new Decimal(1).times(form.conversionRelSecondNum).toNumber().toFixed(3)
      );
      isWeightDisabled.value = true;
      isRequired.value = true;
    } else if (["吨", "t", "T"].includes(selectedLabel)) {
      form.weight = parseFloat(
        new Decimal(1000)
          .times(form.conversionRelSecondNum)
          .toNumber()
          .toFixed(3)
      );
      isWeightDisabled.value = true;
      isRequired.value = true;
    } else if (["斤"].includes(selectedLabel)) {
      form.weight = parseFloat(
        new Decimal(0.5)
          .times(form.conversionRelSecondNum)
          .toNumber()
          .toFixed(3)
      );
      isWeightDisabled.value = true;
      isRequired.value = true;
    } else if (["g", "克", "G"].includes(selectedLabel)) {
      form.weight = parseFloat(
        new Decimal(0.001)
          .times(form.conversionRelSecondNum)
          .toNumber()
          .toFixed(3)
      );
      isWeightDisabled.value = true;
      isRequired.value = true;
    } else {
      isRequired.value = false;
      // form.weight = undefined;
      // isWeightDisabled.value = false;
    }
  } else {
    isRequired.value = false;
    form.productSpec = "";
    // form.weight = undefined;
    // isWeightDisabled.value = false;
  }
}

//安全库存单位
const safetyStockUnit = computed(() => {
  return form.isDiscreteUnit ? firstLevelUnitName.value : secondLevelUnitName.value
})

function handleVolume() {
  if (!form.length || !form.width || !form.height) {
    form.volume = undefined;
    return;
  }
  const volumeInM3 = (form.length * form.width * form.height) / 1000000;
  form.volume = preciseRound(volumeInM3, 6);
}
function preciseRound(num: number, decimals: number = 3): number {
  if (isNaN(num)) return NaN;
  const factor = Math.pow(10, decimals);
  return Math.round((num + Number.EPSILON) * factor) / factor;
}

function changeSaleStatus() {
  form.saleAmount = undefined;
  form.saleAmountRadio = undefined;
}

/** 查询商品分类列表 */
function queryManagerCategoryList(id?: any) {
  productCategoryAPI.queryCategoryTreeList({}).then((data: any) => {
    categoryList.value = data;
  });
}

/** 查询单位列表 */
function getUnitListList() {
  unitAPI.getUnitList().then((data: any) => {
    unitList.value = data;
  });
}

/** 查询品牌列表 */
function queryBrandList() {
  productBrandAPI.queryBrandList().then((data: any) => {
    brandList.value = data;
  });
}

/** 添加分类 */
// function addCategory() {
//   dialog.title = t("product.button.addCategory");
//   dialog.visible = true;
// }

/** 假选择供应商弹窗 */
function setSuppliers() {
  dialog.title = t("purchase.title.selectSuppliers");
  // setSuppliersRef.value.querySupplierAll();
  setSuppliersRef.value.setFormData({
    supplierType: 4,
    productIds: [],
    supplierList: [],
  });
  dialog.visible = true;
}

function handleChange() {
  if (cascaderRef.value.getCheckedNodes()) {
    let valueArr = cascaderRef.value.getCheckedNodes()[0].pathValues;
    form.firstCategoryId = valueArr[0];
    form.secondCategoryId = valueArr[1];
    form.thirdCategoryId = valueArr[2];
    form.productCategory = valueArr;
  }
}

function onSubmit(data: any) {
  if (data.length > 0) {
    data.forEach((item: any) => {
      item.isDefault = 0;
    });
  }
  if (form.supplierList && form.supplierList.length > 0) {
    let arr = data.concat(form.supplierList);
    let uniqueArr: any = [
      ...new Map(arr.map((item: any) => [item.supplierCode, item])).values(),
    ];
    form.supplierList = uniqueArr;
  } else {
    form.supplierList = data;
  }
}

/** 假删除供应商 */
function handleDelete(index: number) {
  loading.value = true;
  form.supplierList?.splice(index, 1);
  ElMessage.success(t("purchase.message.deleteSucess"));
  loading.value = false;
}

/** 假设置默认供应商 */
function defaultSet(index?: number) {
  loading.value = true;
  const i: any = form.supplierList?.findIndex((item) => item.isDefault == 1);
  if (i >= 0) {
    form.supplierList[i].isDefault = 0;
  }
  form.supplierList[index].isDefault = 1;
  ElMessage.success(t("purchase.message.setDefaultSuppliersSucess"));
  loading.value = false;
  console.log("===form.supplierList===" + form.supplierList);
}

async function handleClose() {
  await tagsViewStore.delView(route);
  router.push({
    path: "/wms/products",
  });
}

function handleSubmit() {
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    submitLoading.value = true;
    // form.imagesUrls = JSON.stringify(form.imagesUrls);
    let params: any = {
      ...form,
    };
    params.attributeTypeName= attributeTypeList.value?.find((item:  any) => item.value === form.attributeType)?.label || '';
      params.colorName= colorOption.value?.find((item:  any) => item.value === form.color)?.label || '';
      params.sizeName= sizeOption.value?.find((item:  any) => item.value === form.size)?.label || '';
    if(!form.imagesUrls?.length){//没有选商品图片，默认给生产地址图片
      const defaultImagesUrls: any = [{
        bucket: "supply-erp-read",
        fileName: "https://supply-erp-read.oss-cn-shanghai.aliyuncs.com/supply-erp-read/image/6d763e9912d323e1bd068c7ca64f007b.png",
        originalFileName: "6d763e9912d323e1bd068c7ca64f007b.png",
      }]
      params.imagesUrls = JSON.stringify(defaultImagesUrls);
      params.mainImageUrl = defaultImagesUrls[0].fileName;
    }else{
      params.imagesUrls = JSON.stringify(form.imagesUrls);
    }

    delete params.productCategory;
    console.info(params);

    if (type == "add") {
      delete params.id;
      PurchaseAPI.addPurchase(params)
        .then((data) => {
          ElMessage.success(t("purchase.message.addSucess"));
          handleClose();
        })
        .finally(() => {
          submitLoading.value = false;
        });
    } else {
      PurchaseAPI.updatePurchase(params)
        .then((data) => {
          ElMessage.success(t("purchase.message.editSucess"));
          handleClose();
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}

/** 查询商品详情列表 */
function queryPurchaseDetail() {
  loading.value = true;
  let params: any = {
    id: id,
  };
  PurchaseAPI.queryPurchaseDetail(params)
    .then((data) => {
      Object.assign(form, data);
      form.productCategory = [
        form.firstCategoryId,
        form.secondCategoryId,
        form.thirdCategoryId,
      ];
      if (form.imagesUrls && typeof form.imagesUrls === "string") {
        form.imagesUrls = JSON.parse(form.imagesUrls);
      }

      // if (form.productUnitId) {
      //   const selectedOption = unitList.value.find(
      //     (item: any) => item.id === form.productUnitId
      //   );
      //   if (!selectedOption) {
      //     form.productUnitId = "";
      //   }
      // }
      // if (form.conversionRelSecondUnitId) {
      //   const conversionSelectedOption = unitList.value.find(
      //     (item: any) => item.id === form.conversionRelSecondUnitId
      //   );
      //   if (!conversionSelectedOption) {
      //     form.conversionRelSecondUnitId = "";
      //   }
      // }
      // if (form.productBrandId) {
      //   const brandOption = brandList.value.find(
      //     (item: any) => item.id === form.productBrandId
      //   );
      //   if (!brandOption) {
      //     form.productBrandId = "";
      //   }
      // }
      conversionRelSecondUnitChange();
      form.productSpec = data.productSpec;
      form.weight = data.weight;
      /*编辑状态判断商品库存数量是否可以编辑商品判断*/
      if (type === "edit") {
        queryProductInventoryInfo()
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

// onMounted(async () => {
//   await getUnitListList();
//   await queryBrandList();
//   await queryManagerCategoryList();
//   if (type == "edit") {
//     queryPurchaseDetail();
//   }
// });
/**/

/**编辑状态时，有商品库存数量字段不能编辑*/
const productInventoryDisabled = computed(() => {
  return type === "add" ? false : !!hasProductInventory.value;
})

/**编辑状态时，有商品库存和拆装模板字段不能编辑*/
const productInventoryAndTemplateDisabled = computed(() => {
  return type === "add" ? false : !!hasProductInventoryAndAssembleTemplate.value;
})

/** 编辑时，字段置灰逻辑添加*/
function queryProductInventoryInfo() {
  loading.value = true;
  let params: any = {
    productCode: form?.productCode,
  };
  /*查询商品库存并且有拆装模板*/
  PurchaseAPI.queryProductInventoryAndTemplateInfo(params)
    .then((data) => {
      hasProductInventoryAndAssembleTemplate.value = !!data
    })
    .finally(() => {
      loading.value = false;
    });

  /*查询商品库存*/
  PurchaseAPI.queryProductInventoryInfo(params)
    .then((data) => {
      hasProductInventory.value = !!data
    })
    .finally(() => {
      loading.value = false;
    });
}

/**
 * 商品属性
 */
function getProductAttributes() {
    let params = {
        page: 1,
        limit: 1000,
        enableStatus: 1,

    };
    let paramsAttributes ={
        ...params,
        ...{fieldCode: "attributes"}
    }
    PurchaseAPI.queryProductAttributes(paramsAttributes).then((data: any) => {
         const {records}=data;
             attributeTypeList.value=records.map((item:any)=>{
                 return {
                     label: item.typeName,
                     value: item.id
                 }
            })
    });
    let paramsColor ={
        ...params,
        ...{fieldCode: "colour"}
    }
    PurchaseAPI.queryProductAttributes(paramsColor).then((data: any) => {
        const {records}=data;
        colorOption.value=records.map((item:any)=>{
            return {
                label: item.typeName,
                value: item.id
            }
        })

    });
    let paramsSize ={
        ...params,
        ...{fieldCode: "size"}
    }
    PurchaseAPI.queryProductAttributes(paramsSize).then((data: any) => {
        const {records}=data;
        sizeOption.value=records.map((item:any)=>{
            return {
                label: item.typeName,
                value: item.id
            }
        })
    });

}

onMounted(async () => {
  try {
    await Promise.all([
      getUnitListList(),
      queryBrandList(),
      queryManagerCategoryList(),
        getProductAttributes()
    ]);

    if (type === "edit") {
      queryPurchaseDetail();
    }
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
});
</script>
<style scoped lang="scss">
.addPurchase {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 13px 21px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 20px 9px 20px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
    .flex_style {
      display: flex;
      justify-content: space-between;
      width: 100%;
      align-items: center;
    }
    .equal {
      height: 32px;
      line-height: 32px;
      padding: 0px 8px;
      margin-bottom: 18px;
    }
    .button-add {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary);
    }
    .default-supplier {
      margin-left: 8px;
      padding: 2px 7px;
      background: #fe8200;
      border-radius: 4px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      line-height: 16px;
      text-align: left;
      font-style: normal;
    }
  }
  .mb-20px {
    margin-bottom: 20px;
  }
}
</style>
