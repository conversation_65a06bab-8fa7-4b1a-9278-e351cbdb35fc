<template>
  <div class="app-container">
    <div class="container-wrapper" v-loading="loading">
      <div class="page-title">
        <div class="purchase-title">
          <div @click="handleClose()" class="cursor-pointer mr8px">
            <el-icon>
              <Back />
            </el-icon>
          </div>
          <div>
            <span v-if="pageType === 'detail'">
              {{ $t("pickOrder.title.sortingDetail") }}
            </span>
            <span v-else>{{ $t("pickOrder.title.editSorting") }}</span>
          </div>
          <div></div>
        </div>
      </div>
      <div class="page-content">
        <div class="title-lable">
          <div class="title-line"></div>
          <div class="title-content">
            {{ $t("pickOrder.title.basicInformation") }}
          </div>
        </div>
        <el-row>
          <el-col :span="6">
            <el-form-item :label="$t('pickOrder.label.sortingCode')">
              {{ formData.sortingCode ? formData.sortingCode : "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('pickOrder.label.receiptCode')">
              {{ formData.receiptCode ? formData.receiptCode : "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('pickOrder.label.receiptTotalQuantity')">
              <span>
                {{
                  formData.receiptProductQty ? formData.receiptProductQty : "-"
                }}
              </span>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item
              :label="$t('pickOrder.label.receiptTotalWeight') + '(kg)'"
            >
              {{
                formData.receiptProductWeight
                  ? formData.receiptProductWeight
                  : "-"
              }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('pickOrder.label.receiptStatus')">
              {{ filterReceiptStatus(formData.receiptStatus) }}
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="$t('pickOrder.label.receiveName')">
              {{ formData.handleUserName ? formData.handleUserName : "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('pickOrder.label.receiveTime')">
              {{
                formData.handleTime
                  ? parseDateTime(formData.handleTime, "dateTime")
                  : "-"
              }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('pickOrder.label.sortingStatus')">
              {{ filterSortingStatus(formData.sortingStatus) }}
            </el-form-item>
          </el-col>
          <template v-if="pageType === 'detail'">
            <el-col :span="6">
              <el-form-item :label="$t('pickOrder.label.sortingTotalQuantity')">
                {{
                  formData.sortingProductQty ? formData.sortingProductQty : "-"
                }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :label="$t('pickOrder.label.sortingTotalWeight') + '(kg)'"
              >
                {{
                  formData.sortingProductWeight
                    ? formData.sortingProductWeight
                    : "-"
                }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('pickOrder.label.lastPrintUserName')">
                <span>
                  {{
                    formData.lastPrintUserName
                      ? formData.lastPrintUserName
                      : "-"
                  }}
                </span>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item :label="$t('pickOrder.label.lastPrintTime')">
                {{
                  formData.lastPrintTime
                    ? parseDateTime(formData.lastPrintTime, "dateTime")
                    : "-"
                }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :label="$t('pickOrder.label.inventoryTotalQuantity')"
              >
                {{
                  formData.totalInWarehouseQty
                    ? formData.totalInWarehouseQty
                    : "-"
                }}
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item
                :label="$t('pickOrder.label.inventoryTotalWeight') + '(kg)'"
              >
                {{
                  formData.totalInWarehouseWeight
                    ? formData.totalInWarehouseWeight
                    : "-"
                }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('pickOrder.label.inventoryStatus')">
                {{ filterInWarehouseStatus(formData.inWarehouseStatus) }}
              </el-form-item>
            </el-col>
          </template>
        </el-row>
        <div class="line"></div>
        <div class="title-lable">
          <div class="title-line"></div>
          <div class="title-content">
            {{ $t("pickOrder.title.sortingTable") }}
          </div>
        </div>
      </div>

      <div class="table-container">
        <el-form :model="formData" :rules="rules" ref="formRef">
          <el-table
            :data="formData.detailVOList"
            :span-method="spanMethod"
            default-expand-all
            row-class-name="table-row"
            style="border: none; --el-table-border-color: none"
          >
            <template #empty>
              <Empty />
            </template>

            <el-table-column type="expand" width="1">
              <template #default="props">
                <el-table
                  :data="props.row.sortingResultList"
                  class="table-children"
                  :show-header="false"
                >
                  <template #append>
                    <div class="table-summaries">
                      <div class="summaries">
                        {{ $t("pickOrder.label.summation") }}：
                      </div>
                      <div class="column">
                        {{ $t("pickOrder.label.sortingAfterQty") }}:{{
                          calcAmount(props.row.sortingResultList)
                        }}
                      </div>
                      <div class="column">
                        {{ $t("pickOrder.label.sortingAfterWeight") }}(kg):{{
                          calcWeight(props.row.sortingResultList)
                        }}
                      </div>
                    </div>
                  </template>
                  <el-table-column
                    type="index"
                    :label="$t('common.sort')"
                    width="60"
                    prop="index"
                  />
                  <el-table-column
                    :label="$t('pickOrder.label.goodsCategory')"
                    prop="firstCategoryName"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      {{ scope.row.firstCategoryName }} /
                      {{ scope.row.secondCategoryName }} /
                      {{ scope.row.thirdCategoryName }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('pickOrder.label.goods')"
                    prop="productName"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      {{ scope.row.productCode }} |{{ scope.row.productName }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('pickOrder.label.specification')"
                    prop="productSpec"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    :label="$t('pickOrder.label.units')"
                    prop="productUnit"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    :label="$t('pickOrder.label.sortingAfterQty')"
                    prop="sortingAfterQty"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-form-item
                        :prop="
                          'detailVOList.' +
                          props.$index +
                          '.sortingResultList.' +
                          scope.$index +
                          '.sortingAfterQty'
                        "
                        :rules="rules.sortingAfterQty"
                      >
                        <el-input
                          v-model="scope.row.sortingAfterQty"
                          maxlength="4"
                          :placeholder="$t('common.placeholder.inputTips')"
                          @input="(value) => onInput(value, props, scope)"
                          :disabled="pageType === 'detail'"
                        ></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('pickOrder.label.sortingAfterWeight') + '(kg)'"
                    prop="sortingAfterWeight"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-form-item>
                        <el-input
                          v-model="scope.row.sortingAfterWeight"
                          maxlength="11"
                          :placeholder="$t('pickOrder.placeholder.calcWeight')"
                          disabled
                        ></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <template v-if="pageType === 'detail'">
                    <el-table-column
                      :label="$t('pickOrder.label.lastPrintUserName')"
                      prop="lastPrintUserName"
                      show-overflow-tooltip
                    ></el-table-column>

                    <el-table-column
                      :label="$t('pickOrder.label.lastPrintTime')"
                      prop="lastPrintTime"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{ parseDateTime(scope.row.lastPrintTime, "dateTime") }}
                      </template>
                    </el-table-column>

                    <el-table-column
                      fixed="right"
                      :label="$t('pickOrder.label.ysnCode')"
                      width="160"
                    >
                      <template #default="scope">
                        <el-button
                          type="primary"
                          link
                          @click="handleDetail(scope.row)"
                        >
                          {{ $t("pickOrder.button.actionView") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </template>
                  <template v-else>
                    <el-table-column
                      fixed="right"
                      :label="$t('pickOrder.label.operation')"
                      width="160"
                    >
                      <template #default="scope">
                        <el-button
                          type="danger"
                          link
                          @click="handleDelete(props, scope)"
                        >
                          {{ $t("pickOrder.button.actionDelete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column type="index" :label="$t('common.sort')" width="60">
              <template #default="scope">
                <div class="table-header">
                  <div class="sort">
                    {{
                      scope.$index >= 9
                        ? scope.$index + 1
                        : "0" + (scope.$index + 1)
                    }}
                  </div>
                  <div class="column">
                    {{ $t("pickOrder.label.goodsCategory") }}:
                    {{ scope.row.firstCategoryName }} /
                    {{ scope.row.secondCategoryName }} /
                    {{ scope.row.thirdCategoryName }}
                  </div>
                  <div class="column">
                    {{ $t("pickOrder.label.goods") }}:
                    {{ scope.row.productCode }} |{{ scope.row.productName }}
                  </div>
                  <div class="column">
                    {{ $t("pickOrder.label.specification") }}:{{
                      scope.row.productSpec
                    }}
                  </div>
                  <div class="column">
                    {{ $t("pickOrder.label.receiptQuantity") }}:{{
                      scope.row.sortingBeforeQty
                    }}
                  </div>
                  <div class="column">
                    {{ $t("pickOrder.label.receiptWeight") }}(kg):{{
                      scope.row.sortingBeforeWeight
                    }}
                  </div>
                  <div
                    v-if="pageType !== 'detail'"
                    class="action"
                    @click="handleAddGoods(scope)"
                  >
                    {{ $t("pickOrder.button.addGoods") }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.goodsCategory')"
              prop="firstCategoryName"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.goods')"
              prop="productName"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.specification')"
              prop="productSpec"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.units')"
              prop="productUnit"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.sortingAfterQty')"
              prop="sortingAfterQty"
              show-overflow-tooltip
              label-class-name="label-required"
            ></el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.sortingAfterWeight') + '(kg)'"
              prop="sortingAfterWeight"
              show-overflow-tooltip
            ></el-table-column>
            <template v-if="pageType === 'detail'">
              <el-table-column
                :label="$t('pickOrder.label.lastPrintUserName')"
                prop="lastPrintUserName"
                show-overflow-tooltip
              ></el-table-column>

              <el-table-column
                :label="$t('pickOrder.label.lastPrintTime')"
                prop="lastPrintTime"
                show-overflow-tooltip
              ></el-table-column>

              <el-table-column
                fixed="right"
                :label="$t('pickOrder.label.ysnCode')"
                width="160"
              ></el-table-column>
            </template>
            <template v-else>
              <el-table-column
                fixed="right"
                :label="$t('pickOrder.label.operation')"
                width="160"
              ></el-table-column>
            </template>
          </el-table>
        </el-form>
      </div>
      <div class="page-footer" v-if="pageType !== 'detail'">
        <el-button @click="handleClose()">{{ $t("common.cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ $t("pickOrder.button.confirmSorting") }}
        </el-button>
      </div>
    </div>

    <DetailList
      ref="detailListRef"
      v-model:dialog-visible="detailDialog.visible"
      :title="detailDialog.title"
    />
    <AddProduct
      ref="addProductRef"
      v-model:visible="addProductDialog.visible"
      :title="addProductDialog.title"
      @onSubmit="handleSubmitProduct"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
    name: "EditPickOrder",
    inheritAttrs: false,
});

import { useTagsViewStore, useUserStore } from "@/core/store";
import {
  changeDateRange,
  convertToTimestamp,
  parseDateTime,
  isEmpty,
} from "@/core/utils";

import API from "@/modules/wms/api/pickOrder";

import DetailList from "./components/detailList.vue";
import AddProduct from "./components/addProduct.vue";

const { t } = useI18n();

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();

const state = reactive({
  loading: false,
  query: route.query,
  pageType: route.query?.type,
  formRef: null,
  formData: {},
  sortingStatusOption: [
    { label: t("pickOrder.label.sortingStatusOption[0]"), value: 0 },
    { label: t("pickOrder.label.sortingStatusOption[1]"), value: 1 },
    { label: t("pickOrder.label.sortingStatusOption[2]"), value: 2 },
  ],
  inWarehouseStatusOption: [
    { label: t("pickOrder.label.inventoryStatusOption[0]"), value: 0 },
    { label: t("pickOrder.label.inventoryStatusOption[1]"), value: 1 },
    { label: t("pickOrder.label.inventoryStatusOption[2]"), value: 2 },
  ],
  receiptStatusOption: [
    { label: t("pickOrder.label.receiptStatusOption[0]"), value: 0 },
    { label: t("pickOrder.label.receiptStatusOption[1]"), value: 1 },
  ],

  detailListRef: null,
  detailDialog: {
    title: "",
    visible: false,
  },
  addProductRef: null,
  addProductDialog: {
    title: "",
    visible: false,
  },
  currentOperationIndex: -1,
  originalData: {},
  backState: '',
}) as any;

const {
  loading,
  query,
  pageType,
  formRef,
  formData,
  sortingStatusOption,
  inWarehouseStatusOption,
  receiptStatusOption,

  detailListRef,
  detailDialog,
  addProductRef,
  addProductDialog,
  currentOperationIndex,
  originalData,
  backState,
} = toRefs(state);

const rules = reactive({
  sortingAfterQty: [
    {
      required: true,
      message: `${t("common.placeholder.inputTips")}`,
      trigger: "blur",
    },
    {
      pattern: /^[1-9]\d{0,3}$/,
      message: `${t("pickOrder.rules.positiveInteger")}`,
      trigger: "blur",
    },
  ],
});

/**
 * 详情
 * @param row
 */
function handleDetail(row: any) {
  detailDialog.value.visible = true;
  detailDialog.value.title = `${t("pickOrder.title.ynsCodeTitle")} - ${row.productCode} | ${row.productName}`;
  let params = {
    resultId: row.id,
  };
  detailListRef.value.handleQuery(params);
}

/**
 * 查询详情
 */
function queryDetail() {
  loading.value = true;
  let sortingCode = query.value.sortingCode;
  API.queryDetail(sortingCode)
    .then((data: any) => {
      formData.value = data;
      originalData.value = JSON.parse(JSON.stringify(data));
    })
    .finally(() => {
      loading.value = false;
    });
}

/**
 * 合并列
 * @param row
 * @param column
 * @param rowIndex
 * @param columnIndex
 */
function spanMethod({ row, column, rowIndex, columnIndex }: any) {
  if (columnIndex === 1) {
    return {
      rowspan: 1,
      colspan: 99,
    };
  } else {
    return {
      rowspan: 0,
      colspan: 0,
    };
  }
}

/**
 * 关闭窗口
 * @param type
 */

async function handleClose(type?:string) {
    if (type){
        backState.value = type;
    }else {
        backState.value = 'back';
    }
    // await tagsViewStore.delView(route);
    router.go(-1);
}

/**
 *  确认分拣
 */
function handleSubmit() {
  console.log(formData.value);

  if (calcTotalQty() > 5000) {
    return ElMessage.error(
      t("pickOrder.rules.totalNumberLess", { message: 5000 })
    );
  }

  formRef.value.validate((valid: any) => {
    if (valid) {
      const detailList = formData.value?.detailVOList?.map((item: any) => ({
        sortingDetailId: item.id,
        resultList: item?.sortingResultList
          ?.filter((sub: any) => sub.sortingAfterQty > 0)
          .map((sub: any) => ({
            sortingAfterQty: sub.sortingAfterQty,
            productCode: sub.productCode,
          })),
      }));

      const isEmptyProduct = detailList.every((item: any) => { // 判断列表中有没有存在未分拣的商品
        const resultList = item.resultList;
        return resultList && resultList.length > 0;
      });

      if (!isEmptyProduct) {
        return ElMessage.error(t("pickOrder.message.isEmptyProduct"));
      }
      console.log(detailList);

      let params = {
        sortingCode: query.value.sortingCode,
        detailList: detailList,
      };
      console.log(params);

      loading.value = true;

      API.saveOrder(params)
        .then((res: any) => {
          ElMessage.success(t("pickOrder.message.actionSucess"));
          handleClose('submit');
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

/**
 * 删除
 * @param props
 * @param scope
 */
function handleDelete(props: any, scope: any) {
  formData.value?.detailVOList[props.$index]?.sortingResultList?.splice(
    scope.$index, 1
  );
}

/**
 * 输入计算重量
 * @param val
 * @param props
 * @param scope
 */
function onInput(val: any, props: any, scope: any) {
  formData.value.detailVOList[props.$index].sortingResultList[
    scope.$index
  ].sortingAfterWeight = val
    ? (Number(val) * Number(scope.row?.productWeight || 0)).toFixed(3)
    : "";
}

/**
 *  分拣状态
 * @param val
 */
function filterSortingStatus(val: any) {
  if (!isEmpty(val)) {
    return (
      sortingStatusOption.value.find((item: any) => item.value === val)
        ?.label || ""
    );
  } else {
    return "-";
  }
}

/**
 * 领单状态
 * @param val
 */
function filterReceiptStatus(val: any) {
  if (!isEmpty(val)) {
    return (
      receiptStatusOption.value.find((item: any) => item.value === val)
        ?.label || ""
    );
  } else {
    return "-";
  }
}

/**
 * 入库状态
 * @param val
 */
function filterInWarehouseStatus(val: any) {
  if (!isEmpty(val)) {
    return (
      inWarehouseStatusOption.value.find((item: any) => item.value === val)
        ?.label || ""
    );
  } else {
    return "-";
  }
}

/**
 * 计算数量
 * @param data
 * @returns {*}
 */
function calcAmount(data: any) {
  if (!data) {
    return;
  }
  let calc = data?.reduce((total: any, item: any) => {
    return Number(total) + Number(item.sortingAfterQty || 0);
  }, 0);
  return calc;
}

/**
 * 计算重量
 * @param data
 * @returns {*}
 */
function calcWeight(data: any) {
  if (!data) {
    return;
  }
  let calc = data
    ?.reduce((total: any, item: any) => {
      return Number(total) + Number(item.sortingAfterWeight || 0);
    }, 0)
    .toFixed(3);
  return calc;
}

/**
 * 计算商品总数
 * @returns {number}
 */
function calcTotalQty() {
  let total = 0;
  formData.value?.detailVOList.forEach((item: any) => {
    item?.sortingResultList?.forEach((children: any) => {
      total += Number(children?.sortingAfterQty || 0);
    });
  });
  console.log(total);
  return total;
}

/**
 * 添加商品
 */
function handleAddGoods(scope: any) {
  currentOperationIndex.value = scope.$index;
  addProductDialog.value.visible = true;
  addProductDialog.value.title = `${t("pickOrder.button.addGoods")}`;
  addProductRef.value.handleResetQuery();
}

/**
 * 选中商品
 */
function handleSubmitProduct(multipleSelection: any) {
  multipleSelection = multipleSelection.map((item: any) => {
    return {
      ...item,
      productUnit: item.productUnitName,
      productWeight: item.weight,
    };
  });
  let sortingResultList =
    formData.value?.detailVOList[currentOperationIndex.value]
      ?.sortingResultList || [];

  let multiple = multipleSelection.filter(
    (bItem: any) =>
      !sortingResultList.some(
        (aItem: any) => aItem.productCode === bItem.productCode
      )
  );

  formData.value.detailVOList[currentOperationIndex.value].sortingResultList = [
    ...multiple,
    ...sortingResultList,
  ];
  addProductDialog.value.visible = false;
}

/**
 * 对比两个对象是否相等
 * @param obj1
 * @param obj2
 */
function deepCompare(obj1: any, obj2: any) {
  return JSON.stringify(obj1) === JSON.stringify(obj2);
}

/**
 * 清除View
 */
function delView() {
    if (backState.value){
        tagsViewStore.delView(route);
    }
}

onMounted(() => {
  backState.value = '';
  queryDetail();
});

onBeforeRouteLeave((to, from, next) => {
    if (
      pageType.value === "edit" && !deepCompare(formData.value, originalData.value)
    ) {
        if (backState.value!=='submit'){
            setTimeout(() => {
                    ElMessageBox.confirm(
                      t("pickOrder.message.saveDataTips"),
                      t("common.tipTitle"),
                      {
                          confirmButtonText: t("common.confirm"),
                          cancelButtonText: t("common.cancel"),
                          type: "warning",
                      }
                    )
                      .then(() => {
                          delView()
                          next();
                      })
                      .catch(() => {
                          next(false);
                      });
            }, 200)
        }else {
            delView()
            next();
        }
    } else {
        delView()
        next();
    }
});
</script>

<style lang="scss" scoped>
.container-wrapper {
  background: #ffffff;
  border-radius: 4px;

  .page-content {
    padding-bottom: 10px;

    :deep(.el-form-item__label) {
      font-weight: 400 !important;
      font-size: 14px !important;
      color: #90979e !important;
    }
  }

  .table-container {
    padding: 0 20px;
    margin-bottom: 10px;

    .table-header {
      display: flex;
      background: #f4f6fa;
      border: 1px solid #e5e9f2;
      position: relative;

      font-weight: 600;
      font-size: 14px;
      color: #151719;
      padding: 15px;
      margin: 0 -20px 0 -20px;

      .sort {
        padding: 0 6px;
        background: #e5e7f3;
        border-radius: 4px;
        font-size: 14px;
        color: #151719;
        margin-right: 38px;
      }

      .column {
        padding-right: 50px;
      }

      .action {
        position: absolute;
        right: 100px;
        top: 15px;
        color: #762adb;
        cursor: pointer;
      }
    }

    .table-summaries {
      display: flex;
      background: #f4f6fa;
      border: 1px solid #e5e9f2;
      position: relative;
      font-weight: 600;
      font-size: 14px;
      color: #151719;
      padding: 15px;

      .sort {
        margin-right: 38px;
      }

      .column {
        padding-right: 50px;
      }
    }

    :deep(.table-row) {
      padding: 0 !important;
      margin: 0 !important;

      > td {
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
      }
    }

    :deep(.el-table__expand-icon .el-icon) {
      display: none;
    }

    :deep(.el-table__header) {
      margin-bottom: 16px;
      border: 1px solid #e5e9f2;
    }

    .table-children {
      margin-top: -20px;
      border: 1px solid #e5e9f2;
    }

    :deep(.label-required .cell) {
      &:before {
        content: "*";
        color: red;
        margin-right: 5px;
      }
    }

    :deep(.summaries) {
      font-weight: bold;
    }
  }
}
</style>
