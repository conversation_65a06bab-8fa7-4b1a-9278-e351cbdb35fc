<template>
  <div class="app-container">
    <div class="page-title">
      <el-page-header @back="handleBack">
        <template #content>
          <span class="cursor-pointer mr8px">{{ pageTitle }}</span>
        </template>
      </el-page-header>
    </div>

    <el-form ref="formRef" :model="formData" label-width="140px" class="page-content">
      <!-- 基本信息 -->
      <div class="title-label">
        <div class="title-line"></div>
        <div class="title-content">{{ t('quickWarehousing.label.basicInformation') }}</div>
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.receiptNoticeCode')+'：'" prop="receiptNoticeCode">
            {{ formData.receiptNoticeCode || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.receiptType')+'：'" prop="receiptType">
            {{ filterReceiptTypeLabel(formData.receiptType) }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.themeDesc')+'：'" prop="themeDesc">
            {{ formData.themeDesc || '-' }}
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="业务员" prop="salesmanId">
            <el-select v-model="formData.salesmanId" placeholder="请选择" class="w-full" @change="handleSalesmanChange">
              <el-option v-for="item in businessPersonList" :key="item.userId" :label="item.nickName"
                :value="item.userId" />
            </el-select>
          </el-form-item>
        </el-col> -->

        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.sourceOrderCode')+'：'" prop="sourceOrderCode">
            {{ formData.sourceOrderCode || '-' }}
          </el-form-item>
        </el-col>
        <!-- 供应商 supplierName，供应商地址，供应商联系人，供应商联系电话 -->
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.supplier')+'：'" prop="supplierName">
            {{ formData.supplierName || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.supplierAddress')+'：'" prop="supplierAddress">
            <span class="encryptBox">
              <span v-if="formData.supplierAddressShow">
                {{ formData.supplierAddressFormat }}
                <el-icon
                  @click="formData.supplierAddressShow = false"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['wms:storeManagement:quickWarehousing:eye']"
                >
                  <View />
                </el-icon>
              </span>
              <span v-else>
                {{ filterAddress(formData, 'supplier') }}
              </span>
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.supplierContact')+'：'" prop="contactPerson">
            <span class="encryptBox">
              {{ formData.supplierNameShow ? (formData.contactPerson || '-') : encryptName(formData.contactPerson) }}
              <el-icon
                @click="getSupplierRealName"
                class="encryptBox-icon"
                color="#762ADB "
                  size="16"
                v-if="!formData.supplierNameShow"
                v-hasPermEye="['wms:storeManagement:quickWarehousing:eye']"
              >
                <View />
              </el-icon>
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.supplierPhone')+'：'" prop="mobile">
            <span class="encryptBox">
              {{ formData.supplierMobilePhoneShow ? (formData.mobile ? `${formData.countryAreaCode}${formData.mobile}` : '-') : (formData.mobile ? encryptName(`${formData.countryAreaCode}${formData.mobile}`) : '-') }}
              <el-icon
                @click="getSupplierRealPhone"
                class="encryptBox-icon"
                color="#762ADB "
                  size="16"
                v-if="!formData.supplierMobilePhoneShow"
                v-hasPermEye="['wms:storeManagement:quickWarehousing:eye']"
              >
                <View />
              </el-icon>
            </span>
          </el-form-item>
        </el-col>
        <!-- 客户，客户地址，客户联系人，客户联系电话 -->
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.customer')+'：'" prop="customerName">
            {{ formData.customerName || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.customerAddress')+'：'" prop="customerAddress">
            <span class="encryptBox">
              <span v-if="formData.customerAddressShow">
                {{ formData.customerAddressFormat }}
                <el-icon
                  color="#762ADB "
                  size="16"
                  @click="formData.customerAddressShow = false"
                  class="encryptBox-icon"
                  v-hasPermEye="['wms:storeManagement:quickWarehousing:eye']"
                >
                  <View />
                </el-icon>
              </span>
              <span v-else>
                {{ filterAddress(formData, 'customer') }}
              </span>
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.customerContact')+'：'" prop="contactPerson">
            <span class="encryptBox">
              {{ formData.customerNameShow ? (formData.customerContactPerson || '-') : encryptName(formData.customerContactPerson) }}
              <el-icon
                @click="getCustomerRealName"
                class="encryptBox-icon"
                color="#762ADB "
                  size="16"
                v-if="!formData.customerNameShow"
                v-hasPermEye="['wms:storeManagement:quickWarehousing:eye']"
              >
                <View />
              </el-icon>
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.customerPhone')+'：'" prop="customerMobile">
            <span class="encryptBox">
              {{ formData.customerMobilePhoneShow ? (formData.customerMobile ? `${formData.customerCountryAreaCode}${formData.customerMobile}` : '-') : (formData.customerMobile ? encryptName(`${formData.customerCountryAreaCode}${formData.customerMobile}`) : '-') }}
              <el-icon
                @click="getCustomerRealPhone"
                class="encryptBox-icon"
                color="#762ADB "
                  size="16"
                v-if="!formData.customerMobilePhoneShow"
                v-hasPermEye="['wms:storeManagement:quickWarehousing:eye']"
              >
                <View />
              </el-icon>
            </span>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.plannedDeliveryTime')+'：'" prop="plannedDeliveryTime">
            {{ parseTime(formData.plannedDeliveryTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </el-form-item>
        </el-col>
        <!-- 合同名称，合同编号，合同分类 -->
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.contractName')+'：'" prop="contractName">
            {{ formData.contractName || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.contractCode')+'：'" prop="contractCode">
            {{ formData.contractCode || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.contractType')+'：'" prop="contractType">
            {{ filterContractTypeLabel(formData.contractType) }}
          </el-form-item>
        </el-col>
        <!-- 业务员，原单据号，商品计划总量，商品计划总转换量 -->
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.salesman')+'：'" prop="purchaseSalesPerson">
            {{ formData.purchaseSalesPerson || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.originOrderCode')+'：'" prop="originOrderCode">
            {{ formData.originOrderCode || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.expectedQty')+'：'" prop="expectedQty">
            {{ formData.expectedQty }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.expectedWeight')+'：'" prop="expectedWeight">
            {{ formData.expectedWeight }}
          </el-form-item>
        </el-col>
        <!-- 来源单据备注remark，磅单编号，入库车号，入库备注 -->
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.sourceDocumentRemark')+'：'" prop="remark">
            {{ formData.remark || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.weighbridgeNo')+'：'" prop="weighbridgeNo">
            <el-input v-model="formData.weighbridgeNo" placeholder="请输入">
              <template #append>
                <el-badge :value="calcAttachmentLength()" :offset="[10, 5]" class="item"
                          type="primary">
                  <el-button type="primary" link @click="handleAttachmentUpload(formData)">
                    {{ $t('common.uploadBtn') }}
                  </el-button>
                </el-badge>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.vehicleNo')+'：'" prop="vehicleNo">
            <el-input v-model="formData.vehicleNo" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('quickWarehousing.label.entryOrderRemark')+'：'" prop="entryOrderRemark">
            <el-input v-model="formData.entryOrderRemark" type="textarea" :rows="3" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 商品明细 -->
      <section class="flex align-center justify-between">
        <div class="title-label">
          <div class="title-line"></div>
          <div class="title-content">{{ t('quickWarehousing.label.productDetails') }}</div>
        </div>
        <!-- 采购单的商品都是【是否SKU=是】，则不显示；如果有一个【是否SKU=否】的商品，则再该商品行的右侧加上“选择商品”按钮; -->
        <!--  <el-button type="primary" @click="handleAddProduct">
          添加
        </el-button> -->
      </section>
      <!-- 转换量:用户填写入库量后根据转换关系自动计算，可修改 -->
      <!-- 入库金额:用户填写单价/入库量/入库转换量后自动计算，用户可修改，计算公式:金额(2位小数，四舍五入)=单价(4位小数)*入库量或入库转换量 -->
      <!-- 入库单价:同一商品多个库区时入库单价相同，填写一次，其他默认 -->
      <!-- 入库库区:可选择已启用库区 -->
      <!-- 必填项:入库单价/入库量/入库库区/入库金额/入库转换量 -->

      <!-- 采购单有农资类商品，非isSku：0，该商品从剩余列后都不展示，在操作列展示选择商品按钮，可以添加商品 -->
      <el-table :data="formData.productList" border style="width: 100%" class="mb-4" :span-method="handleSpanMethod"
        show-summary :summary-method="handleSummaryMethod">
        <el-table-column type="index" :label="t('quickWarehousing.label.serialNumber')" width="60" align="center" fixed="left" />
        <el-table-column :label="t('quickWarehousing.label.productInfo')" width="200" fixed="left">
          <template #default="scope">
            <div v-if="scope.row.productTypeCustom !== 'child'">
              <div>{{ t('quickWarehousing.label.productCode') }}：{{ scope.row.productCode }}</div>
              <div>{{ scope.row.productName }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.productCategory')" width="120">
          <template #default="scope">
            <span v-if="scope.row.productTypeCustom !== 'child'">{{ scope.row.fullCategoryName }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.productSpecs')" width="100">
          <template #default="scope">
            <span v-if="scope.row.productTypeCustom !== 'child'">{{ scope.row.productSpecs }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.planQty')" min-width="180">
          <template #default="scope">
            <section  v-if="scope.row.productTypeCustom !== 'child'">
              <div>计划量：{{ scope.row.productExpectQty }}</div>
              <div>计划转换量：{{ scope.row.expectedWeight }}</div>
            </section>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.remainingQty')" min-width="180">
          <template #default="scope">
            <section  v-if="scope.row.productTypeCustom !== 'child'">
              <div>剩余量：{{ scope.row.remainingInQty }}</div>
              <div>剩余转换量：{{ scope.row.remainingInWeight }}</div>
            </section>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.unitPrice')" min-width="180" align="right">
          <template #default="scope">
            <el-form-item v-if="scope.row.productTypeCustom !== 'noSkuAncestor'" class="mt15px" label-width="0px"
              :prop="'productList.' + scope.$index + '.unitPrice'" :rules="[
                {
                  required: true,
                  message: '单价不能为空',
                  trigger: ['blur', 'change'],
                },
                {
                  pattern: /^(-?[0-9][0-9]{0,6}(?:\.\d{1,4})?)$/,
                  message: '整数位限长7位，小数后4位',
                  trigger: ['blur', 'change'],
                },
              ]">
              <el-input v-model="scope.row.unitPrice" controls-position="right"
                @change="calculateAmountByUnitPrice(scope.row)"
                :disabled="scope.row.productTypeCustom === 'child'">
                <template #append>
                  <div class="text-gray-500 text-xs">{{ scope.row.pricingScheme === 0 ? '元/'+scope.row.productUnitName : '元/'+scope.row.conversionRelSecondUnitName }}</div>
                </template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.inWarehouseQty')" min-width="180" align="right">
          <template #default="scope">
            <el-form-item v-if="scope.row.productTypeCustom !== 'noSkuAncestor'" class="mt15px" label-width="0px"
              :prop="'productList.' + scope.$index + '.actualInQty'" :rules="[
                {
                  required: true,
                  message: '入库量不能为空',
                  trigger: ['blur', 'change'],
                },
                {
                  pattern: /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                  message: '整数位限长8位，小数后3位',
                  trigger: ['blur', 'change'],
                },
              ]">
              <el-input v-model="scope.row.actualInQty" :precision="3" :min="0" controls-position="right"
                @change="convertProductUnitStragery(scope.row, 'FIRST_TO_SECOND')">
                <template #append>
                  <div v-if="scope.row.isSku === 1" class="text-gray-500 text-xs">{{ scope.row.productUnitName }}</div>
                </template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.inWarehouseWeight')" min-width="180" align="right">
          <template #default="scope">
            <el-form-item v-if="scope.row.productTypeCustom !== 'noSkuAncestor'" class="mt15px" label-width="0px"
              :prop="'productList.' + scope.$index + '.actualInWeight'" :rules="[
                {
                  required: true,
                  message: '入库转换量不能为空',
                  trigger: ['blur', 'change'],
                },
              ]">
              <el-input v-model="scope.row.actualInWeight"
                @change="convertProductUnitStragery(scope.row, 'SECOND_TO_FIRST')" :precision="3" :min="0"
                controls-position="right">
                <template #append>
                  <div class="text-gray-500 text-xs">{{ scope.row.conversionRelSecondUnitName }}</div>
                </template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.inWarehouseAmount')" min-width="180" align="right">
          <template #default="scope">
            <el-form-item v-if="scope.row.productTypeCustom !== 'noSkuAncestor'" class="mt15px" label-width="0px"
              :prop="'productList.' + scope.$index + '.amount'" :rules="[
                {
                  required: true,
                  message: '入库金额不能为空',
                  trigger: ['blur', 'change'],
                },
                {
                  pattern: /^-?(0(?:\.\d{1,2})?|[1-9]\d{0,8}(?:\.\d{1,2})?)$/,
                  message: '整数位限长9位，小数后2位',
                  trigger: ['blur', 'change'],
                },
              ]">
              <el-input v-model="scope.row.amount" controls-position="right" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.inWarehouseArea')" min-width="200">
          <template #default="scope">

            <el-form-item v-if="scope.row.productTypeCustom !== 'noSkuAncestor'" class="mt15px" label-width="0px"
              :prop="'productList.' + scope.$index + '.warehouseAreaCode'" :rules="[
                {
                  required: true,
                  message: '库区不能为空',
                  trigger: ['blur', 'change'],
                },
              ]">
              <el-select v-model="scope.row.warehouseAreaCode"
              placeholder="请选择">
                <el-option v-for="item in warehouseAreaList" :key="item.areaCode"
                  :label="item.warehouseName + '|' + item.areaName" :value="item.areaCode" />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <!--运费-->
        <el-table-column :label="t('quickWarehousing.label.freight')" min-width="180">
          <template #default="scope">
            <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.shippingAmount'" :rules="[
              {
                pattern:
                  /^-?(0(?:\.\d{1,2})?|[1-9]\d{0,8}(?:\.\d{1,2})?)$/,
                message: '整数位限长9位，小数后2位',
                trigger: ['blur', 'change'],
              }
            ]
            ">
              <el-input v-model="scope.row.shippingAmount" controls-position="right" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.productPackaging')" min-width="180">
          <template #default="scope">
            <el-input v-if="scope.row.productTypeCustom !== 'noSkuAncestor'" v-model="scope.row.productPackaging"
              placeholder="请输入" />
          </template>
        </el-table-column>
        <!--备注-->
        <el-table-column :label="t('quickWarehousing.label.productRemark')" prop="remark"></el-table-column>
        <el-table-column :label="t('quickWarehousing.label.qualityCheck')" width="80" align="center" fixed="right">
          <template #default="scope">
            <el-button v-if="scope.row.productTypeCustom == 'noSkuMain' || scope.row.productTypeCustom == 'skuMain'"
              type="primary" link @click="handleQualityCheck(formData.productList, scope.row, scope.$index)">
              质检
            </el-button>
            <span v-else>
            </span>
          </template>
        </el-table-column>

        <el-table-column :label="t('quickWarehousing.label.operation')" width="100" align="center" fixed="right">
          <template #default="scope">
            <el-button v-if="scope.row.productTypeCustom === 'noSkuAncestor'" type="primary" size="small"
              @click="handleAddProduct(scope.row, scope.$index)">添加商品</el-button>
            <section v-else>
              <el-button type="primary" link size="small" v-if="scope.row.productTypeCustom !== 'noSkuAncestor'"
                @click="handleAddRow(scope.$index, scope.row)">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-button>
              <el-button type="danger" link size="small" @click="handleDeleteRow(scope.$index)"
                v-if="scope.row.productTypeCustom == 'child' || scope.row.productTypeCustom == 'noSkuMain'">
                <el-icon>
                  <Minus />
                </el-icon>
              </el-button>

            </section>

          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <!-- 底部按钮 -->
    <div class="fixed-footer">
      <el-button @click="handleBack">{{ t('quickWarehousing.button.cancel') }}</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ t('quickWarehousing.button.submit') }}
      </el-button>
    </div>

    <!-- 商品选择弹窗 -->
    <AddProduct ref="addGoodsRef" v-model:visible="productDialog.visible" :title="productDialog.title"
      @onSubmit="onProductSubmit" />

    <!-- 质检商品弹窗 -->
    <QualityCheckDialog v-model:visible="qualityDialog.visible" :product-data="qualityDialog.productData"
      @confirm="onQualityConfirm" :type="'update'"/>

    <!-- 上传弹窗 -->
    <UploadDialog ref="uploadDialogRef" v-model:visible="uploadDialog.visible" @onSubmit="onSubmitUpload" />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Plus, Minus, Edit, View } from "@element-plus/icons-vue";
import QuickWarehousingAPI, { QuickWarehousingFormData } from "@/modules/wms/api/quickWarehousing";
import AddProduct from "./components/addProduct.vue";
import QualityCheckDialog from "./components/QualityCheckDialog.vue";
import { parseTime } from "@/core/utils/index.js";
import { useQuickWarehousing } from "./composables";
import WarehouseEntryNoticeAPI from "@/modules/wms/api/warehouseEntryNotice";
import { useI18n } from "vue-i18n";
import UploadDialog from "@/modules/wms/views/storeManagement/quickWarehousing/components/uploadDialog.vue";
const { t } = useI18n();
import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
defineOptions({
  name: "QuickWarehousingWarehousing",
  inheritAttrs: false,
});

const { filterAddress, convertProductUnitStragery, calculateAmount, filterReceiptTypeLabel,
  filterContractTypeLabel } = useQuickWarehousing();

const addGoodsRef = ref();
const route = useRoute();
const router = useRouter();

const pageTitle = computed(() => {
  return "去入库";
});

const isEdit = computed<boolean>(() => {
  return route.query.type === "edit";
});

const formRef = ref();
const saving = ref(false);
const submitting = ref(false);
// 采购单有农资类商品，非isSku：0，该商品从剩余列后都不展示，在操作列展示选择商品按钮，可以添加商品
const isSkuColumn = (row: any) => {
  return row.isSku !== 0;
}
// 表单数据
const formData = reactive<QuickWarehousingFormData>({
  // 基本信息
  receiptNoticeCode: "", // 入库通知单号
  receiptType: 4, // 入库类型:1:采购入库、2:退货入库、3:调拨入库、4:直接入库、5:地头入库
  themeDesc: "", // 主题描述
  sourceOrderCode: "", // 来源单号
  // purchaseSalesPerson: "", // 采购/销售员
  plannedDeliveryTime: "", // 计划交货时间
  // status: 0, // 状态:0:草稿、1:初始 2：完结 3:取消

  // 供应商信息
  supplierCode: "", // 供应商编码
  supplierName: "", // 供应商名称

  // 客户信息
  // customerCode: "", // 客户编码
  // customerName: "", // 客户名称

  // 地址信息
  countryId: "", // 国家id
  countryName: "", // 国家名称
  countryAreaCode: "", // 国家区域代码
  provinceId: "", // 省份id
  provinceName: "", // 省名称
  cityId: "", // 城市id
  cityName: "", // 市名称
  districtId: "", // 区县id
  districtName: "", // 区县名称
  address: "", // 详细地址

  // 联系信息
  contactPerson: "", // 联系人
  mobile: "", // 手机号

  // 合同信息
  contractCode: "", // 合同编码
  contractName: "", // 合同名称
  contractType: undefined, // 合同类型

  // 业务信息
  salesmanId: "", // 业务员ID
  salesmanName: "", // 业务员姓名
  weighbridgeNo: "", // 榜单编号
  weighbridgeNoAttachment: "", // 榜单编号附件
  vehicleNo: "", // 入库车号
  remark: "", // 备注

  // 仓库信息
  /*  warehouseCode: "", // 仓库编码
   warehouseId: undefined, // 仓库id
   warehouseName: "", // 仓库名称 */

  // 金额信息
  // currency: "CNY", // 交易币种：CNY->人民币，USD->美元
  // totalAmount: 0, // 总入库金额
  // totalCostAmount: 0, // 总成本金额
  // expectedQty: 0, // 计划总数
  // expectedWeight: 0, // 计划总重量
  // actualTotalQuantity: 0, // 总入库量（实际总量）
  // actualTotalWeight: 0, // 总入库转换量（实际总数量）

  // 其他信息
  // source: 1, // 来源：1:手动创建 2:同步
  // sourceSystem: "", // 来源系统
  // productQty: 0, // 商品个数

  // 商品列表
  productList: [
    /*  {
       id: undefined,
       productCode: "", // 商品编码
       productName: "", // 商品名称
       productSpecs: "", // 规格
       productPackaging: "", // 商品包装
       
       // 分类信息
       firstCategoryId: undefined, // 一级分类id
       firstCategoryName: "", // 一级分类名称
       secondCategoryId: undefined, // 二级分类id
       secondCategoryName: "", // 二级分类名称
       thirdCategoryId: undefined, // 三级分类id
       thirdCategoryName: "", // 三级分类名称
       
       // 单位信息
       productUnitId: undefined, // 商品采购单位id
       productUnitName: "", // 商品采购单位名称
       conversionRelSecondUnitId: undefined, // 商品换算关系第二个值的单位id
       conversionRelSecondUnitName: "", // 商品换算关系第二个值的单位名称
       
       // 数量信息
       productExpectQty: 0, // 商品数量(期望)
       actualInQty: 0, // 本次实际入库数量
       inWarehouseQty: 0, // 已入库数量
       
       // 重量信息
       expectedWeight: 0, // 计划重量
       actualInWeight: 0, // 本次实际入库重量
       inWarehouseWeight: 0, // 已入库重量
       receivedWeight: 0, // 实收总重量
       weight: 0, // 重量（KG）
       
       // 价格信息
       unitPrice: 0, // 入库单价
       amount: 0, // 入库金额
       costUnitPrice: 0, // 成本单价
       costAmount: 0, // 成本金额
       currency: "CNY", // 交易币种
       
       // 仓库信息
       warehouseCode: "", // 仓库编码
       warehouseName: "", // 仓库名称
       warehouseAreaCode: "", // 仓库库区编码
       warehouseAreaName: "", // 仓库库区名称
       
       // 其他信息
       receiptNoticeCode: "", // 入库通知单号
       receiptNoticeId: undefined, // 入库通知单id
       parentId: undefined, // 父id
       isSku: 0, // 是否sku->1:是;0:否
       isDiscreteUnit: 0, // 一级单位增减->1:开启；0:关闭
       pricingScheme: 0, // 计价模式->0:一级单位;1:二级单位
       remark: "", // 备注
     }, */
  ],

  // 质检列表
  qualityInspectionList: [
    /* {
      productCode: "", // 商品编码
      deductionAmount: 0, // 扣款金额
      deductionDesc: "", // 扣款说明
      deductionAttachment: "", // 附件
      detailList: [
        {
          specification: "", // 规格
          quantity: 0, // 数量
          proportion: 0, // 占比
        }
      ],
    } */
  ],
});

// 表单验证规则
const rules = {
  receiptType: [{ required: true, message: "请选择入库类型", trigger: "change" }],
  plannedDeliveryTime: [
    { required: true, message: "请选择计划交货时间", trigger: "change" },
  ],
};
// const addProductRef = ref();
const uploadDialogRef = ref();
// const currentUploadIndex = ref<number>();
// 上传弹窗状态
const uploadDialog = reactive({
  visible: false,
});

const handleAttachmentUpload = (formData: any) => {
  uploadDialog.visible = true;
  uploadDialogRef.value?.setEditType("add");
  if (formData.weighbridgeNoAttachment) {
    // 新增快速入库单，文件返回的是数组
    if(Array.isArray(formData.weighbridgeNoAttachment)){
      uploadDialogRef.value?.setFormData(formData.weighbridgeNoAttachment);
    }else{
      // 编辑快速入库单，文件返回的是序列后的对象数组
      uploadDialogRef.value?.setFormData(JSON.parse(formData.weighbridgeNoAttachment));
    }
  }
}
const onSubmitUpload = (data: any) => {
  formData.weighbridgeNoAttachment = data;
}
// 基础数据
const businessPersonList = ref<any[]>([]);
const supplierList = ref<any[]>([]);
const warehouseAreaList = ref<any[]>([]);

// 弹窗控制
const productDialog = reactive({
  visible: false,
  title: "选择商品",
});

const qualityDialog = reactive({
  visible: false,
  productData: {},
});

const calculateAmountByUnitPrice = (row: any) => {
  // 找到同productCode商品，重新计算入库金额
  const productCode = row.productCode;
  const productList = formData.productList?.filter((item: any) => item.productCode === productCode);
  if (productList && productList.length > 0) {
    productList.forEach((item: any) => {
      item.unitPrice = row.unitPrice;
      calculateAmount(item);
    });
  }
}

const handleBack = async () => {
  await tagsViewStore.delView(route);
  await router.push({
    path: '/wms/quickWarehousing'
  })
 // 强制页面跳转
  // window.location.href = '/wms/quickWarehousing';
};

const selectedProduct = ref<any>({});
const selectedIndex = ref<number>(0);
// PMS采购单中有农资类【是否SKU=否】的商品时，该商品不需要入库，需要手动添加【是否SKU=是】的商品再入库
const handleAddProduct = (row: any, index: number) => {
  selectedProduct.value = row;
  selectedIndex.value = index;
  productDialog.visible = true;
  nextTick(() => {
    addGoodsRef.value.queryGoodList();
    addGoodsRef.value.queryManagerCategoryList();
  })
};

// 添加新商品：在去入库页面用于非sku商品添加商品
const onProductSubmit = (products: any[]) => {
  products.forEach((product: any) => {
    const existingIndex = formData.productList?.findIndex(
      (item: any) => item.productCode === product.productCode
    );

    if (existingIndex !== undefined && existingIndex >= 0) {
      // 如果商品已存在，更新数量
      if (formData.productList && formData.productList[existingIndex]) {
        formData.productList[existingIndex].actualInQty += product.actualInQty || 1;
      }
    } else {
      // 如果是新商品，添加到列表
      if (formData.productList) {
        const data = {
          parentId: selectedProduct.value.id, //  父级id,用户和父级产品id构建层级关系
          productTypeCustom: 'noSkuMain', // 商品类型
          notBeSubmitedProduct: true, // 新添加商品，未被提交:用户控制计划量和剩余量是否展示
          // 商品信息
          id: product.id,
          productCode: product.productCode,
          productName: product.productName,

          // productUnitName: product.productUnitName,
          // 商品分类
          firstCategoryId: product.firstCategoryId,
          firstCategoryName: product.firstCategoryName,
          secondCategoryId: product.secondCategoryId,
          secondCategoryName: product.secondCategoryName,
          thirdCategoryId: product.thirdCategoryId,
          thirdCategoryName: product.thirdCategoryName,

          fullCategoryName: product.fullCategoryName,
          // 规格
          productSpecs: product.productSpec,
          // 入库单价
          // unitPrice: null,
          // 入库量
          actualInQty: null,
          // 入库转换量
          actualInWeight: null,
          // 入库金额
          amount: null,
          // 入库库区
          warehouseAreaCode: null,
          // 商品包装
          productPackaging: null,
          isSku: product.isSku,
          isDiscreteUnit: product.isDiscreteUnit,
          pricingScheme: product.pricingScheme,
          conversionRelSecondUnitId: product.conversionRelSecondUnitId,
          conversionRelSecondUnitName: product.conversionRelSecondUnitName,
          productUnitId: product.productUnitId,
          productUnitName: product.productUnitName,
          costAmount: selectedProduct.value?.costAmount,
          costUnitPrice: selectedProduct.value?.costUnitPrice,
          unitPrice: selectedProduct.value?.costUnitPrice, // 农资类商品，将成本单价赋值给入库单价
          remark: selectedProduct.value?.remark,
          shippingAmount: null, // 运费
        }
        formData.productList.splice(selectedIndex.value + 1, 0, data);
      }
    }
  });
};

const handleAddRow = (index: number, row: any) => {
  if (formData.productList) {
    const insertData = {
      ...row,
      actualInQty: null, // 清空需要重新输入和计算的字段
      actualInWeight: null,
      amount: null,
      warehouseAreaCode: null,
      productPackaging: null,
      notBeSubmitedProduct: true, // 新添加商品，未被提交:用户控制计划量和剩余量是否展示
      productTypeCustom: 'child',
    }

    // 父级id,用户和父级产品id构建层级关系
    if (row.productTypeCustom === 'skuMain') {
      insertData.parentId = row.id; // 父级id,用户和父级产品id构建层级关系
    }
    if (row.productTypeCustom === 'noSkuMain') {
      insertData.parentId = row.parentId;
    }
    else {
      insertData.parentId = row.parentId;
    }
    formData.productList.splice(index + 1, 0, insertData);
  }
};

const handleDeleteRow = (index: number) => {
  const row = formData.productList?.[index];
  if (formData.productList && formData.productList.length > 0) {
    formData.productList.splice(index, 1);
  }

  // 判断同productCode的商品是否存在
  const productCode = row.productCode;
  const productList = formData.productList?.filter((item: any) => item.productCode === productCode);
  // 解决的问题：质检按钮只在主商品层级显示，如果删除了主商品，质检按钮就会消失，所以需要将parentId设置为0
  if (productList && productList.length > 0) {
    // parentId默认是主商品
    productList[0].parentId = "0";
  }
};

// 表格行合并方法
const handleSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  // 只对商品信息、商品分类、规格这三列进行合并
  if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3 || columnIndex === 4 || columnIndex === 5) {
    const currentProductCode = row.productCode;

    if (!currentProductCode) {
      return [1, 1];
    }

    // 计算连续相同productCode的行数
    let rowspan = 1;
    let startIndex = rowIndex;

    // 向前查找连续相同的productCode
    for (let i = rowIndex - 1; i >= 0; i--) {
      if (formData.productList[i].productCode === currentProductCode) {
        startIndex = i;
        rowspan++;
      } else {
        break;
      }
    }

    // 向后查找连续相同的productCode
    for (let i = rowIndex + 1; i < formData.productList.length; i++) {
      if (formData.productList[i].productCode === currentProductCode) {
        rowspan++;
      } else {
        break;
      }
    }

    // 如果当前行是第一个相同productCode的行，返回合并的行数
    if (rowIndex === startIndex) {
      return [rowspan, 1];
    } else {
      // 如果不是第一行，则隐藏该单元格
      return [0, 0];
    }
  }

  return [1, 1];
};
// 质检弹窗
const handleQualityCheck = (list: any, product: any, index: number) => {
  // 入库量和转换量取同商品productCode对应的字段之和
  const productCode = product.productCode;
  const actualInQty = list.filter((item: any) => item.productCode === productCode).reduce((sum: number, item: any) => sum + (parseFloat(item.actualInQty) || 0), 0);
  const actualInWeight = list.filter((item: any) => item.productCode === productCode).reduce((sum: number, item: any) => sum + (parseFloat(item.actualInWeight) || 0), 0);
  // 从已有的质检信息中找出productCode对应的质检信息
  const existingQuality = formData.qualityInspectionList?.find((item: any) => item.productCode === productCode);
  if (existingQuality) {
    qualityDialog.productData = {
      ...existingQuality,
      ...product,
      actualInQty,
      actualInWeight,
    }
  }
  else {
    qualityDialog.productData = {
      ...product, index, list, actualInQty, actualInWeight, detailList: [
        {
          specification: "",
          quantity: null,
          proportion: null,
          proportionDisplay: "",
        },
      ],
      deductionAmount: null, // 扣款金额
      deductionDesc: "", // 扣款说明
      deductionAttachment: "", // 扣款附件  
    };
  }
  qualityDialog.visible = true;
};

const handleSummaryMethod = (param: any) => {
  const { columns, data } = param;
  const sums: string[] = [];

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计';
    } else if (index === 7 || index === 8 || index === 9) {
      // 计算6、7、8列的和
      const values = data.filter((item: any) => item.productTypeCustom !== 'noSkuAncestor').map((item: any) => {
        if (index === 7) {
          return Number(item.actualInQty) || 0; // 入库量
        } else if (index === 8) {
          return Number(item.actualInWeight) || 0; // 入库转换量
        } else if (index === 9) {
          return Number(item.amount) || 0; // 入库金额
        }
        return 0;
      });

      if (values.every((value: number) => value === 0)) {
        sums[index] = '';
      } else {
        const sum = values.reduce((prev: number, curr: number) => {
          return prev + curr;
        }, 0);

        // 根据列类型格式化显示
        if (index === 7 || index === 8) {
          sums[index] = sum.toFixed(3);
        } else {
          sums[index] = sum.toFixed(2);
        }
      }
    } else {
      sums[index] = ' ';
    }
  });

  return sums;
}

const onQualityConfirm = (data: any) => {
  // 处理质检结果
  const { index, ...qualityData } = data;
  // 根据productCode判断是否存在，存在则更新，不存在则新增  
  const existingIndex = formData.qualityInspectionList?.findIndex((item: any) => item.productCode === qualityData.productCode);
  if (existingIndex !== undefined && existingIndex >= 0) {
    formData.qualityInspectionList[existingIndex] = qualityData;
  } else {
    formData.qualityInspectionList?.push(qualityData);
  }
  console.log("formData.qualityInspectionList---------", formData.qualityInspectionList);
};

/*const handleCancel = () => {
  router.back();
};*/

const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    submitting.value = true;
    const formDataCopy = { ...formData };
    formDataCopy.plannedDeliveryTime = new Date(formDataCopy.plannedDeliveryTime).getTime();
    formDataCopy.weighbridgeNoAttachment = JSON.stringify(formDataCopy.weighbridgeNoAttachment);
    await QuickWarehousingAPI.add(formDataCopy);
    ElMessage.success("提交成功");
    await handleBack();
  } catch (error) {
    console.error("提交失败:", error);
  } finally {
    submitting.value = false;
  }
};

// 获取基础数据
const getBaseData = async () => {
  try {
    // 获取仓库库区列表
    const areaData: any = await QuickWarehousingAPI.getWarehouseAreaList({status: 1});
    warehouseAreaList.value = areaData;

    // 获取业务员列表
    const businessPersonData: any = await QuickWarehousingAPI.querySalesPersonUser();
    businessPersonList.value = businessPersonData || [];

    // 获取供应商列表
    const supplierData: any = await QuickWarehousingAPI.getSupplierListAll({});
    supplierList.value = supplierData || [];
  } catch (error) {
    console.error("获取基础数据失败:", error);
  }
};

/* 姓名加密 */
function encryptName(name: any) {
  if (!name?.trim()) return "-";

  const firstChar = name[0]; // 获取第一位
  const shouldShowFourStars = name.length > 5;

  return shouldShowFourStars
    ? `${firstChar}****`
    : `${firstChar}${"*".repeat(name.length - 1)}`;
}

// 获取真实供应商联系人
function getSupplierRealName() {
  formData.supplierNameShow = true;
}

// 获取真实客户联系人
function getCustomerRealName() {
  formData.customerNameShow = true;
}

// 获取真实供应商联系电话
function getSupplierRealPhone() {
  WarehouseEntryNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      formData.mobile = data.mobile;
      formData.supplierMobilePhoneShow = false;
    })
    .finally(() => {});
}

// 获取真实客户联系电话
function getCustomerRealPhone() {
  WarehouseEntryNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      formData.customerMobile = data.customerMobile;
      formData.customerMobilePhoneShow = false;
    })
    .finally(() => {});
}

function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}

function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}

function encryptHanle (){ // 加密信息判断和处理
  // 加密状态初始化
  formData.supplierMobilePhoneShow = true;
  formData.customerMobilePhoneShow = true;

  // 供应商地址拼接和加密处理
  formData.supplierFullAddress = (formData.supplierCountryName && formData.supplierProvinceName && formData.supplierCityName && formData.supplierDistrictName && formData.supplierAddress)
    ? `${formData.supplierCountryName}${formData.supplierProvinceName}${formData.supplierCityName}${formData.supplierDistrictName}${formData.supplierAddress}`
    : filterAddress(formData, 'supplier');

  // 客户地址拼接和加密处理
  formData.customerFullAddress = (formData.customerCountryName && formData.customerProvinceName && formData.customerCityName && formData.customerDistrictName && formData.customerAddress)
    ? `${formData.customerCountryName}${formData.customerProvinceName}${formData.customerCityName}${formData.customerDistrictName}${formData.customerAddress}`
    : filterAddress(formData, 'customer');

  // 联系人加密处理
  if (formData.contactPerson && formData.contactPerson.length > 1) {
    formData.supplierNameShow = false;
  } else {
    formData.supplierNameShow = true;
  }

  if (formData.customerContactPerson && formData.customerContactPerson.length > 1) {
    formData.customerNameShow = false;
  } else {
    formData.customerNameShow = true;
  }

  // 供应商地址包含数字
  if (formData.supplierFullAddress && containsNumber(formData.supplierFullAddress)) {
    formData.supplierAddressShow = true;
    formData.supplierAddressFormat = replaceNumbersWithAsterisk(formData.supplierFullAddress);
  } else {
    formData.supplierAddressShow = false;
  }

  // 客户地址包含数字
  if (formData.customerFullAddress && containsNumber(formData.customerFullAddress)) {
    formData.customerAddressShow = true;
    formData.customerAddressFormat = replaceNumbersWithAsterisk(formData.customerFullAddress);
  } else {
    formData.customerAddressShow = false;
  }
}

const calcAttachmentLength = () => {
  if (!!formData.weighbridgeNoAttachment && typeof formData.weighbridgeNoAttachment === 'string') {
    return JSON.parse(formData.weighbridgeNoAttachment).length;
  }
  else if(Array.isArray(formData.weighbridgeNoAttachment)){
    return formData.weighbridgeNoAttachment.length;
  }
  return 0;
}
// 获取详情数据
const getDetailData = async () => {
  if (route.query.id) {
    try {
      const data: any = await QuickWarehousingAPI.queryDetail({ id: route.query.id });
      // 模拟农资类非sku商品
      // data.productList[0].isSku = 0;

      Object.assign(formData, data);
      formData.weighbridgeNoAttachment = !formData.weighbridgeNoAttachment ? "" : JSON.parse(data.weighbridgeNoAttachment);
      formData.qualityInspectionList = data.qualityInspectionList || [];

      encryptHanle();

      // 设置商品标志
      (formData.productList || []).forEach((item: any) => {
        if (item.isSku === 0) {
          item.productTypeCustom = 'noSkuAncestor';
        } else if (item.isSku === 1) {
          item.productTypeCustom = 'skuMain';
          item.parentSku = 1;
        }
      });
    } catch (error) {
      console.error("获取详情失败:", error);
    }
  }
};
/* 
商品明细按钮展示逻辑：
                质检按钮  +按钮     -按钮   添加商品
sku主商品           是       是       否       否      skuMain
sku子商品           否       是       是       否      child
非sku主商品1         是       是       是       否       child 第一个子商品
非sku主商品2         否       是       是       否       child 第二个子商品
非sku主商品          否       否       否       否      noSkuMain
非sku祖先商品         否       否       否       是      noSkuAncestor

主商品：parentId不存在或者"0"的商品
子商品：parentId存在且不为"0"的商品
子商品添加一个自定义列：isParentSku，值为parentId的商品的isSku
*/

onMounted(() => {
  getBaseData();
  getDetailData();
});
</script>

<style scoped lang="scss">
.align-center {
  align-items: center;
}

.title-label {
  display: flex;
  align-items: center;
  margin: 32px 0 20px 0;

  .title-line {
    width: 4px;
    height: 16px;
    background: var(--el-color-primary);
    margin-right: 8px;
  }

  .title-content {
    font-size: 16px;
    font-weight: 500;
    color: #151719;
  }

  .flex-1 {
    flex: 1;
  }

  .add-product-btn {
    margin-left: auto;
  }
}

.page-title {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7f3;
  font-size: 18px;
  font-weight: 500;
  color: #151719;

  .mr8px {
    margin-right: 8px;
  }
}

.fixed-footer {
  /* position: fixed;
  bottom: 0;
  right: 0;
  left: 0; */
  width: 100%;
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 16px 24px;
  text-align: right;
  z-index: 100;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-container {
  background: #ffffff;
  border-radius: 4px;
}

.page-content {
  padding: 0px 24px 0 24px;
}

.encryptBox {
  display: flex;
  align-items: center;
  
  .encryptBox-icon {
    margin-left: 8px;
    cursor: pointer;
  }
}
</style>
