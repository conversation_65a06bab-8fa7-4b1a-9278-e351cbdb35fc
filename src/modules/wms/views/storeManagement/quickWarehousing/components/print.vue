<template>
  <PrintNb ref="printRef">
    <div style="display: flex;justify-content: space-between;align-items: center;margin-top: 24px;margin-bottom: 14px">
      <div>编号：{{ printData.receiptNoticeCode }}</div>
      <div class="box-title" style="max-width: 30%;word-break: break-all;">
        {{ printData.title }}
      </div>
      <div class="barcode" >
        <Barcode :value="printData.receiptNoticeCode" :options="barcodeOptions" />
      </div>
    </div>
    <!-- 产品下车信息登记 -->
    <section v-for="(item, index) in printData.entryOrderList" :key="index">
      <div>
        <div>
          <div class="box-title-2">
            产品下车信息登记
          </div>
        </div>
      </div>
      <div>
        <div style="display: flex;justify-content: space-around;align-items: center;margin-top: 10px;">
          <!-- 入库单号 -->
          <div style="flex: 1;">
            <span>入库单号：</span>
            <span style="display: inline-block;word-break: break-all;">{{ item.entryOrderCode }}</span>
          </div>
          
          <!-- 送达时间 -->
          <div style="flex: 1;">
            <span>送达时间：</span>
            <span> {{ parseTime(item.entryTime, "{y}-{m}-{d}") }}</span>
          </div>
        </div>
        <div style="display: flex;justify-content: space-around;align-items: center;margin-top: 10px;">
          <!-- 单位名称 -->
          <div style="flex: 1;">
            <span>单位名称：</span>
            <span> {{ printData.supplierName || "" }}</span>
          </div>
          <!-- 车号 -->
          <div style="flex: 1;">
            <span>车号：</span>
            <span style="display: inline-block;width: 100px;">{{ item.vehicleNo }}</span>
          </div>
        </div>
      </div>
      <table class="print-table">
        <thead>
          <tr>
            <th style="width: 100px">产品大类名称</th>
            <th style="width: 120px">商品名称</th>
            <th>总框数</th>
            <th>检称重量</th>
            <th>检称框数</th>
            <th>数量</th>
            <th>单价(元/斤)</th>
            <th>金额</th>
            <th>备注</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(product, index) in item.productList" :key="index">
            <td>{{ product.thirdCategoryName }}</td>
            <td>
              <div>
                <div>
                  <Barcode :value="product.productCode" :options="productCodeBarcodeOptions" />
                </div>
                <div style="text-align: center;">{{ product.productName }}</div>
              </div>
            </td>
            <td></td>
            <td></td>
            <td></td>
            <td>{{ product.productActualQty }}</td>
            <td>{{ truncateDecimals(product.unitPrice) }}</td>
            <td>{{ (product.amount) }}</td>
            <td>{{ product.remark }}</td>
          </tr>
          <tr>
            <td>合计</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td>{{ calculateTotalQuantity(item.productList) }}</td>
            <td></td>
            <td>{{ calculateTotalAmount(item.productList).toFixed(2) }}</td>
            <td></td>
          </tr>
          <tr>
            <td>接收人员签字</td>
            <td colspan="4"></td>
            <td>司机签字</td>
            <td colspan="3"></td>
          </tr>
        </tbody>
      </table>
      <div>
        <div>
          <div class="box-title-2" style="margin-top: 10px;">
            产品质检信息登记
          </div>
        </div>
      </div>
      <!-- 产品质检信息登记 -->
      <section>
        <div>
          <div style="display: flex;justify-content: space-between;align-items: center;margin-top: 10px;">
            <!-- 以上信息复核意见： -->
            <div>
              <span>以上信息复核意见：</span>
              <span></span>
            </div>
            <!-- 质检时间： -->
            <div>
              <span>质检时间：{{ parseTime(item.inspectionTime, "{y}-{m}-{d}") }}</span>
              <span></span>
            </div>
            <!-- 质检员签字： -->
            <div>
              <span>质检员签字：</span>
              <span style="display: inline-block;width: 100px;"></span>
            </div>
          </div>
        </div>
        <table class="print-table">
          <thead>
            <tr>
              <th style="width: 100px">商品名称</th>
              <th style="width: 100px">处罚金额(元)</th>
              <th>规格</th>
              <th>数量</th>
              <th>占比</th>
            </tr>
          </thead>
          <tbody>

            <section v-for="(checkItem, index) in item.qualityInspectionList" :key="index">
              <tr v-for="(detailItem, detailIndex) in checkItem.detailList" :key="detailIndex">
                <td v-if="shouldShowProductColumn(item.qualityInspectionList, checkItem.productCode, index, detailIndex)" 
                    :rowspan="getProductRowspan(item.qualityInspectionList, checkItem.productCode)">
                  {{ checkItem.productName }}
                </td>
                <td v-if="shouldShowProductColumn(item.qualityInspectionList, checkItem.productCode, index, detailIndex)" 
                    :rowspan="getProductRowspan(item.qualityInspectionList, checkItem.productCode)">
                  {{ (checkItem.deductionAmount) }}
                </td>
                <td>
                  {{ detailItem.specification }}
                </td>
                <td>
                  {{ detailItem.quantity }}
                </td>
                <td>
                  {{ detailItem.proportion }}
                </td>
              </tr>
            </section>
            <tr>
              <td>合计</td>
              <td>{{ calculateTotalQuantity(item.qualityInspectionList || [], 'deductionAmount') || ''}} </td>
              <td></td>
              <td>{{ calculateTotalQuantityInspection(item.qualityInspectionList || [], 'quantity') || ''}}</td>
              <td></td>
            </tr>
            <tr>
              <td>代收费标准</td>
              <td></td>
              <td>代收费金额</td>
              <td colspan="2"></td>
            </tr>
          </tbody>
        </table>
        <div style="display: flex;justify-content: space-between;align-items: center;margin-top: 10px;">
          <div class="">开票人签字：</div>
          <div class=""></div>
          <div class="">冷库负责人签字：</div>
          <div class=""></div>
        </div>
      </section>
    </section>

  </PrintNb>
</template>

<script setup lang="ts">
import { ref } from "vue";
// import PrintTemplate from "@/core/components/Print/PrintTemplate.vue";
// import NewPrint from "@/core/components/NewPrint/index.vue";
import PrintNb from "@/core/components/Print/PrintNb.vue";
const { t } = useI18n();
import { parseTime, changeDateRange } from "@/core/utils/index.js";
const barcodeOptions = ref({
  format: "CODE128", // 条形码格式
  width: 1, // 条形码宽度
  height: 40, // 条形码高度
  displayValue: true, // 是否显示条形码下方的文本
  fontSize: 12, // 设置文字大小为12px
  fontOptions: "bold", // 可以设置字体的粗细等
});
const productCodeBarcodeOptions = ref({
  format: "CODE128", // 条形码格式
  width: 1, // 条形码宽度
  height: 22, // 条形码高度
  displayValue: false, // 是否显示条形码下方的文本
});

const printData = ref<any>({});
const printRef = ref<InstanceType<typeof PrintNb>>();
// 暴露打印方法给父组件
const handlePrint = (data: any) => {
  printData.value = data;
  nextTick(() => {
    printRef.value?.onPrint();
  });
};
// 截断小数到指定位数，不进行四舍五入
const truncateDecimals = (value: number | string, decimals: number = 2) => {
  if (value === null || value === undefined || value === '') return '0.00';
  const num = Number(value);
  if (isNaN(num)) return '0.00';
  const factor = Math.pow(10, decimals);
  return (Math.floor(num * factor) / factor).toFixed(decimals);
};

const calculateTotalQuantity = (productList: any[], key: string = 'productActualQty') => {
  return productList.reduce((total, product) => total + Number(product[key]), 0);
};

const calculateTotalAmount = (productList: any[]) => {
  return productList.reduce((total, product) => total + Number(product.amount), 0);
};

const calculateTotalQuantityInspection = (qualityInspectionList: any[], key: string = 'quantity') => {
  let total = 0;
  for(let item of qualityInspectionList){
    for(let detail of item.detailList){
      total += Number(detail[key]);
    }
  }
  return total;
};

// 判断是否应该显示商品列（第一列和第二列）
const shouldShowProductColumn = (qualityInspectionList: any[], productCode: string, checkIndex: number, detailIndex: number) => {
  // 如果是第一个明细项，则显示
  if (detailIndex === 0) {
    // 检查之前是否有相同的 productCode
    for (let i = 0; i < checkIndex; i++) {
      if (qualityInspectionList[i].productCode === productCode) {
        return false; // 之前已经有相同的 productCode，不显示
      }
    }
    return true; // 第一次出现该 productCode，显示
  }
  return false; // 不是第一个明细项，不显示
};

// 获取商品行跨度
const getProductRowspan = (qualityInspectionList: any[], productCode: string) => {
  let totalRows = 0;
  for (let item of qualityInspectionList) {
    if (item.productCode === productCode) {
      totalRows += item.detailList ? item.detailList.length : 0;
    }
  }
  return totalRows;
};

defineExpose({
  handlePrint,
});
</script>

<style scoped lang="scss">
.box-title {
  color: #000000;
  text-align: center !important;
  /*margin-top: 24px;
    margin-bottom: 14px;*/
  font-weight: 600 !important;
  font-size: 18px !important;
}
.box-title-2{
  color: #000000;
  text-align: center !important;
  /*margin-top: 24px;
    margin-bottom: 14px;*/
  font-weight: 600 !important;
  font-size: 14px !important;
}

.barcode {
  text-align: center;
  /*margin-bottom: 16px;*/
}

.grad-row-print {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;

  .el-form-item--div {
    flex-basis: calc(33% - 10px);
    display: flex;
  }

  .el-form-item__label {
    color: #000000;
    font-weight: 500;
    font-size: 12px;
    font-style: normal;
    width: 100px;
  }

  .el-form-item__content {
    color: #000000;
    font-weight: 500;
    font-size: 12px;
    word-break: break-word;
  }
}

.print-info {
  margin-top: 10px;

  .info-row {
    display: flex;
    margin-bottom: 8px;
    flex-wrap: wrap;
    page-break-inside: avoid;

    .info-item-4 {
      width: 25% !important;
      display: flex;
      margin-bottom: 5px;

      .info-label {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 12px;
        color: #000000 !important;
        line-height: 26px;
        text-align: left;
        font-style: normal;
      }

      .info-value {
        flex: 1;
        padding-right: 8px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 12px;
        color: #000000 !important;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        word-break: break-word;
      }
    }
  }
}

.print-table {
  margin-top: 10px;
  border: 1px solid #000000 !important;
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
  -webkit-print-color-adjust: exact;
}


table,
th,
td {
  color: #000000;
  border: 1px solid #000000;

  padding: 5px;
}

th,
td {
  // padding: 14px 12px;
  word-break: break-word;
  font-size: 12px;
  font-weight: 500;
}
</style>
