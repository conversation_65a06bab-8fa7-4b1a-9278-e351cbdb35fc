<template>
  <div class="app-container">
    <div class="addPage">
      <div class="page-title">
        <div class="purchase-title">
          <div @click="handleCancel" class="cursor-pointer mr8px">
            <el-icon><Back /></el-icon>
          </div>
          <div>
            {{ t("warehouseEntryNotice.label.receivingOrderCode") }}：{{
            route.query.receivingOrderCode
            }}
          </div>
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="145px"
          label-position="right"
        >
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("warehouseEntryNotice.label.basicInformation") }}
            </div>
          </div>
          <div class="grad-row">
            <el-row>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.receiptNoticeCode')"
                >
                  {{ form.receiptNoticeCode ? form.receiptNoticeCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.sourceOrderCode')"
                >
                  {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.plannedDeliveryTime')"
                >
                  <span v-if="form.plannedDeliveryTime">
                    {{
                      parseTime(
                        form.plannedDeliveryTime,
                        "{y}-{m}-{d} {h}:{i}:{s}"
                      )
                    }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.receiptType')"
                >
                  <span v-if="form.receiptType == 1">
                    {{ t("warehouseEntryNotice.label.purchaseInventory") }}
                  </span>
                  <span v-else-if="form.receiptType == 2">
                    {{ t("warehouseEntryNotice.label.returnStorage") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.purchaseSalesPerson')"
                >
                  {{
                  form.purchaseSalesPerson ? form.purchaseSalesPerson : "-"
                  }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <span v-if="form.receiptType == 2">
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.customerName')"
                  >
                    {{ form.customerName ? form.customerName : "-" }}
                  </el-form-item>
                </span>
                <span v-else>
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.supplierName')"
                  >
                    {{ form.supplierName ? form.supplierName : "-" }}
                  </el-form-item>
                </span>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.productTotalPlanQty')"
                >
                  {{ form.expectedQty ? form.expectedQty : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.productTotalPlanWeight')"
                >
                  {{ form.expectedWeight ? form.expectedWeight : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item :label="$t('warehouseEntryNotice.label.receiptNoticeRemark')">
                  {{form.receiptNoticeRemark ? form.receiptNoticeRemark : "-"}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('warehouseEntryNotice.label.isSorting')" prop="isSorting" :rules="[{required:true,message:t('warehouseEntryNotice.rules.isSorting'),trigger: 'blur'}]">
                  <el-select v-model="form.isSorting">
                    <el-option v-for="(item,index) in isSortingList" :key="index" :value="item.key" :label="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('warehouseEntryNotice.label.receivingRemark')" prop="goodsRemark">
                  <el-input  v-model="form.goodsRemark" :placeholder="$t('common.placeholder.inputTips')" clearable type="textarea" maxlength="200" show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              {{ $t("warehouseEntryNotice.label.goodsReceiving") }}
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 商品列表 -->
                <el-form-item label-width="0" prop="warehouseIds">
                  <el-table
                    :data="form.productList"
                    v-loading="formLoading"
                    stripe
                    highlight-current-row
                  >
                    <el-table-column
                      type="index"
                      :label="$t('common.sort')"
                      width="60"
                      align="center"
                    />
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.goodsInfor')"
                      min-width="150"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        <div class="product-code">
                          {{ $t("warehouseEntryNotice.label.productCode") }}：
                          {{ scope.row.productCode }}
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </template>
                    </el-table-column>
                    <!-- 规格 -->
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.productSpecs')"
                      prop="productSpecs"
                      show-overflow-tooltip
                    />
                    <!-- 计划数量 -->
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.plannedQuantity')"
                      prop="productExpectQty"
                    />
                    <!-- 收运数量 -->
                    <el-table-column
                      prop="productActualQty"
                      min-width="138"
                      :label="
                        $t('warehouseEntryNotice.label.receivedQty')
                      "
                    >
                      <template #default="scope">
                        <el-form-item
                          class="mt15px"
                          label-width="0px"
                          :prop="
                            'productList.' + scope.$index + '.productActualQty'
                          "
                          :rules="scope.row.productActualQty ? [
                            { required: true, validator: validateProductActualQty, trigger: ['blur','change'] },
                            {
                              pattern:
                                /(^[1-9](\d{0,3})$)/,
                              message: t(
                                'warehouseEntryNotice.rules.productActualQtyFomart'
                              ),
                              trigger: ['blur', 'change'],
                            },
                          ] : []"
                        >
                          <el-input
                            v-model="scope.row.productActualQty"
                            :placeholder="$t('common.placeholder.inputTips')"
                            clearable
                          >
                            <template #append>
                              {{ scope.row.productUnitName }}
                            </template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 计划重量 -->
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.productPlanWeightCopy')"
                      prop="expectedWeight"
                    />
                    <!-- 收运重量 -->
                    <el-table-column
                      prop="receivedWeight"
                      min-width="138"
                      :label="
                        $t('warehouseEntryNotice.label.actualWeightCopy')
                      "
                    >
                      <template #default="scope">
                        <el-form-item
                          class="mt15px"
                          label-width="0px"
                          :prop="
                            'productList.' + scope.$index + '.receivedWeight'
                          "
                          :rules="scope.row.receivedWeight ? [
                            {
                              required: true,
                              pattern:
                                /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                              message: t(
                                'warehouseEntryNotice.rules.productActualWeightFomart'
                              ),
                              trigger: ['blur', 'change'],
                            },
                          ]: []"
                        >
                          <el-input
                            v-model="scope.row.receivedWeight"
                            :placeholder="$t('common.placeholder.inputTips')"
                            clearable
                          >
                            <template #append>
                              kg
                            </template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')">
                      <template #default="scope">
                        <div v-if="scope.row.imagesNum">
                          <el-badge
                            :value="scope.row.imagesNum"
                            :offset="[10, 8]"
                            class="item"
                            type="primary"
                          >
                            <!-- 上传 -->
                            <el-button
                              type="primary"
                              link
                              @click="handleUpload(scope.row, scope.$index)"
                            >
                              {{ $t("warehouseEntryNotice.button.upload") }}
                            </el-button>
                          </el-badge>
                        </div>
                        <div v-else>
                          <!-- 上传 -->
                          <el-button
                            type="primary"
                            link
                            @click="handleUpload(scope.row, scope.$index)"
                          >
                            {{ $t("warehouseEntryNotice.button.upload") }}
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("warehouseEntryNotice.button.cancel") }}
          </el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            {{ $t("warehouseEntryNotice.button.confirmBtn") }}
          </el-button>
        </div>
      </div>
    </div>
    <!--    上传-->
    <UploadDialog
      v-model:visible="uploadDialog.visible"
      ref="uploadDialogRef"
      @on-submit="onSubmitUpload"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "ToReceivingOrders",
  inheritAttrs: false,
});
import shippingReceiptAPI, {
  addFormData,
} from "@/modules/wms/api/shippingReceipt";
import warehouseEntryNoticeAPI from "@/modules/wms/api/warehouseEntryNotice";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
import { parseTime } from "@/core/utils/index.js";
import UploadDialog from "./components/uploadDialog.vue";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const formRef = ref(ElForm);
const loading = ref(false);
const formLoading = ref(false);
const tableIndex = ref();
const isSortingList = ref([
  {
    key: 0,
    value: t("warehouseEntryNotice.label.not"),
  },
  {
    key: 1,
    value: t("warehouseEntryNotice.label.yes"),
  },

]);

const form = reactive<addFormData>({
  productList: [],
});

const uploadDialog = reactive({
  visible: false,
});
const uploadDialogRef = ref();

const rules = reactive({});

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.push({
    path: "/wms/storeManagement/shippingReceipt",
  });
};

// 上传
function handleUpload(row: any, index: any) {
  tableIndex.value = index;
  uploadDialog.visible = true;
  uploadDialogRef.value.setEditType("add");
  if (row.imagesUrls) {
    uploadDialogRef.value.setFormData(JSON.parse(row.imagesUrls));
  }
}

//上传提交
function onSubmitUpload(data: any) {
  if (data) {
    form.productList[tableIndex.value].imagesUrls = JSON.stringify(data);
    form.productList[tableIndex.value].imagesNum = data.length;
  }
}
function validateProductActualQty(rule, value, callback) {
  let index = rule.field?.split('.')[1]
  let productExpectQty = form.productList[index].productExpectQty?form.productList[index].productExpectQty:0
  if(value && productExpectQty){
    if (value > parseInt(productExpectQty)) {
      callback(new Error(t('warehouseEntryNotice.message.productActualQtyTips1')));
    } else {
      callback();
    }
  }
}
// 提交
function handleSubmit() {
  if (form.productList && form.productList.length == 0) {
    return ElMessage.error(
      t("warehouseEntryNotice.message.addOrEditGoodsTips")
    );
  }
  // 最少有一行收运数量大于0
  const sum = computed(() => {
    return form.productList.reduce((total: number, obj: any) => {
      return total + (obj.productActualQty || 0); // 如果元素不存在，默认为 0
    }, 0);
  });
  if (sum.value <= 0) {
    return ElMessage.error(
      t("warehouseEntryNotice.message.productActualQtyTips")
    );
  }
  let arr = form.productList.filter(function (list) {
    return (list.productActualQty && !list.receivedWeight) || (!list.productActualQty && list.receivedWeight)
  })
  if(arr.length > 0){
    return ElMessage.error(
      t("warehouseEntryNotice.message.qtyAndWeightTip")
    );
  }
  formRef.value.validate((valid: any) => {
    if (!valid) return;

    ElMessageBox.confirm(
      `${t("warehouseEntryNotice.message.shippingReceiptTips")}`,
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
      }
    ).then(
      () => {
        loading.value = true;
        let params = {
          id: route.query.id,
          isSorting:form.isSorting,
          receiptNoticeCode:form.receiptNoticeCode,
          receivingOrderCode:route.query.receivingOrderCode,
          remark:form.goodsRemark,
          productList:form.productList
        }
        shippingReceiptAPI.submitReceiving(params).then((res)=>{
          ElMessage.success(
            t("warehouseEntryNotice.message.shippingReceiptSucess")
          );
          loading.value = false;
          handleCancel();
        }).finally(() => {
          loading.value = false;
        })
      },
      () => {
        ElMessage.info(t("warehouseEntryNotice.message.cancelTip"));
      }
    );
  });
}

/** 查询采购单详情 */
function queryDetail() {
  loading.value = true;
  let params = {
    id: route.query.id,
  };
  shippingReceiptAPI
    .queryDetail(params)
    .then((data: any) => {
      Object.assign(form, data);
      nextTick(()=>{
        formRef.value?.clearValidate()
      })
    })
    .finally(() => {
      loading.value = false;
    });
}

onMounted(() => {
  queryDetail()
});
</script>
<style scoped lang="scss">
.addPage {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 13px 21px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 30px 24px 30px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 9px 20px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .mt15px {
    margin-bottom: 15px !important;
  }
}
</style>
<style scoped>
::v-deep .custom-select .el-select__wrapper {
  background: #f8f9fc !important;
}
</style>
