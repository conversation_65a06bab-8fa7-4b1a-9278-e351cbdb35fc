# 应用端口
VITE_APP_PORT = 3000

# 代理前缀
VITE_APP_BASE_API = '/api'

# 线上接口地址
# 开发环境
VITE_APP_API_URL = http://**************:9100
# sit环境
# VITE_APP_API_URL = http://**************:9100
# 开发接口地址
# VITE_APP_API_URL = http://localhost:8989

# 是否启用 Mock 服务
VITE_MOCK_DEV_SERVER = false

# APP_ID
VITE_APP_ID = '1871121364401471491'

# LOGIN_TYPE
VITE_LOGIN_TYPE = 'username'

#  自定义域名code码
VITE_APP_CUSTOM_URL_CODE= 'supplyUrlCode'
